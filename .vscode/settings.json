{"typescript.tsdk": "./node_modules/typescript/lib", "npm.packageManager": "pnpm", "editor.tabSize": 2, "editor.formatOnSave": true, "editor.defaultFormatter": "esbenp.prettier-vscode", "editor.quickSuggestions": {"other": true, "comments": true, "strings": true}, "editor.codeActionsOnSave": {"source.fixAll": "explicit", "source.fixAll.eslint": "explicit", "source.fixAll.stylelint": "explicit"}, "files.eol": "\n", "search.exclude": {"**/node_modules": true, "**/*.log": true, "**/*.log*": true, "**/bower_components": true, "**/dist": true, "**/elehukouben": true, "**/.git": true, "**/.gitignore": true, "**/.svn": true, "**/.DS_Store": true, "**/.idea": true, "**/.vscode": false, "**/yarn.lock": true, "**/tmp": true, "out": true, "dist": true, "node_modules": true, "CHANGELOG.md": true, "examples": true, "res": true, "screenshots": true, "yarn-error.log": true, "**/.yarn": true}, "files.exclude": {"**/.cache": true, "**/.editorconfig": true, "**/.eslintcache": true, "**/bower_components": true, "**/.idea": true, "**/tmp": true, "**/.git": true, "**/.svn": true, "**/.hg": true, "**/CVS": true, "**/.DS_Store": true}, "files.watcherExclude": {"**/.git/objects/**": true, "**/.git/subtree-cache/**": true, "**/.vscode/**": true, "**/node_modules/**": true, "**/tmp/**": true, "**/bower_components/**": true, "**/dist/**": true, "**/yarn.lock": true}, "i18n-ally.keystyle": "nested", "i18n-ally.sortKeys": true, "i18n-ally.namespace": false, "i18n-ally.pathMatcher": "{namespaces}/{locale}.{ext}", "i18n-ally.enabledParsers": ["ts"], "i18n-ally.sourceLanguage": "en", "i18n-ally.displayLanguage": "zh-CN", "i18n-ally.enabledFrameworks": ["vue", "react"], "i18n-ally.localesPaths": ["src/lang"], "scss.lint.unknownAtRules": "ignore", "cSpell.words": ["Actionid", "Actionstatus", "Actionunit", "Acupoint", "Acupoints", "adcode", "Adcodes", "Addresss", "Aftersale", "Aftersales", "aipowered", "amap", "apifox", "Attributify", "Autosize", "Backtop", "Biao", "bodypredict", "bomm", "breakline", "browserify", "<PERSON><PERSON>nst<PERSON>", "Bule", "<PERSON><PERSON><PERSON>", "Carouselts", "cascader", "catelog", "Ccto", "<PERSON>", "citycode", "Classifys", "codegen", "Consum", "Creater", "daterange", "datetimerange", "Deleter", "Depts", "Disea", "<PERSON><PERSON><PERSON>", "DTO", "Dtos", "DXQJDZDZ", "DXQJDZXZ", "echarts", "Enble", "exceljs", "Excute", "Execut", "Expresss", "FIVC", "flac", "Footscreen", "frequence", "geocodes", "Geolocation", "georoam", "gitee", "Hwkj", "hxunion", "icbc", "Icds", "iconify", "ikid", "Ikidcare", "infocode", "Instru", "<PERSON><PERSON><PERSON>", "jspdf", "<PERSON>", "kangfx", "LYSLZDZ", "LYSLZXZ", "<PERSON><PERSON>", "Mogo", "noredirect", "Nums", "Obyx", "onevent", "<PERSON><PERSON>", "Pageable", "Paitent", "<PERSON><PERSON><PERSON><PERSON>", "Pinia", "pload", "popconfirm", "Prescripton", "Progres", "Prolem", "<PERSON><PERSON><PERSON>", "Qingpai", "qrcode", "<PERSON><PERSON>", "redash", "<PERSON><PERSON><PERSON>", "Relateds", "Rlation", "satisficing", "<PERSON><PERSON>", "Scopeable", "segmentit", "<PERSON><PERSON>", "<PERSON>", "Signalr", "snmb", "sortablejs", "Sstability", "stompjs", "subcomponents", "Taotal", "templet", "<PERSON><PERSON><PERSON>", "Triggerable", "Unkown", "unocss", "unplugin", "VITE", "vnode", "vnodes", "vueuse", "wangeditor", "wechat", "Xeek", "y<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "yonghu", "yxxx", "<PERSON><PERSON>", "zlevel", "Zlxg"], "[vue]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[markdown]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}}