{"Spine Mission Management Page": {"scope": "vue", "prefix": "Vue3.7+TreeTableLayout", "body": ["<template>", "  <el-container class=\"w-full h-full\">", "    <!-- 左侧目录 -->", "    <el-aside width=\"230px\" class=\"p-10px\">", "      <EditTree", "        ref=\"treeRef\"", "        :data=\"treeList\"", "        :props=\"defaultTreeProps\"", "        node-key=\"Id\"", "        :current-node-key=\"queryParams.Type\"", "        :operations=\"getTreeNodeOperations\"", "        @add=\"(e) => onAddOrEditTreeNode(e, true)\"", "        @edit=\"(e) => onAddOrEditTreeNode(e, false)\"", "        @delete=\"(e) => onDeleteTreeNode(e)\"", "        @node-click=\"onTreeClick\"", "        @node-contextmenu=\"onNodeRightClick\"", "      />", "    </el-aside>", "    <el-main class=\"p-10px!\">", "      <BaseTableSearchContainer @size-changed=\"tableResize\">", "        <!-- 顶部筛选条件 -->", "        <template #search>", "          <TBSearchContainer :is-show-toggle=\"true\">", "            <template #left>", "              <el-form :model=\"queryParams\" label-position=\"right\" :inline=\"true\">", "                <el-form-item label=\"关键字\">", "                  <el-input", "                    v-model=\"queryParams.keyword\"", "                    placeholder=\"请输入\"", "                    clearable", "                    @keyup.enter=\"handleQuery\"", "                  />", "                </el-form-item>", "              </el-form>", "            </template>", "            <template #right>", "              <el-button type=\"primary\" icon=\"search\" @click=\"handleQuery\">搜索</el-button>", "              <el-button type=\"primary\" @click=\"onAddItem\">新增</el-button>", "            </template>", "          </TBSearchContainer>", "        </template>", "        <!-- 列表 -->", "        <template #table>", "          <el-table", "            :ref=\"kTableRef\"", "            v-loading=\"tableLoading\"", "            :data=\"pageData\"", "            :total=\"total\"", "            row-key=\"Id\"", "            :height=\"tableFluidHeight\"", "            :header-cell-style=\"{ textAlign: 'center' }\"", "            :cell-style=\"{ textAlign: 'center' }\"", "            border", "            highlight-current-row", "          >", "            <!-- 可选项框 -->", "            <el-table-column type=\"selection\" width=\"55\" />", "            <!-- 自定义项 -->", "            <el-table-column prop=\"Key\" label=\"自定义项\">", "              <template #default=\"scope\">", "                <!-- 最多2行 -->", "                <span class=\"line-clamp-2\">{{ scope.row.Key }}</span>", "              </template>", "            </el-table-column>", "            <!-- 操作 -->", "            <el-table-column fixed=\"right\" label=\"操作\" width=\"150\">", "              <template #default=\"scope\">", "                <el-button link type=\"primary\" @click=\"onPreviewOrEdit(scope.row, true)\">", "                  查看", "                </el-button>", "                <el-button", "                  v-hasNoPermission=\"['promoter']\"", "                  link", "                  type=\"primary\"", "                  @click=\"onPreviewOrEdit(scope.row, false)\"", "                >", "                  编辑", "                </el-button>", "              </template>", "            </el-table-column>", "          </el-table>", "        </template>", "        <!-- 分页 -->", "        <template #pagination>", "          <Pagination", "            v-if=\"total > 0\"", "            v-model:total=\"total\"", "            v-model:page=\"queryParams.PageIndex\"", "            v-model:limit=\"queryParams.PageSize\"", "            @pagination=\"requestTableList\"", "          />", "        </template>", "      </BaseTableSearchContainer>", "    </el-main>", "  </el-container>", "</template>", "", "<script setup lang=\"ts\">", "import { useTableConfig } from \"@/hooks/useTableConfig\";", "import useOrgDialog from \"@/hooks/useOrgDialog\";", "", "/** 调试开关 */", "const kEnableDebug = true;", "defineOptions({", "  name: \"\",", "});", "", "const {", "  kTableRef,", "  tableRef,", "  pageData,", "  tableLoading,", "  tableFluidHeight,", "  total,", "  tableResize,", "  handleSelectionChange,", "  selectedTableIds,", "  tableDateFormat,", "} = useTableConfig<any>();", "/** 查询条件 */", "const queryParams = reactive<any>({});", "/** 点击搜索 */", "function handleQuery() {", "  queryParams.PageIndex = 1;", "  requestTableList();", "}", "", "/** 查看/添加/编辑弹窗 */", "const showDataDialog = reactive({", "  isShow: false,", "  title: \"\",", "  disabled: false,", "  data: {} as any, // 查看/添加/编辑详情", "});", "", "/** 点击添加 */", "function onAddItem() {", "  kEnableDebug && console.debug(\"点击添加\");", "  showDataDialog.title = \"新增\";", "  showDataDialog.disabled = false;", "  showDataDialog.data = {};", "  showDataDialog.isShow = true;", "}", "", "/** 点击查看/编辑 */", "async function onPreviewOrEdit(row: any, disabled: boolean = false) {", "  kEnableDebug && console.debug(\"查看/编辑\", row, disabled);", "  showDataDialog.title = disabled ? \"查看\" : \"编辑\";", "  showDataDialog.disabled = disabled;", "  showDataDialog.data = row;", "  showDataDialog.isShow = true;", "}", "/** 请求列表数据 */", "async function requestTableList() {", "  tableLoading.value = true;", "  tableLoading.value = false;", "}", "", "/** 编辑/添加左侧树节点弹窗 */", "const showTreeNodeDialog = reactive({", "  isShow: false,", "  title: \"\",", "  parent: {} as any,", "  data: {} as any,", "});", "", "/** 树实例 */", "const treeRef = useTemplateRef(\"treeRef\");", "", "/** 左侧树列表数据结构 */", "const treeList = ref<any[]>([]);", "const defaultTreeProps = reactive({", "  children: \"Children\",", "  label: \"Name\",", "});", "", "/** 树点击事件 */", "async function onTreeClick(data: any) {", "  kEnableDebug && console.debug(\"树点击事件\", data);", "  queryParams.RecoveryMissionType = data.Id;", "  queryParams.PageIndex = 1;", "", "  requestTableList();", "}", "", "/** 树右键点击事件 */", "function onNodeRightClick(data: any) {", "  kEnableDebug && console.debug(\"树右键点击事件\", data);", "  showTreeNodeDialog.data = data;", "}", "", "/** 获取树节点右键操作 */", "function getTreeNodeOperations(data: any) {", "  if (data.Depth === 2) {", "    return {", "      add: data.Enable,", "    };", "  } else if (data.Depth && data.Depth > 6) {", "    return {", "      edit: true,", "      delete: true,", "    };", "  } else if (data.Depth && data.Depth !== 1) {", "    return {", "      add: data.Enable,", "      edit: true,", "      delete: true,", "    };", "  }", "", "  return {};", "}", "", "/** 点击删除左侧树数据 */", "async function onDeleteTreeNode(data: any) {", "  kEnableDebug && console.debug(\"点击删除左侧树数据\");", "}", "", "/** 点击添加/编辑左侧树数据 */", "function onAddOrEditTreeNode(data: any, add: boolean) {", "  kEnableDebug && console.debug(\"点击添加/编辑左侧树数据\", add, data);", "", "  if (add) {", "    showTreeNodeDialog.parent = data;", "    showTreeNodeDialog.data = {};", "    showTreeNodeDialog.title = \"添加类别\";", "    showTreeNodeDialog.isShow = true;", "    return;", "  }", "", "  if (!data.ParentId) {", "    ElMessage.error(\"上级分类ID为空\");", "    return;", "  }", "", "  const parentType = findTreeNodeById(treeList.value, data.ParentId);", "  kEnableDebug && console.debug(\"查询上级分类\", parentType);", "  if (!parentType) {", "    ElMessage.warning(\"未找到上级分类\");", "    return;", "  }", "", "  showTreeNodeDialog.parent = parentType;", "  showTreeNodeDialog.data = data;", "  showTreeNodeDialog.title = \"编辑类别\";", "  showTreeNodeDialog.isShow = true;", "}", "/** 递归查询当前类别 */", "function findTreeNodeById(tree: any[], targetId: string): any | undefined {", "  // 遍历树中的每个节点", "  for (const node of tree) {", "    // 如果当前节点的 Id 匹配目标 Id，返回该节点", "    if (node.Id === targetId) {", "      return node;", "    }", "", "    // 如果当前节点有子节点，递归查找子节点", "    if (node.Children && node.Children.length > 0) {", "      const foundNode = findTreeNodeById(node.Children, targetId);", "      if (foundNode) {", "        return foundNode; // 如果在子节点中找到，直接返回", "      }", "    }", "  }", "", "  // 如果未找到匹配的节点，返回 undefined", "  return undefined;", "}", "", "onBeforeMount(() => {", "  requestTreeData();", "});", "/** 请求左侧树列表数据 */", "const requestTreeData = async () => {};", "", "onActivated(() => {", "  requestTableList();", "});", "</script>", "", "<style lang=\"scss\" scoped></style>"], "description": "脊柱任务管理页面模板 - 包含左侧树形菜单和右侧表格的完整后台管理页面组件"}}