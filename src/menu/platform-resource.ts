import { type RouteVO } from "@/api/system/menu";

const platformResourceRoutes: RouteVO[] = [
  {
    path: "/platform-resource",
    component: "Layout",
    name: "PlatformResource",
    sort: 30,
    meta: {
      title: "平台医疗资源管理",
      icon: "system", // system resources
      hidden: false,
      alwaysShow: false,
      roles: ["superOperate", "internetHospitalAdmin"],
    },
    children: [
      {
        path: "adviceManagement",
        component: "platform-resource/adviceManagement/index",
        name: "AdviceManagement",
        meta: {
          title: "医嘱管理",
          icon: "el-icon-Reading",
          hidden: false,
          keepAlive: true,
          alwaysShow: false,
          params: null,
          roles: ["superOperate"],
        },
      },
      {
        path: "trainingActionManagement",
        component: "platform-resource/trainingActionManagement/index",
        name: "TrainingActionManagement",
        meta: {
          title: "训练动作",
          icon: "el-icon-Reading",
          hidden: false,
          keepAlive: true,
          alwaysShow: false,
          params: null,
        },
      },
      {
        path: "recoveryMissionManagement",
        component: "platform-resource/recoveryMissionManagement/index",
        name: "RecoveryMissionManagement",
        meta: {
          title: "康复宣教",
          icon: "el-icon-Memo",
          hidden: false,
          keepAlive: true,
          alwaysShow: false,
          params: null,
          roles: ["superOperate", "internetHospitalAdmin"],
        },
      },
      {
        path: "gaugeManagement",
        component: "platform-resource/gaugeManagement/index",
        name: "GaugeManagement",
        meta: {
          title: "表单管理",
          icon: "el-icon-Tickets",
          hidden: false,
          keepAlive: true,
          alwaysShow: false,
          params: null,
          roles: ["superOperate", "internetHospitalAdmin"],
        },
      },
      {
        path: "servicePackageManagement",
        component: "platform-resource/servicePackageManagement/index",
        name: "ServicePackageManagement",
        meta: {
          title: "服务包管理",
          icon: "el-icon-Reading",
          hidden: false,
          keepAlive: true,
          alwaysShow: false,
          params: null,
          roles: ["superOperate"],
        },
      },
      {
        path: "followUpTemplate",
        component: "platform-resource/followUpTemplate/index",
        name: "FollowUpTemplate",
        meta: {
          title: "随访模板",
          icon: "el-icon-Memo",
          hidden: false,
          keepAlive: true,
          alwaysShow: false,
          params: null,
          roles: ["internetHospitalAdmin"],
        },
      },
      {
        path: "returnHospitalReminder",
        component: "platform-resource/returnHospitalReminder/index",
        name: "ReturnHospitalReminder",
        meta: {
          title: "回院提醒",
          icon: "el-icon-Bell",
          hidden: false,
          keepAlive: true,
          alwaysShow: false,
          params: null,
          roles: ["superAdmin", "internetHospitalAdmin"],
        },
      },
      {
        path: "evaluationScheme",
        component: "platform-resource/evaluationScheme/index",
        name: "EvaluationScheme",
        meta: {
          title: "评估方案",
          icon: "el-icon-Bell",
          hidden: false,
          keepAlive: true,
          alwaysShow: false,
          params: null,
          roles: ["superOperate", "superAdmin"],
        },
      },
    ],
  },
];
export default platformResourceRoutes;
