import { type RouteVO } from "@/api/system/menu";

const orderManagementRoutes: RouteVO[] = [
  {
    path: "/order-management",
    component: "Layout",
    name: "OrderManagement",
    sort: 60,
    meta: {
      title: "订单管理",
      icon: "system",
      hidden: false,
      alwaysShow: false,
      roles: [
        "sales",
        "internetHospitalAdmin",
        "assistant",
        "finance",
        "operations",
        "storage",
        "superOperate",
        "externalSeller",
      ],
    },
    children: [
      {
        path: "consultationOrder",
        component: "order-management/consultationOrder/index",
        name: "ConsultationOrder",
        meta: {
          title: "问诊订单",
          icon: "el-icon-ChatLineSquare",
          hidden: false,
          keepAlive: true,
          alwaysShow: false,
          params: null,
          roles: ["superOperate", "finance", "operations"],
        },
      },
      {
        path: "therapyOrder",
        component: "order-management/therapyOrder/index",
        name: "TherapyOrder",
        meta: {
          title: "治疗订单管理",
          icon: "el-icon-Place",
          hidden: false,
          keepAlive: true,
          alwaysShow: false,
          params: null,
          roles: [
            "sales",
            "internetHospitalAdmin",
            "assistant",
            "finance",
            "operations",
            "storage",
            "externalSeller",
            "superOperate",
          ],
        },
      },
      {
        path: "depositManagement",
        component: "order-management/depositManagement/index",
        name: "DepositManagement",
        meta: {
          title: "设备押金管理",
          icon: "el-icon-Briefcase",
          hidden: false,
          keepAlive: true,
          alwaysShow: false,
          params: null,
          roles: ["assistant", "finance", "operations", "storage"],
        },
      },
      {
        path: "refundManagement",
        component: "order-management/refundManagement/index",
        name: "RefundManagement",
        meta: {
          title: "退款管理",
          icon: "el-icon-money",
          hidden: false,
          keepAlive: true,
          alwaysShow: false,
          params: null,
          roles: ["sales", "finance", "operations", "externalSeller"],
        },
      },
      {
        path: "tableCardManagement",
        component: "order-management/tableCardManagement/index",
        name: "TableCardManagement",
        meta: {
          title: "台卡管理",
          icon: "el-icon-PictureFilled",
          hidden: false,
          keepAlive: true,
          alwaysShow: false,
          params: null,
          roles: ["assistant", "superOperate"],
        },
      },
      {
        path: "tableCardOrder",
        component: "order-management/tableCardOrder/index",
        name: "TableCardOrder",
        meta: {
          title: "台卡订单",
          icon: "el-icon-Postcard",
          hidden: false,
          keepAlive: true,
          alwaysShow: false,
          params: null,
          roles: ["assistant", "superOperate"],
        },
      },
    ],
  },
];
export default orderManagementRoutes;
