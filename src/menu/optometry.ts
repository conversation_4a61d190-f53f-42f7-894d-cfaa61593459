import { type RouteVO } from "@/api/system/menu";
const optometryRoutes: RouteVO[] = [
  {
    path: "/optometry",
    component: "Layout",
    name: "Optometry",
    sort: 130,
    meta: {
      title: "视光筛查项目",
      icon: "el-icon-View",
      hidden: false,
      alwaysShow: false,
      roles: ["visionScreenAdmin"],
    },
    children: [
      {
        path: "optometryDataImport",
        name: "OptometryDataImport",
        component: "optometry/optometryDataImport/index",
        meta: {
          title: "筛查数据导入",
          icon: "el-icon-Place",
          hidden: false,
          keepAlive: true,
          alwaysShow: false,
          params: null,
        },
      },
      {
        path: "optometryMissionManagement",
        name: "OptometryMissionManagement",
        component: "optometry/optometryMissionManagement/index",
        meta: {
          title: "视光宣教推荐设置",
          icon: "el-icon-Place",
          hidden: false,
          keepAlive: true,
          alwaysShow: false,
          params: null,
        },
      },
      {
        path: "optometryMemberManagement",
        name: "OptometryMemberManagement",
        component: "optometry/optometryMemberManagement/index",
        meta: {
          title: "视光成员管理",
          icon: "el-icon-Place",
          hidden: false,
          keepAlive: true,
          alwaysShow: false,
          params: null,
        },
      },
      {
        path: "optometryReportQuery",
        name: "OptometryReportQuery",
        component: "optometry/optometryReportQuery/index",
        meta: {
          title: "报告查询",
          icon: "el-icon-Place",
          hidden: false,
          keepAlive: true,
          alwaysShow: false,
          params: null,
        },
      },
      {
        path: "optometrySchoolManagement",
        name: "OptometrySchoolManagement",
        component: "optometry/optometrySchoolManagement/index",
        meta: {
          title: "学校管理",
          icon: "el-icon-Place",
          hidden: false,
          keepAlive: true,
          alwaysShow: false,
          params: null,
        },
      },
    ],
  },
];
export default optometryRoutes;
