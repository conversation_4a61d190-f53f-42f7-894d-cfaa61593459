import { type RouteVO } from "@/api/system/menu";
const dashboardRoutes: RouteVO[] = [
  {
    path: "/dashboard",
    component: "Layout",
    sort: 10,
    meta: {
      title: "首页",
      icon: "homepage", // system resources
      hidden: false,
      alwaysShow: false,
      roles: ["sales", "superOperate", "operations"],
    },
    children: [
      {
        path: "index",
        component: "dashboard/index",
        name: "Dashboard",
        meta: {
          title: "首页",
          icon: "homepage",
          hidden: false,
          keepAlive: true,
          alwaysShow: false,
          params: null,
        },
      },
    ],
  },
];
export default dashboardRoutes;
