import { type RouteVO } from "@/api/system/menu";

const hospitalResourceRoutes: RouteVO[] = [
  {
    path: "/hospital-resource",
    component: "Layout",
    name: "HospitalResource",
    sort: 40,
    meta: {
      title: "医院医疗资源管理",
      icon: "system", // system resources
      hidden: false,
      alwaysShow: false,
      roles: ["superAdmin"],
    },
    children: [
      {
        path: "servicePackageManagement",
        component: "hospital-resource/servicePackageManagement/index",
        name: "HospitalServicePackageManagement",
        meta: {
          title: "医院服务包管理",
          icon: "el-icon-Reading",
          hidden: false,
          keepAlive: true,
          alwaysShow: false,
          params: null,
        },
      },
      {
        path: "adviceManagement",
        component: "hospital-resource/adviceManagement/index",
        name: "HospitalAdviceManagement",
        meta: {
          title: "医院医嘱管理",
          icon: "el-icon-Reading",
          hidden: false,
          keepAlive: true,
          alwaysShow: false,
          params: null,
        },
      },
      {
        path: "hospitalFormManagement",
        component: "hospital-resource/hospitalFormManagement/index",
        name: "HospitalFormManagement",
        meta: {
          title: "医院表单",
          icon: "el-icon-DocumentCopy",
          hidden: false,
          keepAlive: true,
          alwaysShow: false,
          params: null,
        },
      },
    ],
  },
];
export default hospitalResourceRoutes;
