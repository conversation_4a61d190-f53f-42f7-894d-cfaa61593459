import { type RouteVO } from "@/api/system/menu";

const baseDataRoutes: RouteVO[] = [
  {
    path: "/base-data",
    component: "Layout",
    name: "BaseData",
    sort: 20,
    meta: {
      title: "基础数据管理",
      icon: "system",
      hidden: false,
      alwaysShow: false,
      roles: ["superOperate", "internetHospitalAdmin"],
    },
    children: [
      {
        path: "dysfunctionManagement",
        component: "base-data/dysfunctionManagement/index",
        name: "DysfunctionManagement",
        meta: {
          title: "功能障碍管理",
          icon: "el-icon-Place",
          hidden: false,
          keepAlive: true,
          alwaysShow: false,
          params: null,
          roles: ["superOperate"],
        },
      },
      {
        path: "bodyPartManagement",
        component: "base-data/bodyPartManagement/index",
        name: "BodyPartManagement",
        meta: {
          title: "部位管理",
          icon: "el-icon-Place",
          hidden: false,
          keepAlive: true,
          alwaysShow: false,
          params: null,
          roles: ["superOperate", "internetHospitalAdmin"],
        },
      },
      {
        path: "rehabilitationClassificationManagement",
        component: "base-data/rehabilitationClassificationManagement/index",
        name: "RehabilitationClassificationManagement",
        meta: {
          title: "康复分类",
          icon: "el-icon-Place",
          hidden: false,
          keepAlive: true,
          alwaysShow: false,
          params: null,
          roles: ["superOperate", "internetHospitalAdmin"],
        },
      },
      {
        path: "diseaseManagement",
        component: "base-data/diseaseManagement/index",
        name: "DiseaseManagement",
        meta: {
          title: "病种管理",
          icon: "el-icon-Place",
          hidden: false,
          keepAlive: true,
          alwaysShow: false,
          params: null,
          roles: ["superOperate"],
        },
      },
      {
        path: "icd10Management",
        component: "base-data/icd10Management/index",
        name: "Icd10Management",
        meta: {
          title: "ICD-10",
          icon: "el-icon-Place",
          hidden: false,
          keepAlive: true,
          alwaysShow: false,
          params: null,
        },
      },
      {
        path: "professionalTitleManagement",
        component: "base-data/professionalTitleManagement/index",
        name: "ProfessionalTitleManagement",
        meta: {
          title: "职称管理",
          icon: "el-icon-Place",
          hidden: false,
          keepAlive: true,
          alwaysShow: false,
          params: null,
        },
      },
      {
        path: "workerTypeManagement",
        component: "base-data/workerTypeManagement/index",
        name: "WorkerTypeManagement",
        meta: {
          title: "职业类型管理",
          icon: "el-icon-Place",
          hidden: false,
          keepAlive: true,
          alwaysShow: false,
          params: null,
        },
      },
      {
        path: "departmentManagement",
        component: "base-data/departmentManagement/index",
        name: "DepartmentManagement",
        meta: {
          title: "科别管理",
          icon: "el-icon-Place",
          hidden: false,
          keepAlive: true,
          alwaysShow: false,
          params: null,
        },
      },
      {
        path: "deviceTypeManagement",
        component: "base-data/deviceTypeManagement/index",
        name: "DeviceTypeManagement",
        meta: {
          title: "设备类型管理",
          icon: "el-icon-Place",
          hidden: false,
          keepAlive: true,
          alwaysShow: false,
          params: null,
        },
      },
      {
        path: "instrument-price",
        name: "InstrumentPrice",
        component: "base-data/instrument-price/index",
        meta: {
          title: "设备费用管理",
          roles: ["superOperate"],
          keepAlive: true,
          hidden: false,
        },
      },
      {
        path: "TCMSyndrome",
        component: "base-data/TCMSyndrome/index",
        name: "TCMSyndrome",
        meta: {
          title: "中医症候",
          icon: "el-icon-ChatLineRound",
          hidden: false,
          keepAlive: true,
          alwaysShow: false,
          params: null,
          roles: ["superOperate", "internetHospitalAdmin"],
        },
      },
      {
        path: "TCMDiseasePattern",
        component: "base-data/TCMDiseasePattern/index",
        name: "TCMDiseasePattern",
        meta: {
          title: "中医病症",
          icon: "el-icon-ChatLineSquare",
          hidden: false,
          keepAlive: true,
          alwaysShow: false,
          params: null,
          roles: ["superOperate", "internetHospitalAdmin"],
        },
      },
      {
        path: "consumablesManagement",
        component: "base-data/consumablesManagement/index",
        name: "ConsumablesManagement",
        meta: {
          title: "耗材管理",
          icon: "el-icon-Place",
          hidden: false,
          keepAlive: true,
          alwaysShow: false,
          params: null,
          roles: ["superOperate", "internetHospitalAdmin"],
        },
      },
      {
        path: "acupointManagement",
        component: "base-data/acupointManagement/index",
        name: "AcupointManagement",
        meta: {
          title: "穴位管理",
          icon: "el-icon-MagicStick",
          hidden: false,
          keepAlive: true,
          alwaysShow: false,
          params: null,
          roles: ["superOperate", "internetHospitalAdmin"],
        },
      },
      {
        path: "acupointTemplate",
        component: "base-data/acupointTemplate/index",
        name: "AcupointTemplate",
        meta: {
          title: "穴位模板",
          icon: "el-icon-CopyDocument",
          hidden: false,
          keepAlive: true,
          alwaysShow: false,
          params: null,
          roles: ["superOperate", "internetHospitalAdmin"],
        },
      },
      {
        path: "termManagement",
        component: "base-data/termManagement/index",
        name: "TermManagement",
        meta: {
          title: "词条管理",
          icon: "el-icon-Tickets",
          hidden: false,
          keepAlive: true,
          alwaysShow: false,
          params: null,
          roles: ["superOperate", "internetHospitalAdmin"],
        },
      },
      {
        path: "recommendedContent",
        component: "base-data/recommendedContent/index",
        name: "RecommendedContent",
        meta: {
          title: "推荐内容管理",
          icon: "el-icon-Share",
          hidden: false,
          keepAlive: true,
          alwaysShow: false,
          params: null,
          roles: ["superOperate", "internetHospitalAdmin"],
        },
      },
      {
        path: "commonPhraseManagement",
        component: "base-data/commonPhraseManagement/index",
        name: "CommonPhraseManagement",
        meta: {
          title: "常用语管理",
          icon: "el-icon-Message",
          hidden: false,
          keepAlive: true,
          alwaysShow: false,
          params: null,
          roles: ["superOperate"],
        },
      },
    ],
  },
];
export default baseDataRoutes;
