import { type RouteVO } from "@/api/system/menu";

const routes: RouteVO[] = [
  {
    path: "/setting",
    component: "Layout",
    name: "setting",
    sort: 160,
    redirect: "/setting/parameter",
    meta: {
      title: "系统设置",
      roles: ["superAdmin"],
    },
    children: [
      {
        path: "parameter",
        name: "parameter",
        component: "setting/parameter/index",
        meta: { title: "参数设置" },
      },
      {
        path: "menu",
        name: "MenuSetting",
        component: "setting/menu/index",
        meta: {
          title: "菜单设置",
          icon: "el-icon-Menu",
          hidden: false,
          keepAlive: true,
          alwaysShow: false,
          params: null,
          roles: ["superAdmin"],
        },
      },
      // {
      //   path: "slide-parameter",
      //   name: "slide-parameter",
      //   hidden: false,
      //   component: () => import("@/views/setting/slide-parameter"),
      //   meta: { title: "轮播图参数设置" },
      // },
      {
        path: "Terms",
        name: "Terms",
        component: "setting/Terms",
        meta: { title: "用户协议维护" },
      },
      // {
      //   path: "privacy",
      //   name: "privacy",
      //   component: () => import("@/views/setting/privacy"),
      //   meta: { title: "隐私协议维护" },
      // },
      // {
      //   path: "application",
      //   name: "application",
      //   component: () => import("@/views/setting/application"),
      //   meta: { title: "应用管理" },
      // },
      // {
      //   path: "resources",
      //   name: "resources",
      //   component: () => import("@/views/setting/resources"),
      //   meta: { title: "服务管理" },
      // },
      // {
      //   path: "baseScope",
      //   name: "baseScope",
      //   component: () => import("@/views/setting/baseScope"),
      //   meta: { title: "作用域管理", roles: [] },
      // },
      // {
      //   path: "DeviceTypeProtocol",
      //   name: "DeviceTypeProtocol",
      //   component: () => import("@/views/setting/DeviceTypeProtocol"),
      //   meta: {
      //     title: "设备租赁协议",
      //     roles: [],
      //   },
      // },
    ],
  },
];
export default routes;
