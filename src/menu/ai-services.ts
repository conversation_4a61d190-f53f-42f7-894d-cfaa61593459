import { type RouteVO } from "@/api/system/menu";

const routes: RouteVO[] = [
  {
    path: "/ai-services",
    component: "Layout",
    name: "ai-services",
    sort: 140,
    redirect: "/ai-services/DeviceBinding",
    meta: {
      title: "AI服务流程",
      hidden: false,
      roles: ["superAd<PERSON>", "assistant"],
    },
    children: [
      {
        path: "toBeStarted",
        name: "AiServicesToBeStarted",
        component: "ai-services/toBeStarted/index",
        meta: {
          title: "待启动流程",
          icon: "el-icon-Place",
          hidden: false,
          keepAlive: true,
          alwaysShow: false,
          params: null,
        },
      },
      {
        path: "started",
        name: "AiServicesStarted",
        component: "ai-services/started/index",
        meta: {
          title: "已启动流程",
          icon: "el-icon-Place",
          hidden: false,
          keepAlive: true,
          alwaysShow: false,
          params: null,
        },
      },
    ],
  },
];
export default routes;
