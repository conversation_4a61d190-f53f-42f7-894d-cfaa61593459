import { type RouteVO } from "@/api/system/menu";
const feedbackRoutes: RouteVO[] = [
  {
    path: "/feedback",
    component: "Layout",
    name: "Feedback",
    sort: 100,
    meta: {
      title: "用户反馈",
      icon: "el-icon-Promotion",
      hidden: false,
      alwaysShow: false,
      roles: ["assistant", "superAdmin"],
    },
    children: [
      {
        path: "userFeedback",
        component: "feedback/userFeedback/index",
        name: "UserFeedback",
        meta: {
          title: "用户反馈",
          icon: "el-icon-Message",
          hidden: false,
          keepAlive: true,
          alwaysShow: false,
          params: null,
        },
      },
      {
        path: "serviceFeedback",
        component: "feedback/serviceFeedback/index",
        name: "ServiceFeedback",
        meta: {
          title: "就诊服务评价",
          icon: "el-icon-Message",
          hidden: false,
          keepAlive: true,
          alwaysShow: false,
          params: null,
        },
      },
      {
        path: "rehabilitationFeedback",
        component: "feedback/rehabilitationFeedback/index",
        name: "RehabilitationFeedback",
        meta: {
          title: "康复服务评价",
          icon: "el-icon-Message",
          hidden: false,
          keepAlive: true,
          alwaysShow: false,
          params: null,
        },
      },
    ],
  },
];
export default feedbackRoutes;
