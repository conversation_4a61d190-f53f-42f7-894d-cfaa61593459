import request from "@/utils/request";
import { type DictCreateUpdateInputDTO } from "./types";

// --------------- dictionary_Dict ---------------
const Dictionary_Dict = "/dictionary/api/Dict";
// --------------- dictionary_DictItem ---------------
const Dictionary_DictItem = "/dictionary/api/DictItem";

const Dictionary_Api = {
  // --------------- Dict ---------------
  /**
   * 根据code获取字典信息
   *
   * @param code
   *
   * RecoveryClassify: 康复分类
   * CMSymptomsDict: 中医症候
   * CMDiseasesDict: 中医病症
   */
  getDictByCode(code: string): Promise<ServerResult<DictCode>> {
    return request.get(`${Dictionary_Dict}/GetDictByCode`, {
      params: {
        code: code,
      },
    });
  },

  /**
   * 读取字典表数据
   *
   * @param query.code
   * DiseaseDict: 疾病列表
   */
  getDict(query: { code: string; orgId?: string }): Promise<ServerResult<ReadDict[]>> {
    return request.get(`${Dictionary_Dict}/GetDict`, {
      params: {
        code: query.code,
        orgId: query.orgId,
      },
    });
  },

  readStd(query: DictQueryParams): Promise<ServerResult<ListRowsTotal<DictCode>>> {
    return request.post(`${Dictionary_Dict}/ReadStd`, query);
  },

  // --------------- DictItem ---------------
  /**
   *
   * @param query 读取字典数据
   * @returns
   */
  readDict(query: DictQueryParams): Promise<ServerResult<ListRowsTotal<ReadDict>>> {
    return request.post(`${Dictionary_DictItem}/Read`, query);
  },

  /**
   *
   * @param query 读取字典数据
   * @returns
   */
  itemReadStd(query: DictQueryParams): Promise<ServerResult<ListRowsTotal<ReadDict>>> {
    return request.post(`${Dictionary_DictItem}/ReadStd`, query);
  },

  /**
   *
   * @param query 读取字典数据
   * @returns
   */
  readDict1(query: DictQueryParams): Promise<ServerResult<ListRowsTotal<ReadDict>>> {
    return request.post(`${Dictionary_DictItem}/Read1`, query);
  },
  /**
   * 创建字典数据
   */
  createDict(data: DictCreateUpdateInputDTO[]): Promise<ServerResult<null>> {
    return request.post(`${Dictionary_DictItem}/Create`, data);
  },
  /**
   * 更新字典数据
   */
  updateDict(data: DictCreateUpdateInputDTO[]): Promise<ServerResult<null>> {
    return request.post(`${Dictionary_DictItem}/Update`, data);
  },
  /**
   * 删除字典数据
   */
  deleteDict(data: { id: string }): Promise<ServerResult<null>> {
    return request.post(`${Dictionary_DictItem}/Delete?id=${data.id}`);
  },
  /**
   * 删除字典数据
   */
  deleteDict1(data: { id: string }): Promise<ServerResult<null>> {
    return request.post(`${Dictionary_DictItem}/Delete1?id=${data.id}`);
  },
  /**
   * 发布字典数据
   */
  publishDict(data: { id: string }): Promise<ServerResult<null>> {
    return request.post(`${Dictionary_DictItem}/Publish?id=${data.id}`);
  },
  /**
   * 是否含有子字典数据
   */
  haveSonData(id: string): Promise<ServerResult<boolean>> {
    return request.get(`${Dictionary_DictItem}/HaveSonData`, {
      params: {
        parentId: id,
      },
    });
  },
};
export default Dictionary_Api;
