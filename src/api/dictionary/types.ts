import { type EpPropMergeTypeWithNull } from "element-plus";

export interface DictCreateUpdateInputDTO {
  Key: string;
  Value?: string;
  OrgId?: EpPropMergeTypeWithNull<string>;
  DepartmentId?: string;
  IsPublish?: boolean;
  PinyinCode: string;
  Remark?: string;
  IsEnabled: boolean;
  DictId: number;
  Id?: string;
  DictItemRelateds?: DictItemRelated[];
  OrderNumber?: number;
  CreatedTime?: string;
  CustomSort?: EpPropMergeTypeWithNull<number>;
  ParentId?: EpPropMergeTypeWithNull<string>;
  Updatable?: boolean;
  Deletable?: boolean;
  IsDefaultPush?: boolean;
}
