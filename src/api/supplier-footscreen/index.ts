import request from "@/utils/request";
import { type FootReportPageItem } from "./types";

const Supplier_FootScreen_Info = "/supplier/footscreen/api/info";
const Supplier_FootScreen_Api = {
  getReport(data: {
    Name: string;
    Phone: string;
    IsLatest?: boolean;
    PageIndex?: number;
    PageSize?: number;
  }) {
    return request.post<ListDataTotalCount<FootReportPageItem>>(
      `${Supplier_FootScreen_Info}/GetReport`,
      data
    );
  },
};

export default Supplier_FootScreen_Api;
