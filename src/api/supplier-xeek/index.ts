import request from "@/utils/request";
import { type SnCodeItem } from "./types";

const Supplier_Xeek_Info = "/supplier/xeek/Info";
const Supplier_Xeek_Api = {
  getSendSnCodeRecord(params: {
    pageIndex: number;
    pageSize: number;
    keywords: string;
  }): Promise<ServerResult<ListDataTotal<SnCodeItem>>> {
    return request.get(`${Supplier_Xeek_Info}/GetSendSnCodeRecord`, { params });
  },
  /** 添加设备SN码 */
  sendSnCode(data: {
    CreatorId: string;
    CreatorName: string;
    SnCodes: string[];
  }): Promise<ServerResult<null>> {
    return request.post(`${Supplier_Xeek_Info}/SendSnCode`, data);
  },
  /** 批量导入设备SN码 */
  fileSendSnCode(data: { Url: string }): Promise<ServerResult<null>> {
    return request.post(`${Supplier_Xeek_Info}/FileSendSnCode`, data);
  },
};

export default Supplier_Xeek_Api;
