import request from "@/utils/request";
import {
  type FlowInstanceInputDTO,
  type FlowInstanceItem,
  type FlowItem,
  type FlowListInputDTO,
} from "./types";

const AIPowered_Workflow = "/aipowered/api/workflow";
const AIPowered_Room = "/aipowered/api/room";
const AIPowered_Api = {
  /** 获取待启动流程列表 */
  getPendingFlowList(data: FlowListInputDTO): Promise<ServerResult<ListRowsTotal<FlowItem>>> {
    return request.post(`${AIPowered_Workflow}/GetPendingFlowList`, data);
  },
  /** 启动流程 */
  launchFlow(data: { PrescriptionId: string }): Promise<ServerResult<null>> {
    return request.post(`${AIPowered_Workflow}/LaunchFlow`, data);
  },
  /** 切换AI客服 */
  switchAiCustomerService(data: { PrescriptionId: string }): Promise<ServerResult<null>> {
    return request.post(`${AIPowered_Workflow}/SwitchAiCustomerService`, data);
  },
  /** 切换群聊消息 */
  switchRoomReceiveMessage(data: { RoomWxId: string }): Promise<ServerResult<null>> {
    return request.post(`${AIPowered_Room}/SwitchRoomReceiveMessage`, data);
  },
  /** 暂停或恢复流程 */
  suspendOrResumeFlow(
    data: { PrescriptionId: string; Reason?: string },
    type: "Suspend" | "Resume"
  ): Promise<ServerResult<null>> {
    return request.post(`${AIPowered_Workflow}/${type}`, data);
  },
  /** 终止流程 */
  terminateFlow(data: { PrescriptionId: string }): Promise<ServerResult<null>> {
    return request.post(`${AIPowered_Workflow}/Terminate`, data);
  },
  /** 重启流程 */
  restartFlow(data: { PrescriptionId: string }): Promise<ServerResult<null>> {
    return request.post(`${AIPowered_Workflow}/Restart`, data);
  },
  /** 获取流程列表 */
  getFlowList(data: FlowListInputDTO): Promise<ServerResult<ListRowsTotal<FlowItem>>> {
    return request.post(`${AIPowered_Workflow}/GetFlowList`, data);
  },
  /** 获取流程实例列表 */
  getFlowInstanceList(
    data: FlowInstanceInputDTO
  ): Promise<ServerResult<ListRowsTotal<FlowInstanceItem>>> {
    return request.post(`${AIPowered_Workflow}/GetFlowInstanceList`, data);
  },
  /** 获取流程状态 */
  getFlowState(params: { prescriptionId: string }): Promise<ServerResult<any>> {
    return request.get(`${AIPowered_Workflow}/FlowState`, { params });
  },
  /** 添加诊断 */
  updateAssistantDiagnosis(data: {
    PrescriptionId: string;
    AssistantDiagnosis: string;
  }): Promise<ServerResult<null>> {
    return request.post(`${AIPowered_Workflow}/UpdateAssistantDiagnosis`, data);
  },
};

export default AIPowered_Api;
