import request from "@/utils/request";
import {
  type CheckedReportItem,
  type CheckedReportInputDTO,
  type QingpaiFileReportResponse,
  type SchoolInfoInputDTO,
  type SchoolInfoItem,
} from "./types";

const Supplier_Qingpai_Push = "/supplier/qingpai/Push";
const Supplier_Qingpai_Info = "/supplier/qingpai/Info";
const Supplier_Qingpai_Api = {
  /**
   * 对上传的文件进行解析
   * @param data 上传的文件
   * @returns 解析结果
   */
  fileReport(data: { Url: string }): Promise<ServerResult<QingpaiFileReportResponse>> {
    return request.post(`${Supplier_Qingpai_Push}/FileReport`, data, {
      timeout: 60000,
    });
  },
  /** 查询报告统计 */
  getCheckedReport(data: CheckedReportInputDTO): Promise<ServerResult<CheckedReportItem[]>> {
    return request.post(`${Supplier_Qingpai_Info}/GetCheckedReport`, data);
  },
  /** 获取学校数据 */
  getSchoolInfo(data: SchoolInfoInputDTO): Promise<ServerResult<SchoolInfoItem[]>> {
    return request.post(`${Supplier_Qingpai_Info}/GetSchoolInfo`, data);
  },
};

export default Supplier_Qingpai_Api;
