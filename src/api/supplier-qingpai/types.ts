import { type EpPropMergeTypeWithNull } from "element-plus";

export interface QingpaiFileReportResponse {
  Stage: "Success" | "Fail";
  TotalCount: number;
  SuccessCount: number;
  FailCount: number;
  ErrorDetail: Record<string, string>;
}
export interface CheckedReportInputDTO {
  BeginTime: string;
  EndTime: string;
  SchoolName: EpPropMergeTypeWithNull<string>;
  SchoolCategory: EpPropMergeTypeWithNull<string>;
  Grade: EpPropMergeTypeWithNull<string>;
}
export interface CheckedReportItem {
  ReportCount?: number;
  CheckedCount?: number;
  UnCheckedCount?: number;
  CheckedRate?: number;
  OrderIdx?: string;
  SchoolName?: string;
  SchoolCategory?: string;
  Grade?: string;
  Class?: string;
}

export interface SchoolInfoInputDTO {
  SchoolCategory?: string;
  SchoolName?: string;
}
export interface SchoolInfoItem {
  SchoolCategory?: string;
  SchoolName?: string;
  Grade?: string;
  Id?: string;
}
