export interface BankProvince {
  ProvinceCode: number;
  ProvinceName: string;
}
export interface BankCity {
  CityCode: number;
  CityName: string;
  BankAddressCode: string;
}
export interface SubBranchInputDTO {
  BankCode: string;
  CityCode: number;
  Keyword: string;
  PageIndex: number;
  PageSize: number;
}
export interface GrassQRCodeInputDTO {
  cliT: string;
  cliD: string;
  api_key?: string;
  sign?: string;
  return_file?: string;
  [key: `cliF${number}`]: string | undefined;
  [key: `cliP${number}`]: string | undefined;
}
