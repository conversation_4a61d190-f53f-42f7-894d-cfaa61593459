import request from "@/utils/request";
import { type GrassQRCodeInputDTO, type SubBranchInputDTO } from "./types";
const Other_Wechat_Doctorkyx_MP = "/wechat-doctorkyx/api/mp";
const Other_Api = {
  /** 获取开户地 */
  getBankProvinceOrCity<T>(
    type: string,
    data: { provinceCode?: string }
  ): Promise<ServerResult<T[]>> {
    return request.get(`${Other_Wechat_Doctorkyx_MP}/Get${type}`, {
      params: data,
    });
  },
  /** 获取开户行 */
  getSubBranch(
    data: SubBranchInputDTO
  ): Promise<ServerResult<ListRowsTotal<{ BranchId: string; BranchName: string }>>> {
    return request.post(`${Other_Wechat_Doctorkyx_MP}/GetSubBranch`, data);
  },
  /** 获取银行卡信息 */
  getBankInfo(data: { BankNum: string }): Promise<ServerResult<BaseBankInfo>> {
    return request.post(`${Other_Wechat_<PERSON>kyx_MP}/GetBankInfo`, data);
  },
  /** 获取经纬度 */
  getLngLatByAddress(address: string): Promise<BaseAddressInfo> {
    return fetch(
      "https://restapi.amap.com/v3/geocode/geo?platform=JS&s=rsv3&logversion=2.0&key=ee1fbdaeaaba54fad923021d68ff12b2&sdkversion=********&appname=https://lbs.amap.com/demo/jsapi-v2/example/geocoder/regeocoding&jscode=44e48de9fc6a5488dc1da13b10f76831&address=" +
        address,
      {
        method: "GET",
      }
    ).then((res) => res.json());
  },
  /** 获取草稿箱二维码 */
  getGrassQRCode(
    params: GrassQRCodeInputDTO
  ): Promise<ServerResult<string | { code: number; data: string | null; message: string }>> {
    return request.get(
      `https://open-api.cli.im/cli-open-platform-service/v1/labelStyle/createWithKey`,
      {
        params,
      }
    );
  },
};
export default Other_Api;
