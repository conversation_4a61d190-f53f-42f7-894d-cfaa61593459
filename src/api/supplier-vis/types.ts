export interface VisReportPageItem {
  Id?: string;
  DeviceId?: string;
  Phone?: string;
  OriginalData?: OriginalData;
  PdfUrl?: string;
  Advice?: string[];
  Conclusion?: string[];
  ReportTime?: string;
  CreatedTime?: string;
}
interface OriginalData {
  user_info: Userinfo;
  project: string[];
  action_status: Actionstatus;
  device_id: string;
  scan_id: string;
  time: string;
  token: string;
}
interface Actionstatus {
  measure_status?: any;
  eval_status: boolean;
  bia_status?: any;
  bodypredict_status?: any;
  tchar_status?: any;
  eval_dynamic_status?: any;
}
interface Userinfo {
  age: number;
  birthday: string;
  height: number;
  phone: string;
  sex: string;
}
