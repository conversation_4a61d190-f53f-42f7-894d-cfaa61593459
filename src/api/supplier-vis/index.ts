import request from "@/utils/request";
import { type VisReportPageItem } from "./types";

const Supplier_Vis_Info = "/supplier/vis/api/info";
const Supplier_Vis_Api = {
  queryReport(data: { Phone: string; PageIndex?: number; PageSize?: number }) {
    return request.post<ListDataTotalCount<VisReportPageItem>>(
      `${Supplier_Vis_Info}/QueryReport`,
      data
    );
  },
};

export default Supplier_Vis_Api;
