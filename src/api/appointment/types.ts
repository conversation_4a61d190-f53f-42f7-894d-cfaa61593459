import { type EpPropMergeTypeWithNull } from "element-plus";

export interface SchoolQueryInputDTO {
  IsEnable: EpPropMergeTypeWithNull<boolean>;
  Keyword: string;
  PageIndex: number;
  PageSize: number;
}
export interface SchoolItem {
  Id?: string;
  Name?: string; // 学校名称
  Code?: string; // 学校编码
  PYM?: string; // 拼音码
  IsEnable?: boolean; // 是否启用
  CreatorId?: string; // 创建人ID
  Creator?: string; // 创建人
  UpdatedTime?: string; // 更新时间
  UpdaterId?: string; // 更新人ID
}
