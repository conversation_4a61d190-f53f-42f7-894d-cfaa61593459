import request from "@/utils/request";
import { type SchoolItem, type SchoolQueryInputDTO } from "./types";

const Appointment_School = "/appointment/School";
const Appointment_Api = {
  /** 查询学校分页 */
  querySchoolPage(data: SchoolQueryInputDTO) {
    return request.post<ListDataTotalCount<SchoolItem>>(
      `${Appointment_School}/QuerySchoolPage`,
      data
    );
  },
  /** 新增/编辑学校 */
  saveSchool(data: SchoolItem) {
    return request.post<ServerResult<null>>(`${Appointment_School}/SaveSchool`, data);
  },
  /** 删除学校 */
  deleteSchool(data: { Id: string }) {
    return request.delete<ServerResult<null>>(`${Appointment_School}/DeleteSchool`, {
      data,
    });
  },
};

export default Appointment_Api;
