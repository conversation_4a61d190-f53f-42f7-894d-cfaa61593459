export interface GetVisitsByUserIdParams {
  dateSerachType?: number;
  type?: number;
  userId: string;
  pageIndex: number;
  pageSize: number;
  organizationId?: string;
}

export interface UserArchive {
  Id?: string;
  UserId?: string;
  Address?: string;
  ContactPerson?: string;
  ContactPhone?: string;
  ContactRelation?: string;
  Height?: string;
  Weight?: string;
  Blood?: string;
  Marital?: string;
  Give?: string;
  Abstract?: string;
  IsDisability?: boolean;
  DisabilityType?: string;
  DisabilityLevel?: string;

  /**
   * 过敏史，JSON解析后是 TagsItem 类型
   */
  PreviousHistory?: string;
  /**
   * 过敏史，JSON解析后是 TagsItem 类型
   */
  AllergicHistory?: string;
  FamilyHistory?: string;
  DietaryHabit?: string;
  DietaryHabit1?: string;
  Smoking?: string;
  StartSmokingAge?: string;
  QuitSmokingAge?: string;
  DaySmoking?: string;
  Drink?: string;
  DayDrink?: string;
  IsAbstinence?: boolean;
  AbstinenceAge?: string;
  StartDrinkAge?: string;
  IsYearIntemperance?: boolean;
  WineType?: string;
  CreatedTime?: string;
  Name?: string;
  HeadImg?: string;
  Sex?: string;
  Birthday?: string;
  Age?: string;
  IdCard?: string;
  Nation?: string;
  NativePlace?: string;
  Education?: string;
  WorkState?: string;
  EconomicIncome?: string;
  Professional?: string;
  WorkUnit?: string;
}

export interface VisitOutputDTO {
  // 结算记录
  CostCalcs?: VisitCostCalc[];
  // 费用清单记录
  CostDetails?: VisitCostDetail[];
  // 预缴记录
  CostPays?: VisitCostPay[];
  VisitDatumImgs?: string[];
  // 诊断记录(可能包含一个主诊断和多个其他诊断、症状描述)
  VisitDiagnoses?: VisitDiagnosis[];
  VisitReportDetails?: VisitReportDetail[];
  // 各种图文报告
  VisitReports?: VisitReport[];
  VisitTreatmentTeams?: VisitTreatmentTeamMember[];
  // 访问信息
  Vist?: VisitInfo;
}

export interface VisitDiagnosis extends BaseDiagnosis {
  OthersId?: string;
  Source?: number;
  VisitId?: string;
  ConsultId?: string;
  MedicalId?: string;
  CreatedTime?: string;
  OperatorTime?: string;
  DeleteTime?: string;
  Id?: string;
}

export interface VisitCostCalc {
  Key?: string;
  Value?: string;
  VisitId?: string;
  Id?: string;
}

export interface VisitCostDetail {
  Item?: string;
  Price?: number;
  Number?: number;
  Amount?: number;
  VisitId?: string;
  CreatedTime?: string;
  Id?: string;
}

export interface VisitCostPay {
  VisitId?: string;
  PayWay?: string;
  Amount?: number;
  State?: string;
  CreatedTime?: string;
  Id?: string;
}

export interface VisitInfo {
  Id?: string;
  OthersId?: string;
  PatientId?: string;
  ConsultId?: string;
  Name?: string;
  Age?: string;
  Sex?: string;
  BirthDay?: string;
  VisitNo?: string;
  Source?: string;
  DepartmentName?: string;
  DepartmentId?: string;
  BedNo?: string;
  DoctorName?: string;
  DoctorId?: string;
  NurseId?: string;
  NurseName?: string;
  InDate?: string;
  OutDate?: string;
  ChiefComplaint?: string;
  Complain?: string;
  PresentHistory?: string;
  PresentIllness?: string;
  PastHistory?: string;
  HistoryIllness?: string;
  Allergies?: string;
  IsAllergy?: boolean;
  AllergyDrug?: string;
  PatientNo?: string;
  CardNumber?: string;
  OrganizationId?: string;
  OrganizationName?: string;
  CreatorId?: string;
  CreatedTime?: string;
  IsSelfBuild?: boolean;
  AuxiliaryDiagnosis?: string;
  TreatmentTime?: string;
  Department?: string;
  Disposal?: string;
  Address?: string;
  Marital?: string;
  Give?: string;
  Nation?: string;
  Professional?: string;
  WorkUnit?: string;
  ReportUrls?: string[];
  VisitDiagnoses?: VisitDiagnosis[];
}

export interface VisitReportDetail {
  Id?: string;
  OriginalReportUrl?: string;

  // 病历需要用到，不然报告找不到图片
  VisitReportId?: string;

  // 报告图片地址
  ReportUrl?: string;

  CreatorId?: string;
  // 0:同步，1：医生，2：患者
  Source?: number;
}

interface VisitReport {
  CreatedTime?: string;
  DeleteTime?: string;
  Id?: string;
  ItemNames?: string;
  OperatorTime?: string;
  OthersId?: string;
  ReportDate?: string;
  ReportName?: string;
  ReportType?: number;
  ReportTypeName?: string;
  Source?: number;
  VisitId?: string;
}

interface VisitTreatmentTeamMember {
  Id?: string;
  VisitId?: string;
  OthersId?: string;
  DoctorId?: string;
  DoctorTypeName?: string;
  Name?: string;
  Code?: string;
  CreatedTime?: string;
  OperatorTime?: string;
  DeleteTime?: string;
  testTime?: string;
}
