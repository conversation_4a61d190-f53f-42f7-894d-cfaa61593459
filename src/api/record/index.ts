import request from "@/utils/request";
import {
  type UserArchive,
  type GetVisitsByUserIdParams,
  type VisitOutputDTO,
  type VisitInfo,
} from "./types";

const Record_Info = "/record/api/info";
const Record_Visit = "/record/api/visit";

const Record_Api = {
  // --------------- info ---------------

  // 读取档案信息
  getUserArchives(data: DictQueryParams): Promise<ServerResult<ListRowsTotal<UserArchive>>> {
    return request.post(`${Record_Info}/Read`, data);
  },

  // --------------- visit ---------------

  // 读取随访信息
  getVisitsByUserId(data: GetVisitsByUserIdParams): Promise<ServerResult<VisitInfo[]>> {
    return request.post(`${Record_Visit}/GetVisitsByUserId`, data);
  },

  // 读取病历详情
  getVisit(params: { visitId: string }): Promise<ServerResult<VisitOutputDTO>> {
    return request.get(`${Record_Visit}/GetVisit`, { params });
  },
};
export default Record_Api;
