import { type EpPropMergeTypeWithNull } from "element-plus";

export interface IkidAndFootItem {
  SchoolCount?: number;
  UserCount?: number;
  IkidData?: IkidData;
  FootData?: FootData;
}
export interface FootData {
  SchoolCount?: number;
  UserCount?: number;
  LeftNormalCount?: number;
  RightNormalCount?: number;
  LeftNormalRate?: string;
  RightNormalRate?: string;
  LeftBoyUserCount?: number;
  LeftBoyRate?: string;
  RightBoyUserCount?: number;
  RightBoyRate?: string;
  LeftGirlUserCount?: number;
  LeftGirlRate?: string;
  RightGirlUserCount?: number;
  RightGirlRate?: string;
  LeftLowCount?: number;
  LeftLowRate?: string;
  RightLowCount?: number;
  RightLowRate?: string;
  LeftHighCount?: number;
  LeftHighRate?: string;
  RightHighCount?: number;
  RightHighRate?: string;
  LeftLineCharts?: FootLineChart[];
  RightLineCharts?: FootLineChart[];
}
export interface FootLineChart {
  Age?: number;
  MildRate?: string;
  Moderate?: string;
  Severe?: string;
  HighFoot?: string;
}
export interface IkidData {
  UserCount?: number;
  ProblemCount?: number;
  ProblemRate?: string;
  NormalCount?: number;
  NormalRate?: string;
  LowCount?: number;
  LowRate?: string;
  HighCount?: number;
  HighRate?: string;
  BoyUserCount?: number;
  BoyRate?: string;
  GirlUserCount?: number;
  GirlRate?: string;
  HeightRate?: string;
  BoyLineCharts?: IkidLineChart[];
  GirlLineCharts?: IkidLineChart[];
  HighLineCharts?: IkidLineChart[];
  LowLineCharts?: IkidLineChart[];
}
export interface IkidLineChart {
  Age?: number;
  Rate?: string;
}
export interface GlobalStatisticsInputDTO {
  StartDate: string;
  EndDate: string;
  School: EpPropMergeTypeWithNull<string>;
  Grade: EpPropMergeTypeWithNull<string>;
  Class: EpPropMergeTypeWithNull<string>;
  DtoName?: string; //传了dtoName,就返回的学生统计信息，查询报告，使用Name和Phone调用现有查询报告接口
  PageIndex: number;
  PageSize: number;
  Keyword: string;
}
export interface GlobalStatisticItem {
  Id?: string;
  Name?: string;
  Sex?: string;
  Age?: number;
  Phone?: string;
  Birthday?: string;
  School?: string;
  Grade?: string;
  Class?: string;
  IkidReportData?: string;
  FootReportData?: string;
  IkidAnalysis?: string;
  FootAnalysis?: string;
  ReportTime?: string;
  FootAnalysisData?: FootAnalysisData;
  IkidAnalysisData?: IkidAnalysisData;
}
export interface IkidAnalysisData {
  Analysis?: number;
}
export interface FootAnalysisData {
  Left?: number;
  Right?: number;
}

export interface DetailStatisticsInputDTO {
  StartDate: string;
  EndDate: string;
  School: EpPropMergeTypeWithNull<string>;
  Grade: EpPropMergeTypeWithNull<string>;
  Class: EpPropMergeTypeWithNull<string>;
  GroupByAge?: boolean;
}
export interface detailStatisticItem {
  ShowName?: string;
  UserCount?: number;
  AvgAtrMax?: number;
  NotNormalCount?: number;
  NotNormalIkidRate?: number;
  LowUserCount?: number;
  LowRate?: number;
  HighUserCount?: number;
  HighRate?: number;
  NotNormalFootCount?: number;
  NotNormalFootRate?: number;
  LeftHighFootCount?: number;
  LeftHighFootRate?: number;
  LeftMildFootCount?: number;
  LeftMildFootRate?: number;
  LeftModerateFootCount?: number;
  LeftModerateFootRate?: number;
  LeftSevereFootCount?: number;
  LeftSevereFootRate?: number;
  RightHighFootCount?: number;
  RightHighFootRate?: number;
  RightMildFootCount?: number;
  RightMildFootRate?: number;
  RightModerateFootCount?: number;
  RightModerateFootRate?: number;
  RightSevereFootCount?: number;
  RightSevereFootRate?: number;
  BoyUserCount?: number;
  BoyAvgAtrMax?: number;
  BoyNotNormalCount?: number;
  BoyNotNormalIkidRate?: number;
  BoyLowUserCount?: number;
  BoyLowRate?: number;
  BoyHighUserCount?: number;
  BoyHighRate?: number;
  BoyNotNormalFootCount?: number;
  BoyNotNormalFootRate?: number;
  BoyLeftHighFootCount?: number;
  BoyLeftHighFootRate?: number;
  BoyLeftMildFootCount?: number;
  BoyLeftMildFootRate?: number;
  BoyLeftModerateFootCount?: number;
  BoyLeftModerateFootRate?: number;
  BoyLeftSevereFootCount?: number;
  BoyLeftSevereFootRate?: number;
  BoyRightHighFootCount?: number;
  BoyRightHighFootRate?: number;
  BoyRightMildFootCount?: number;
  BoyRightMildFootRate?: number;
  BoyRightModerateFootCount?: number;
  BoyRightModerateFootRate?: number;
  BoyRightSevereFootCount?: number;
  BoyRightSevereFootRate?: number;
  GirlUserCount?: number;
  GirlAvgAtrMax?: number;
  GirlNotNormalCount?: number;
  GirlNotNormalIkidRate?: number;
  GirlLowUserCount?: number;
  GirlLowRate?: number;
  GirlHighUserCount?: number;
  GirlHighRate?: number;
  GirlNotNormalFootCount?: number;
  GirlNotNormalFootRate?: number;
  GirlLeftHighFootCount?: number;
  GirlLeftHighFootRate?: number;
  GirlLeftMildFootCount?: number;
  GirlLeftMildFootRate?: number;
  GirlLeftModerateFootCount?: number;
  GirlLeftModerateFootRate?: number;
  GirlLeftSevereFootCount?: number;
  GirlLeftSevereFootRate?: number;
  GirlRightHighFootCount?: number;
  GirlRightHighFootRate?: number;
  GirlRightMildFootCount?: number;
  GirlRightMildFootRate?: number;
  GirlRightModerateFootCount?: number;
  GirlRightModerateFootRate?: number;
  GirlRightSevereFootCount?: number;
  GirlRightSevereFootRate?: number;
  ReportTime?: string;
  SortReportTime?: number;
}
