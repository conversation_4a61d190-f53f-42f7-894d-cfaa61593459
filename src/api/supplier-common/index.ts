import request from "@/utils/request";
import {
  type detailStatisticItem,
  type DetailStatisticsInputDTO,
  type GlobalStatisticItem,
  type GlobalStatisticsInputDTO,
  type IkidAndFootItem,
} from "./types";

const Supplier_Common_Common = "/supplier-common/api/common";
const Supplier_Common_Api = {
  /** 获取脊柱大屏数据 */
  ikidAndFootStatistics(data: { StartDate: string; EndDate: string }) {
    return request.post<IkidAndFootItem>(`${Supplier_Common_Common}/IkidAndFootStatistics`, data);
  },
  /** 获取总体数据 */
  globalStatistics(data: GlobalStatisticsInputDTO) {
    return request.post<ListDataTotal<GlobalStatisticItem>>(
      `${Supplier_Common_Common}/GlobalStatistics`,
      data
    );
  },
  /** 脊柱学校统计 */
  detailStatistics(data: DetailStatisticsInputDTO) {
    return request.post<detailStatisticItem[]>(`${Supplier_Common_Common}/DetailStatistics`, data);
  },
  /** 获取脊柱的学校列表数据 */
  getSchool() {
    return request.get<{ School: string }[]>(`${Supplier_Common_Common}/GetSchool`);
  },
};

export default Supplier_Common_Api;
