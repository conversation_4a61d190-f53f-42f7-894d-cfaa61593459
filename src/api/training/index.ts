import request from "@/utils/request";
import {
  type TrainingListInputDTO,
  type GetEvaluateGaugeInputDTO,
  type TrainingScheme,
  type TrainingProgram,
  type PatientInfoByProgramId,
  type RoomMsgByProgram,
  type TrainingActionDetailByDate,
  type BindingDeviceInputDTO,
  type FilledPatGaugeInputDTO,
  type FilledPatGaugeItem,
  type PatEvaluateItem,
  type GaugeSchemePageItem,
  type DctPatGauge,
  type GaugeDetail,
} from "./types";

const Training_Gauge = "/training/gauge";
const Training_Scheme = "/training/scheme";
const TRAINING_DEVICE = "/training/device";
const Training_Api = {
  // --------------- gauge ---------------
  /**
   * 获取量表列表
   * @param data
   */
  getEvaluateGaugePage(
    data: GetEvaluateGaugeInputDTO
  ): Promise<ServerResult<ListRowsTotal<BaseGauge>>> {
    return request.post(`${Training_Gauge}/GetEvaluateGaugePage`, data);
  },

  /**
   * 获取量表详情
   * @param id
   */
  getEvaluateGaugeDetail(evaluateId: string): Promise<ServerResult<GaugeDetail>> {
    return request.get(`${Training_Gauge}/GetEvaluateGaugeDetail`, { params: { evaluateId } });
  },

  /**
   * 获取基础数据量表详情
   *
   * @param id 量表id
   */
  getGaugeById(id: string): Promise<ServerResult<GaugeDetail>> {
    return request.get(`${Training_Gauge}/GetGaugeById`, { params: { id } });
  },

  /**
   * 获取引用量表列表
   * @param data
   */
  getReferenceEvaluate(
    data: GetEvaluateGaugeInputDTO
  ): Promise<ServerResult<ListRowsTotal<BaseGauge>>> {
    return request.post(`${Training_Gauge}/GetReferenceEvaluate`, data);
  },

  /**
   * 添加更新量表
   */
  insertOrUpdateGauge(data: GaugeDetail): Promise<ServerResult<void>> {
    return request.post(`${Training_Gauge}/InsertOrUpdateGauge`, data);
  },

  /**
   * 添加更新业务量表
   */
  insertPatEvaluateGauge(data: DctPatGauge): Promise<ServerResult<void>> {
    return request.post(`${Training_Gauge}/InsertPatEvaluateGauge`, data);
  },

  /**
   * 删除量表
   * @param id
   */
  deleteEvaluateGauge(id: string): Promise<ServerResult<void>> {
    return request.post(`${Training_Gauge}/DeleteEvaluateGauge`, { id });
  },

  /**
   * 引用量表
   * @param data
   */
  referenceGauge(data: { Ids: string[]; OrgId: string }): Promise<ServerResult<void>> {
    return request.post(`${Training_Gauge}/ReferenceGauge`, data);
  },

  /**
   * 取消引用
   * @param id
   */
  cancelReferenceGauge(id: string): Promise<ServerResult<void>> {
    return request.post(`${Training_Gauge}/CancelReferenceGauge`, { id });
  },

  /**
   * 推送量表
   * @param data
   */
  pushGauge(data: { gaugeIds: string[]; organizationIds: string[] }): Promise<ServerResult<void>> {
    const params = {
      OrgIds: data.organizationIds,
      Ids: data.gaugeIds,
    };
    return request.post(`${Training_Gauge}/PushGauge`, params);
  },
  /**
   * 获取患者量表详情
   * @param id
   */
  dctGetPatGaugeById(params: { patGaugeId: string }): Promise<ServerResult<DctPatGauge[]>> {
    return request.get(`${Training_Gauge}/DctGetPatGaugeById`, { params });
  },
  /** 获取评估结果 */
  getFilledPatGauge(
    data: FilledPatGaugeInputDTO
  ): Promise<ServerResult<ListDataTotal<FilledPatGaugeItem>>> {
    return request.post(`${Training_Gauge}/GetFilledPatGauge`, data);
  },
  /** 获取评估结果详情 */
  getPatEvaluateById(params: {
    patEvaluateGaugeId: string;
  }): Promise<ServerResult<PatEvaluateItem>> {
    return request.get(`${Training_Gauge}/GetPatEvaluateById`, { params });
  },
  /** 获取评估方案列表 */
  getGaugeSchemePage(data: {
    PageIndex: number;
    PageSize: number;
  }): Promise<ServerResult<ListDataTotal<GaugeSchemePageItem>>> {
    return request.post(`${Training_Gauge}/GetGaugeSchemePage`, data);
  },
  /** 获取评估方案详情 */
  getGaugeSchemeDetailById(params: { id: string }): Promise<ServerResult<GaugeSchemePageItem>> {
    return request.get(`${Training_Gauge}/GetGaugeSchemeDetailById`, { params });
  },
  /** 添加更新评估方案 */
  insertOrUpdateGaugeScheme(data: GaugeSchemePageItem): Promise<ServerResult<null>> {
    return request.post(`${Training_Gauge}/InsertOrUpdateGaugeScheme`, data);
  },
  /** 删除评估方案 */
  deleteGaugeScheme(data: { Id: string }): Promise<ServerResult<null>> {
    return request.post(`${Training_Gauge}/DeleteGaugeScheme`, data);
  },
  // --------------- scheme ---------------
  getTrainingPage(
    data: TrainingListInputDTO
  ): Promise<ServerResult<ListDataTotal<TrainingScheme>>> {
    return request.post(`${Training_Scheme}/GetTrainingPage`, data);
  },
  /**
   * 获取历史方案列表
   */
  getHistoryProgramByPatVisitId(params: {
    visitId: string;
    useScene: number;
  }): Promise<ServerResult<TrainingProgram[]>> {
    return request.get(`${Training_Scheme}/GetHistoryProgramByPatVisitId`, { params });
  },
  /**
   * 获取患者训练信息
   */
  getPatientInfoByProgramId(params: {
    programId: string;
  }): Promise<ServerResult<PatientInfoByProgramId>> {
    return request.get(`${Training_Scheme}/GetPatientInfoByProgramId`, { params });
  },
  /**
   * 获取聊天记录
   */
  getRoomMsgByProgramId(params: { programId: string }): Promise<ServerResult<RoomMsgByProgram>> {
    return request.get(`${Training_Scheme}/GetRoomMsgByProgramId`, { params });
  },
  /**
   * 获取时间段的康复计划
   */
  getTrainingActionDetailByDate(params: {
    programId: string;
    startTime: string;
    endTime: string;
  }): Promise<ServerResult<TrainingActionDetailByDate[]>> {
    return request.get(`${Training_Scheme}/GetTrainingActionDetailByDate`, {
      params,
    });
  },

  /** 获取设备绑定列表 */
  getDeviceList(data: { keywords?: string; pageIndex: number; pageSize: number }) {
    return request.post<ListDataTotal<any>>(TRAINING_DEVICE + "/GetBingDevice", data);
  },
  /** 设备绑定 */
  bindingUserDevice(data: BindingDeviceInputDTO[]) {
    return request.post(`${Training_Scheme}/BindingUserDevice`, data);
  },
  /** 设备解绑 */
  unBindingUserDevice(data: any) {
    return request.post(`${Training_Scheme}/UnBundlingUserDevice`, data);
  },
  /** 判断该用户是否绑定多个相同类型的设备 */
  checkIsBindingUser(
    data: Pick<
      BindingDeviceInputDTO,
      "DeviceCode" | "DeviceTypeCode" | "DeviceFactory" | "MenberId"
    >[]
  ) {
    return request.post<boolean>(`${Training_Scheme}/CheckBindingUserDevice`, data);
  },
};

export default Training_Api;
