import { type EpPropMergeTypeWithNull } from "element-plus";

export interface UserFeedback {
  Id?: string;
  Type?: string;
  Source?: number;
  State?: number;
  Phone?: string;
  CreatedTime?: string;
  Description?: string;
  /**
   * 截图
   * eg: "[\"https://kfxoss-biz-stg.oss-cn-hangzhou.aliyuncs.com/4127336973091871744.jpg\"]"
   */
  Screenshot?: string;
  Result?: null;
  ViewTime?: string;
  Viewer?: string;
  Processor?: string;
  ProcessTime?: string;
  DeletedTime?: string;
  CreatorId?: string;
}

export interface ProcessFeedbackParams {
  Id: string;
  Result: string;
}

export interface GetEvaInputDTO {
  PageIndex: number;
  PageSize: number;
  StartDate: string;
  EndDate: string;
  OrgId: EpPropMergeTypeWithNull<string>;
  DoctorId: EpPropMergeTypeWithNull<string>;
  ConsultWay: EpPropMergeTypeWithNull<number>;
  Keyword: string;
  LoginUserId: string;
  Scopeable: number;
  Type?: number;
}
export interface EvaItem {
  Total?: number;
  Data?: EvaItemData[];
  TagAndScore?: {
    ScoreInfo?: ScoreInfoItem[];
    TagInfos?: TagInfoItem[];
  };
}
export interface TagInfoItem {
  Count?: number;
  Tag?: string;
}
export interface ScoreInfoItem {
  Name?: string;
  Count?: number;
  Type?: number;
  Score?: number;
}
export interface EvaItemData {
  Id?: string;
  DoctorId?: string;
  UserName?: string;
  DoctorName?: string;
  HeadImg?: any;
  Type?: number;
  Age?: string;
  Sex?: string;
  VisitNo?: string;
  PrescriptionId?: number;
  VisitTime?: string;
  DctName?: string;
  OrganizationId?: string;
  OrganizationName?: string;
  Created?: string;
  CreatorId?: string;
  OtherAdvice?: string;
  Phone?: string;
  AverageScore?: number;
  ReadTime?: any;
  ConsultWay?: number;
  DeptId?: string;
  DeptName?: string;
  Kf?: number;
  Fw?: number;
  Zl?: number;
  Wz?: number;
  Tags?: any;
  IsAutomatically?: boolean;
  SatisfactionSurveyDetails?: SatisfactionSurveyItem[];
  SatisfactionTags?: unknown[];
}
export interface SatisfactionSurveyItem {
  Id?: string;
  Score?: number;
  SatisfactionSurveyId?: string;
  EvaluateContentId?: string;
  Type?: number;
}
