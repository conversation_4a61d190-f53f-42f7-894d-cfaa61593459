import request from "@/utils/request";
import {
  type EvaItem,
  type GetEvaInputDTO,
  type UserFeedback,
  type ProcessFeedbackParams,
} from "./types";

const baseUrl = "/satisfactory";
const managerPath = `${baseUrl}/Manager`;
const userEvaluatePath = `${baseUrl}/UserEvaluate`;

const Satisfactory_Api = {
  // --------------- manager ---------------

  /**
   * 查询用户反馈列表
   * @param query 查询用户反馈列表
   * @returns 用户反馈列表
   */
  getPageFeedback(query: DictQueryParams): Promise<ServerResult<ListRowsTotal<UserFeedback>>> {
    const path = `${managerPath}/getPageFeedback`;
    return request.post(path, query);
  },
  /** 获取服务评价列表 */
  getEva(query: GetEvaInputDTO): Promise<ServerResult<EvaItem>> {
    const path = `${userEvaluatePath}/GetEva`;
    return request.post(path, query);
  },

  /**
   * 处理用户反馈
   */
  processFeedback(data: ProcessFeedbackParams): Promise<ServerResult<UserFeedback>> {
    const path = `${managerPath}/ProcessFeedback`;
    return request.put(path, data);
  },

  /**
   * 根据ID获取用户反馈详情
   */
  loadFeedbackById(id: string): Promise<ServerResult<UserFeedback>> {
    const path = `${managerPath}/LoadFeedbackById`;
    return request.get(path, { params: { id } });
  },

  /**
   * 设置用户反馈已读
   */
  setReaded(ids: string[]): Promise<ServerResult<string[]>> {
    const path = `${managerPath}/SetReaded`;
    return request.put(path, ids);
  },
};

export default Satisfactory_Api;
