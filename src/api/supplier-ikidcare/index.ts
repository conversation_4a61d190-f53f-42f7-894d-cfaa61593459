import request from "@/utils/request";
import { type SpineReportPageItem } from "./types";

const Supplier_Ikidcare_Info = "/supplier/ikidcare/api/info";
const Supplier_Ikidcare_Api = {
  getReportPage(data: { Name: string; Phone: string; IsLatest?: boolean }) {
    return request.post<SpineReportPageItem[]>(`${Supplier_Ikidcare_Info}/GetReportPage`, data);
  },
};

export default Supplier_Ikidcare_Api;
