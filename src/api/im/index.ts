import request from "@/utils/request";

const version = "v2";

const IM_Room = `/im${version}/api/room`;

const IM_Api = {
  getHistoryMsg(params: {
    UserId: string;
    Project: string;
    RoomId: string;
  }): Promise<ServerResult<RoomMsg[]>> {
    return request.get(`${IM_Room}/GetHistoryMsg`, { params });
  },
  getRoom(params: { roomId: string }): Promise<ServerResult<RoomInfo>> {
    return request.get(`${IM_Room}/GetRoom`, { params });
  },
};

export default IM_Api;
