import CryptoJS from "crypto-js";
import FileAPI from "@/api/file";
import { type GrassQRCodeInputDTO } from "@/api/other/types";
import { GRASS_API_KEY, GRASS_API_SECRET } from "./constants";
import Other_Api from "@/api/other";

/**
 * 深度移除对象中的 null 和 undefined 值
 *
 * @description
 * - 如果输入为 null，直接返回 null
 * - 如果输入为数组，递归处理每个元素，并过滤掉 null 和 undefined（数字类型除外）
 * - 如果输入为对象，递归处理每个属性，跳过值为 null 或 undefined 的属性
 * - 其他类型直接返回原值
 *
 * @param {any} obj - 需要处理的目标对象
 * @returns {any} 处理后的新对象，移除了所有的 null 和 undefined 值
 *
 * @example
 * // 处理对象
 * deepRemoveNullValues({ a: 1, b: null, c: { d: undefined, e: 2 } })
 * // 返回 { a: 1, c: { e: 2 } }
 *
 * // 处理数组
 * deepRemoveNullValues([1, null, { a: null }, undefined])
 * // 返回 [1, { }]
 */
export function deepRemoveNullValues(obj: any): any {
  if (obj == null) return null;

  if (Array.isArray(obj)) {
    return obj.map(deepRemoveNullValues).filter((item) => item !== null && item !== undefined);
  }

  if (typeof obj === "object") {
    return Object.fromEntries(
      Object.entries(obj)
        .map(([key, value]) => [key, deepRemoveNullValues(value)])
        .filter(([, value]) => value !== null && value !== undefined)
    );
  }

  return obj;
}

/**
 * 获取对象中某个键的String值
 *
 * @param {*} obj 目标对象
 * @param {string} keyField 键
 * @returns {null | string[]}
 */
export function deepGetValueByKey(obj: any, keyField: string): null | string[] {
  if (typeof obj !== "object" || obj === null) return null;

  if (Array.isArray(obj)) {
    const results = obj
      .map((item) => deepGetValueByKey(item, keyField))
      .filter((item) => item !== null)
      .flat();
    return results.length > 0 ? results : null;
  }

  const results = Object.entries(obj).reduce<string[]>((acc, [key, value]) => {
    if (key === keyField) {
      if (typeof value === "string") {
        acc.push(value);
      } else {
        const nestedValues = deepGetValueByKey(value, keyField);
        if (nestedValues) acc.push(...nestedValues);
      }
    } else {
      const nestedValues = deepGetValueByKey(value, keyField);
      if (nestedValues) acc.push(...nestedValues);
    }
    return acc;
  }, []);

  return results.length > 0 ? results : null;
}
export async function getVideoFirstFrame(videoUrl: string | string[]): Promise<string | null> {
  const url = typeof videoUrl === "string" ? videoUrl : videoUrl[0];
  if (!url) return null;
  // 获取视频第一帧的base64
  const data = await getVideoBase64(url);
  if (data) {
    const imageUrl = await putBase64ToServe(data);
    return imageUrl;
  }
  return null;
}
export function putBase64ToServe(file: string): Promise<string | null> {
  const resFile = detectFileType(file);
  const formData = new FormData();
  formData.set("file", resFile);
  return FileAPI.upload(formData)
    .then((data: any) => {
      if (data.Type === 200) {
        const url = data.Data.HostSetting.External + data.Data.PathSetting.Path;
        return url;
      } else {
        return null;
      }
    })
    .catch(() => {
      return null;
    });
}
export function detectFileType(file: string): File {
  const resFile = dataURLtoFile(file, new Date().getTime() + ".png");
  return resFile;
}
// 将base64转换为文件
export function dataURLtoFile(dataUrl: string, filename: string): File {
  const arr = dataUrl.split(",");
  const mimeMatch = arr[0].match(/:(.*?);/);
  if (!mimeMatch) {
    throw new Error("Invalid data URL format");
  }
  const mime = mimeMatch[1];
  const bstr = atob(arr[1]);
  const n = bstr.length;
  const u8arr = new Uint8Array(n);
  for (let i = 0; i < n; i++) {
    u8arr[i] = bstr.charCodeAt(i);
  }
  return new File([u8arr], filename, {
    type: mime,
  });
}
// 获取视频第一帧转化为base64格式图片
export function getVideoBase64(url: string): Promise<string> {
  return new Promise<string>(function (resolve) {
    let dataURL = "";
    const video = document.createElement("video");
    video.setAttribute("crossOrigin", "anonymous"); // 处理跨域
    video.setAttribute("src", url);
    video.setAttribute("width", "400");
    video.setAttribute("height", "400");
    video.setAttribute("preload", "auto");
    video.addEventListener("loadeddata", function () {
      const canvas = document.createElement("canvas");
      const width = video.videoWidth; // 使用 videoWidth 获取实际视频宽度
      const height = video.videoHeight; // 使用 videoHeight 获取实际视频高度
      canvas.width = width;
      canvas.height = height;
      const ctx = canvas.getContext("2d");
      if (ctx) {
        ctx.drawImage(video, 0, 0, width, height); // 绘制canvas
        dataURL = canvas.toDataURL("image/jpeg"); // 转换为base64
        resolve(dataURL);
      }
    });
  });
}

// /**
//  * 创建防抖函数
//  * @param {Function} fun 原始函数
//  * @param {number} wait 延迟执行毫秒数
//  * @param {boolean} immediate  true 表立即执行，false 表非立即执行
//  * @return {Function} 返回 fun 的防抖函数
//  */
// export function debounce(fun: Function, wait: number = 500, immediate: boolean = false): Function {
//   // console.log('debounce create', fun.name);

//   let lastArgs, lastThis, result, timerId, lastCallTime;

//   if (typeof fun !== "function") {
//     throw new TypeError("Expected a function");
//   }

//   const later = function () {
//     // 据上一次触发时间间隔
//     const timeSinceLastCall = +new Date() - lastCallTime;

//     // 被包装函数上次被调用时间间隔 timeSinceLastCall 小于设定时间间隔 wait
//     if (timeSinceLastCall < wait && timeSinceLastCall > 0) {
//       timerId = setTimeout(later, wait - timeSinceLastCall);
//     } else {
//       timerId = null;
//       // 如果设定为immediate===true，因为开始边界已经调用过了此处无需调用
//       if (!immediate) {
//         // console.log('debounce', fun.name, 'invoke');
//         result = fun.apply(lastThis, lastArgs);
//         if (!timerId) lastThis = lastArgs = null;
//       }
//     }
//   };

//   return function () {
//     // console.log('debounce call');
//     lastArgs = arguments;
//     lastThis = this;
//     lastCallTime = +new Date();

//     const callNow = immediate && !timerId;
//     // 如果延时不存在，重新设定延时
//     if (!timerId) timerId = setTimeout(later, wait);
//     if (callNow) {
//       // console.log('debounce', fun.name, 'invoke');
//       result = fun.apply(lastThis, lastArgs);
//       lastThis = lastArgs = null;
//     }

//     return result;
//   };
// }

// /**
//  * 创建节流函数
//  * @param {Function} fun 原始函数
//  * @param {number} limit 节流时间间隔，单位为毫秒
//  * @returns {Function} 返回 func 的节流函数
//  */
// export function throttle(fun, limit) {
//   // 标记是否处于节流状态
//   let inThrottle;
//   return function () {
//     // 获取函数调用时的参数
//     const args = arguments;
//     // 获取函数调用时的上下文
//     const context = this;
//     // 如果当前不处于节流状态
//     if (!inThrottle) {
//       // 调用原始函数，并传入上下文和参数
//       fun.apply(context, args);
//       // 进入节流状态
//       inThrottle = true;
//       // 在指定的时间间隔后，解除节流状态
//       setTimeout(() => (inThrottle = false), limit);
//     }
//   };
// }
/**
 * 验证草料二维码参数
 * @param params 草料二维码输入参数
 * @returns 验证结果，如果验证失败返回错误信息
 */
function validateGrassQRCodeParams(params: GrassQRCodeInputDTO): string | null {
  // 验证params是否是对象
  if (typeof params !== "object" || !params) {
    return "创建二维码参数请参照GrassQRCodeInputDTO";
  }
  if (!params.cliT) {
    return "cliT是必填字段";
  }
  if (!params.cliD) {
    return "cliD是必填字段";
  }
  return null;
}

/**
 * 创建错误响应
 * @param message 错误消息
 * @returns 标准错误响应格式
 */
function createErrorResponse(message: string): ServerResult<string | null> {
  return {
    Type: 400,
    Data: null,
    Content: message,
  };
}

/**
 * 创建成功响应
 * @param data 响应数据
 * @returns 标准成功响应格式
 */
function createSuccessResponse(data: string | null): ServerResult<string | null> {
  return {
    Type: 200,
    Data: data,
    Content: "执行操作成功",
  };
}

/**
 * 生成草料API签名
 * @param params 请求参数
 * @returns 包含签名的完整参数对象
 */
function generateGrassApiSignature<T extends Record<string, any>>(params: T): T & { sign: string } {
  const sortedParams = sortObjectByKey(params);
  const queryString = objectToQueryString(sortedParams);
  const sign = getMD5(queryString + GRASS_API_SECRET);
  return { ...params, sign };
}

/**
 * 获取草料二维码
 * @param params 参数
 * @param isUploadOss 是否上传到oss
 * @returns 二维码图片url base64 或者 https 地址
 */
export const getGrassQRCodeUrl = async (
  params: GrassQRCodeInputDTO,
  isUploadOss?: boolean
): Promise<ServerResult<string | null>> => {
  // 参数验证
  const validationError = validateGrassQRCodeParams(params);
  if (validationError) {
    return createErrorResponse(validationError);
  }

  // 构建请求参数
  const defaultParams = {
    api_key: GRASS_API_KEY,
    return_file: "base64",
    ...params,
  };

  try {
    // 生成签名
    const signedParams = generateGrassApiSignature(defaultParams);

    // 调用API
    const res = await Other_Api.getGrassQRCode(signedParams);
    if (typeof res.Data !== "string") {
      return createErrorResponse(res.Data.message);
    }

    // 处理成功响应
    if (isUploadOss) {
      const imageUrl = await putBase64ToServe(res.Data);
      return createSuccessResponse(imageUrl);
    }

    return createSuccessResponse(res.Data);
  } catch (error) {
    console.error(error);
    return createErrorResponse("制作二维码失败，请稍后重试");
  }
};
/**
 * 获取MD5加密之后的值
 */
function getMD5(str: string): string {
  if (!str) {
    throw new Error("MD5加密的值不能为空");
  }
  return CryptoJS.MD5(str).toString();
}
/** 将对象进行按照key的字典序排列 */
function sortObjectByKey<T extends Record<string, any>>(obj: T): T {
  const sortedEntries = Object.entries(obj).sort(([keyA], [keyB]) => keyA.localeCompare(keyB));
  const sortedObj = {} as T;

  for (const [key, value] of sortedEntries) {
    sortedObj[key as keyof T] = value;
  }
  return sortedObj;
}
/** 将对象转化为&拼接的字符串 */
function objectToQueryString(obj: Record<string, any>): string {
  return Object.entries(obj)
    .map(([key, value]) => `${key}=${value}`)
    .join("&");
}
