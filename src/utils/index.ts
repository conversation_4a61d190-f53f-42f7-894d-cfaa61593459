/**是否调试模式 */
import dayjs from "dayjs";
import Decimal from "decimal.js";

export const kDebug = import.meta.env.DEV;

/**
 * Check if an element has a class
 */
export function hasClass(ele: HTMLElement, cls: string): boolean {
  return !!ele.className.match(new RegExp("(\\s|^)" + cls + "(\\s|$)"));
}

/**
 * Add class to element
 * @param {HTMLElement} ele
 * @param {string} cls
 */
export function addClass(ele: HTMLElement, cls: string) {
  if (!hasClass(ele, cls)) ele.className += " " + cls;
}

/**
 * Remove class from element
 * @param {HTMLElement} ele
 * @param {string} cls
 */
export function removeClass(ele: HTMLElement, cls: string) {
  if (hasClass(ele, cls)) {
    const reg = new RegExp("(\\s|^)" + cls + "(\\s|$)");
    ele.className = ele.className.replace(reg, " ");
  }
}

/**
 * 判断是否是外部链接
 */
export function isExternal(path: string): boolean {
  const isExternal = /^(https?:|http?:|mailto:|tel:)/.test(path);
  return isExternal;
}

/**
 * 格式化增长率，保留两位小数 ，并且去掉末尾的0  取绝对值
 *
 * @param growthRate
 * @returns
 */
export function formatGrowthRate(growthRate: number) {
  if (growthRate === 0) {
    return "-";
  }

  const formattedRate = Math.abs(growthRate * 100)
    .toFixed(2)
    .replace(/\.?0+$/, "");
  return formattedRate + "%";
}

export function listGroupByKey<T extends Record<K, string>, K extends keyof T>(
  arr: T[],
  key: K
): GroupedItem<T>[] {
  const types: Record<string, GroupedItem<T>> = {};
  const result: GroupedItem<T>[] = [];

  for (const item of arr) {
    const keyValue = item[key];
    if (!(keyValue in types)) {
      types[keyValue] = {
        Type: keyValue,
        Data: [],
      };
      result.push(types[keyValue]);
    }
    types[keyValue].Data.push(item);
  }

  return result;
}

/** 使用Decimal计算金额 加减乘除 保留两位小数 最终return 一个Number类型 */
export function calculateAmount(prices: (number | string)[], operator: "+" | "-" | "*" | "/") {
  if (!prices || prices.length === 0) {
    return 0;
  }

  // 过滤掉空字符串和非法值，并转换为Decimal
  const validPrices = prices.filter(
    (price) => price !== "" && price !== null && price !== undefined && !Number.isNaN(Number(price))
  );

  if (validPrices.length === 0) {
    return 0;
  }

  let result = new Decimal(validPrices[0]);

  for (let i = 1; i < validPrices.length; i++) {
    const current = new Decimal(validPrices[i]);

    switch (operator) {
      case "+":
        result = result.plus(current);
        break;
      case "-":
        result = result.minus(current);
        break;
      case "*":
        result = result.times(current);
        break;
      case "/":
        if (current.equals(0)) {
          throw new Error("Division by zero is not allowed");
        }
        result = result.dividedBy(current);
        break;
    }
  }

  // 保留两位小数并转换回 number 类型
  return Number(result.toDecimalPlaces(2));
}

/**
 * 生成UUID
 * @returns
 */
export function generateUUID(): string {
  return "xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g, (char) => {
    const rand = (Math.random() * 16) | 0; // 生成 0~15 的随机数
    const value = char === "x" ? rand : (rand & 0x3) | 0x8; // 确保 'y' 位置符合 UUID 规则
    return value.toString(16);
  });
}
/** 将base64转化为文件 并且上传到服务器获取地址 */
export function base64ToFile(base64: string, fileName: string): FormData {
  const arr = base64.split(",");
  const mimeMatch = arr[0].match(/:(.*?);/);
  if (!mimeMatch) {
    throw new Error("Invalid base64 format");
  }
  const mime = mimeMatch[1];
  const bstr = atob(arr[1]);
  let n = bstr.length;
  const u8arr = new Uint8Array(n);
  while (n--) {
    u8arr[n] = bstr.charCodeAt(n);
  }
  const file = new File([u8arr], fileName, { type: mime });
  const formData = new FormData();
  formData.set("file", file);
  return formData;
}
export const getDecimalNum = (num: number | string) => {
  return new Decimal(Number(num));
};

/** 格式化时间
 * @param time 时间
 * @param format 格式 默认是YYYY-MM-DD HH:mm:ss
 * @returns 格式化后的时间
 */
export const formatTime = (time: string | Date | number, format = "YYYY-MM-DD HH:mm:ss") => {
  return dayjs(time).format(format);
};

/** 格式化日期
 * @param time 时间
 * @param format 格式 默认是YYYY-MM-DD
 * @returns 格式化后的日期
 */
export const formatDate = (time: string | Date | number, format = "YYYY-MM-DD") => {
  return dayjs(time).format(format);
};
