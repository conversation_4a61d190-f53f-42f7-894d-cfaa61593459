import { kDebug } from ".";

export const DEFAULT_DYNAMIC_CONFIG: DynamicConfig = Object.freeze({
  keywordRegexp: "^[*+()（）.,，\u4e00-\u9fbb°a-zA-Z0-9_-]*$",
  protectionEnable: false,
});

export const RESPONSE_400_MESSAGE_MAPPERS = Object.freeze([
  { reg: new RegExp("^Invalid value of"), message: "参数无效" },
]);

export const KEYWORD_FIELD = "Keyword";
export const DEFAULT_KEYWORD_REGEXP = new RegExp("^[*+()（）.,，\u4e00-\u9fbb°a-zA-Z0-9_-]*$");
export const KEYWORD_WARN_TEXT = "关键字不能包含特殊字符";
export const GRASS_API_KEY = "CLdd532e49d7a29a4a";
export const GRASS_API_SECRET = "6ef77c1d2d0b2c12c17d68d936e6c354";

/**自定义Header */
export const CUSTOM_HEADERS = Object.freeze({
  "hwkj-custom-client": import.meta.env.VITE_APP_CLIENT_ID,
  "hwkj-custom-clientVersion": kDebug ? "debug" : "2.0.0",
});
