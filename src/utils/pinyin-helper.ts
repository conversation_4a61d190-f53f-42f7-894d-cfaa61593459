// pinyin 这个包打包后有6M大小，gzip 压缩后有 2M 大小
// 所以这里单独抽离出来实现动态加载，避免打开页面时加载时间过长
import pinyin from "pinyin";

/** 中文转拼音首字母 */
export function chineseToPinyin(text: string): string {
  if (!text) return "";
  // console.log("text", text);

  const pinyinArr = pinyin(text, {
    style: pinyin.STYLE_FIRST_LETTER, // 获取拼音的首字母
    heteronym: false, // 是否考虑多音字
    segment: true,
  });
  return pinyinArr.map((item: string[]) => item[0].toUpperCase()).join("");
}
