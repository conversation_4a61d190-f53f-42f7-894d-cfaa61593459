import { type UnwrapRef } from "vue";

export type DialogAction = "add" | "edit" | "view";

interface UseActionDialogReturn<T> {
  dialogModel: {
    visible: boolean;
    action: DialogAction | null;
    data: UnwrapRef<T> | null;
  };
  dialogTitle: ComputedRef<string>;
  dialogCloseable: ComputedRef<boolean>;
}

/**
 * 操作对话框
 * @param titles 对话框标题，key 为 DialogAction，value 为对话框标题
 */
export function useActionDialog<T>(titles: Record<DialogAction, string>): UseActionDialogReturn<T>;

/**
 * 操作对话框
 * @param title 对话框标题，如果传入，则使用操作+传入值作为显示的标题，否则使用默认标题
 */
export function useActionDialog<T>(title?: string): UseActionDialogReturn<T>;

export function useActionDialog<T>(
  titleOrTitles?: Record<DialogAction, string> | string
): UseActionDialogReturn<T> {
  const defaultTitles = {
    add: "新增",
    edit: "编辑",
    view: "查看",
  };

  const dialogModel = reactive({
    visible: false,
    action: null as DialogAction | null,
    data: null as T | null,
  });

  /**
   * 对话框标题
   */
  const dialogTitle = computed(() => {
    if (!dialogModel.action) {
      return "";
    }

    if (!titleOrTitles) {
      return defaultTitles[dialogModel.action as DialogAction] || "";
    }

    if (typeof titleOrTitles === "string") {
      return (defaultTitles[dialogModel.action as DialogAction] || "") + titleOrTitles;
    }

    return (
      titleOrTitles?.[dialogModel.action as DialogAction] ||
      defaultTitles[dialogModel.action as DialogAction] ||
      ""
    );
  });

  /**
   * 对话框是否可关闭
   */
  const dialogCloseable = computed(() => {
    return dialogModel.action === "view";
  });

  return {
    dialogModel,
    dialogTitle,
    dialogCloseable,
  };
}
