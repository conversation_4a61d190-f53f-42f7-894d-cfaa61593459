import { type PageBaseChargeItemType } from "@/api/content/types";

declare global {
  interface ReadDict {
    Id?: string;
    DictId?: number;
    /** 字典项名称 */
    Key?: string;
    Value?: string;
    OrgId?: string;
    DepartmentId?: string | null;
    IsPublish?: boolean;
    Updatable?: boolean;
    Deletable?: boolean;
    PinyinCode?: string;
    OrderNumber?: number;
    CustomSort?: number;
    Remark?: string;
    IsEnabled?: boolean;
    IsDefaultPush?: boolean;
    CreatedTime?: string;
    ParentId?: string;
    DictItemRelateds?: DictItemRelated[];
    Children?: ReadDict[];
  }
  interface DictItemRelated {
    DictItemId?: string;
    DictItemIdA: string;
  }
  /**
   * 字典
   */
  interface DictCode {
    Code?: string;
    Id?: string;
    Name?: string;
    TypeCode?: string;
  }

  interface SortCondition {
    SortField: string;
    ListSortDirection: number;
  }

  interface Rule {
    Field: string;
    Value: string | number | boolean | null;
    Operate: number;
  }

  interface Group {
    Rules: Rule[];
    Operate: number;
  }

  interface FilterGroup {
    Rules?: Rule[];
    Groups?: Group[];
    Operate?: number;
  }

  interface PageCondition {
    PageIndex?: number;
    PageSize?: number;
    SortConditions?: SortCondition[];
  }

  interface DictQueryParams {
    PageCondition?: PageCondition;
    FilterGroup: FilterGroup;
  }
  interface MoItemTypeList {
    Id: string;
    IsShow: boolean;
    Name: string;
    OrganizationId: string;
    Value: number;
  }

  interface BaseRxMoItem {
    CreatorId?: string;
    Freq?: number;
    FreqDay?: number;
    Id?: string;
    IsSpecialFreq?: boolean;
    LogisticsDay?: number;
    Manufacturer?: number;
    MoDay?: number;
    MoItemChargeMode?: number;
    MoItemId?: string;
    MoItemMedia?: string;
    MoItemMethod?: number;
    MoItemUseScope?: number;
    MoMonth?: number;
    MoName?: string;
    MoRemark?: string;
    OriginalPrescriptionId?: string;
    PackId?: string;
    Part?: number;
    PrescriptionId?: string;
    Price?: number;
    Sort?: number;
    TotalCount?: number;
  }

  interface ActionUnit {
    /** 内容ID */
    ContentId: string;
    /** 使用范围 */
    UseScope: number;
    /** 审批状态 */
    ApproveState?: number;
    /** 动作名称 */
    Name: string;
    /** 别名 */
    AliasName?: string;
    /** 参考信息 */
    Reference?: string;
    /** 编码 */
    Code: string;
    /** 拼音码 */
    PinyinCode: string;
    /** 创建者 */
    Creator: string;
    /** 创建时间 */
    CreatedTime: string;
    /** 机构 */
    Organization: string;
    /** 是否启用 */
    Enable: boolean;
    /** 类型 */
    Type: number;
    /** 频率 */
    Freq: number;
    /** 功能障碍ID列表 */
    Dysfunction: string[];
    /** 疾病字典ID列表 */
    DiseaseDicts: string[];
    /** 疾病字典名称 */
    DiseaseDictName: string;
    /** 使用次数 */
    UseNum: number;
    /** 制造商 */
    Manufacturer?: string;
    /** 制造商类型 */
    MFType?: number;
    /** 动作单元图片URL */
    ActionUnitImgURL: string;
  }
  interface ChargeItem {
    PackId?: string;
    MoItemId: string;
    PackChargeItemDetails: PageBaseChargeItemType[];
  }
  interface ConsumablesType {
    Id: string;
    Name: string;
    PYM: string;
    Code: string;
    IsEnable: boolean;
    CreateTime?: string; // 可以考虑使用 Date 类型，如果需要处理日期
    CreatorId?: string;
    IsDeletable?: boolean;
    OrganizationId?: string | null; // 允许为 null
  }
  interface BaseConsumables {
    Id: string;
    Name: string;
    Type: string;
    PYM: string;
    Code: string;
    Spec: string;
    PackUnit: string;
    PackRatio: number;
    IsEnable: boolean;
    CreatedTime?: string;
    CreatorId?: string;
    Urls: string[];
  }
  interface FileResult {
    HostSetting: {
      External: string;
      Internal: string;
    };
    PathSetting: {
      Path: string;
      Transform: string;
    };
  }

  /**
   * 收费项目
   */
  export interface BaseChargeItemType {
    Code?: string;
    Id: string;
    IsEnabled: boolean;
    Name: string;
    Price: number;
    Unit: string;
    Amount: number;
    Quantity?: number;
  }
  /** 医嘱详情 */
  export interface BaseMoItemData {
    TreatInfos?: {
      OrgId: string;
      OrgName: string;
    }[]; // 可以根据实际情况更改类型
    Id?: string;
    Remark: string;
    Name: string;
    AliasName: string;
    PYM: string;
    Code: string;
    ChargeMode: number;
    LogisticsDay: number;
    MoType: number;
    MaxAmount: number;
    MinAmount: number;
    ShowAmount: number;
    OrganizationId?: string; // 可以根据实际情况更改类型
    OrganizationName?: string; // 可以根据实际情况更改类型
    MinDay: number;
    MaxDay: number;
    IsEnable: boolean;
    IsUseConsumable: boolean;
    Consumables?: string[]; // 可以根据实际情况更改类型
    IsDefaultPush: boolean;
    ConsumablesOutputDtos?: BaseConsumables[]; // 可以根据实际情况更改类型
    DefaultTrainingItem?: string;
    DefaultActionUnit?: ActionUnit;
    ChargeItems?: ChargeItem[];
    MoItemAmount?: number;
    CreatedTime?: string;
    CreatorId?: string;
    DeletedTime?: string; // 可以根据实际情况更改类型
    MoItemUseScope: number;
    BeReferencedNum?: number;
    IsSpecialFreq: boolean;
    MoItemMethod: number;
    MoItemVideoUrl: string;
    Reference?: string; // 可以根据实际情况更改类型
    Explain: string;
    Media: string[];
    Visibility: number;
    Scene: number;
    DoctorVisibility?: boolean;
    TherapistVisibility?: boolean;
    TherapistByDoctorVisibility?: boolean;
    NurseVisibility?: boolean;
    NurseByDoctorVisibility?: boolean;
    ConsultScene?: boolean;
    PatientManagerScene?: boolean;
    HaveConsultServe: boolean;
    DefaultMoDay: number;
    Manufacturer: number;
    DeptIds?: string[]; // 可以根据实际情况更改类型
    DeptDTO?: {
      Code: string;
      DeptType: string;
      DeptTypeId: unknown;
      Id: string;
      Name: string;
      StandardName: string;
    }[]; // 可以根据实际情况更改类型
    SortTime?: string;
    Treats: string[]; // 可以根据实际情况更改类型
    Tags: string[];
    Catelog: string[];
  }
  /** 诊断 */
  export interface BaseDiagnosis {
    DiagnoseTypeName: string;
    DiagnoseName: string;
    DiagnoseCode: string;
    IsMain: boolean;
    Sort?: number;
  }

  /** 穴位模板 */
  export interface BaseAcuPointTemplate {
    Id?: string;
    ObjectId?: string;
    Name?: string;
    Code?: string;
    MoItemIds?: string[];
    MoItems?: BaseMoItemData[];
    OrganizationId?: string;
    IsEnable?: boolean;
    AcuPointIds?: string[];
    AcuPointInfos?: AcuPointInfo[];
    CreateTime?: string;
    DeleteTime?: string;
    CreatorId?: string;
  }

  /** 穴位 */
  export interface AcuPointInfo {
    AcuPointId?: string;
    Name: string;
    AcuPointCount: number;
    IsDouble: boolean;
    RxDetailTemplateId?: string; // 编辑的时候才需要传递
  }
  export interface BaseAcuPoint {
    Id?: string;
    ObjectId?: string;
    Name?: string;
    PYM?: string;
    Code?: string;
    AcuPointTypeId?: string;
    AcuPointImg?: string[];
    Remark?: string;
    IsEnable?: boolean;
    IsDouble?: boolean;
    CreateTime?: string;
    CreatorId?: string;
    OrganizationId?: string;
    AcuPointCount?: number;
  }
  /** 宣教 */
  export interface BaseRecoveryMission {
    ContentId?: string;
    /** 标题 */
    Title?: string;
    /** 富文本 */
    Text?: string;
    /** 宣教类型Id */
    RecoveryMissionType?: string;
    /** 宣教类型名称 */
    RecoveryMissionTypeName?: string;
    AuthorName?: string;
    ChannelId?: string;
    Enable: boolean;
    /** 宣教来源（null 表示原创） */
    Source?: string;
    Reference?: string;
    Summary?: string;
    Organization?: string;
    /** 疾病Id */
    Diseases?: string[];
    IsRecommend: boolean;
    CreatedTime?: string;
    CreatorName?: string;
    Sort?: number;
    ShowImg?: string;
    IsDefaultPush: boolean;
    Depts?: string[];
    DeptName?: string;
    CreatorId?: string;
    /** 音频 */
    Media?: string[];
  }

  /** 宣教类型 */
  export interface RecoveryMissionType {
    Code?: string;
    Depth?: number;
    Enable?: boolean;
    Id?: string;
    Name?: string;
    OrganizationId?: string;
    ParentId?: string;
    Children?: RecoveryMissionType[];
  }

  /** 根据某个字段对数组进行分组 */
  export interface GroupedItem<T> {
    Type: string;
    Data: T[];
  }
  /** 设备参数 */
  export interface BaseInstrument {
    Id: string;
    Name: string;
    PYM: string;
    OriginalDataId?: string;
    OrganizationId?: string;
    Code: string;
    IsIOTDevice: boolean;
    DeviceType: number;
    DeviceFactory: string;
    IsEnable: boolean;
    IsPublish: boolean;
    InstrumentRentData: string;
    InstrumentDepositData: string;
    Parameters: string; // Assuming this is a JSON string
    CreatedTime: string;
    DeletedTime: string;
    InstrumentImg: unknown;
    CreaterId: string | null;
    InstrumentId: string;
    Amount: number;
    RentDataMoney: number;
    RentDataMoneyInfos: unknown[];
    DepositDataMoney: number;
    DepositDataMoneyInfos: unknown[];
  }
  export interface BaseInstrumentParameter {
    Name: string;
    Type: number;
    Value: string | number;
    OptionValue?: {
      Key: string;
      Value: string | number;
    }[];
    ValueLabel?: string;
    Unit: string;
    SignCode?: string;
    NotShow?: boolean;
    Min?: number;
    Max?: number;
    Key?: string;
  }

  export interface BaseTask {
    Id: string;
    FileName: string;
    TaskFinishedTime?: string;
    DownloadTime?: string;
    Status: number;
    CreatedTime: string;
  }
  export interface BaseBankInfo {
    BankCode: string;
    BankId: string;
    BankName: string;
    NeedBranch: string;
    AccountBank: string;
    BankCardNum: string;
  }
  export interface RedashParamsInputDTO<T> {
    queryName: string;
    maxAge: number;
    JobWaitingMs: number;
    pageIndex: number;
    PageStart?: number;
    PageSize?: number;
    pageSize: number;
    parameters: T;
  }

  type RedashParameters<T> = T & {
    pageIndex: number;
    pageSize: number;
  };

  export interface BaseAddressInfo {
    count: string;
    geocodes: {
      adcode: string;
      city: string;
      citycode: string;
      country: string;
      district: string;
      formatted_address: string;
      level: string;
      location: string;
      province: string;
    }[];
    info: string;
    infocode: string;
    status: string;
  }

  interface OrderAddress {
    Address?: string;
    City?: string;
    CityName?: string;
    County?: string;
    CountyName?: string;
    Name?: string;
    Province?: string;
    ProvinceName?: string;
    Tel?: string;
  }

  interface PageBaseUserProfile extends BaseUserProfile {
    UserRole?: string;
  }

  interface RoomMsg {
    Id: string; // 消息 ID
    FromUserId: string; // 发送者用户 ID
    FromUserGuid: string; // 发送者用户 GUID
    FromUser: RoomMsgFromUser; // 发送者用户信息
    TargetId: string; // 目标 ID
    TargetRoom: string | null; // 目标房间，允许为 null
    Msg: string; // 消息内容
    MsgExp: string; // 消息扩展信息（JSON 字符串）
    MsgType: string; // 消息类型
    SendDate: number; // 发送日期（时间戳）
    Members: RoomMsgMember[]; // 成员数组
  }
  interface RoomMsgObject {
    CustomType: number;
    Body: {
      assessId: string; // 评估 ID
      assessName: string; // 评估名称
      assessSign: string; // 评估标识
      userName?: string;
      userNames?: string[];
      actionName?: string;
      visitId: string | null; // 访问 ID，允许为 null
      consultId: string; // 咨询 ID
      patientId: string; // 患者 ID
      sendDateTime: string; // 发送日期时间
      image: string; // 宣教
      name: string; // 宣教名称
      message: string; // 消息
      content?: string;
      title?: string;
    };
  }
  interface RoomInfo {
    Id: string; // 房间 ID
    Pwd: string; // 密码
    Project: string; // 项目 ID
    Type: string; // 房间类型
    RoomName: string; // 房间名称
    CreateUserId: string; // 创建者用户 ID
    CreateUserGuid: string; // 创建者用户 GUID
    CreateUser: RoomMsgFromUser; // 创建者用户信息
    RoomImage: string; // 房间图片 URL
    CreateDate: number; // 创建日期（时间戳）
    ExpInfo: {
      type: string; // 类型
      visitId: string | null; // 访问 ID，允许为 null
      consultId: string; // 咨询 ID
      visitUserId: string; // 访问用户 ID
      referenceId: string; // 参考 ID
    }; // 扩展信息
    RoomInfo: {
      State: string | null; // 房间状态，允许为 null
      Rules: string | null; // 房间规则，允许为 null
      RuleHandles: unknown[]; // 规则处理数组，根据实际情况定义
    }; // 房间信息
    Members: RoomInfoMember[]; // 成员数组
  }
  export interface RoomInfoMember {
    UserId: string; // 成员用户 ID
    UserGuid: string; // 成员用户 GUID
    User: RoomMsgFromUser; // 用户信息
    RoleType: number; // 角色类型
    NickName: string; // 昵称
    InviteUserId: string; // 邀请用户 ID
    ExpContent: {
      WorkRole: string; // 工作角色
    };
  }
  export interface RoomMsgFromUser {
    Id: string; // 用户 ID
    Project: string; // 项目 ID
    UserId: string; // 用户 GUID
    RegisterDate: number; // 注册日期（时间戳）
    UserHeadImage: string; // 用户头像 URL
    UserName: string; // 用户名
    UserType: string; // 用户类型
  }
  export interface RedashResult<T> {
    Data: T[];
    ErrorMsg: string;
    QueryName: string;
    QueryResultId: number;
    Success: boolean;
    TotalCount: number;
  }

  interface TagsItem {
    Tags?: string[];
    Text?: string;
  }
  interface WarehouseItem {
    Warehouse: string;
    GoodsType: any[];
    WarehouseAddress: string;
    Address: string;
    Picking: WarehousePicking[];
    SendOutGoods: WarehousePicking[];
    Recovery: WarehousePicking[];
    RecoveryOrder: WarehousePicking[];
    OrderRecord: WarehousePicking[];
    Ccto: unknown[];
  }
  interface WarehousePicking {
    Name: string;
    UserName: string;
    Status: number;
    Type: number;
  }

  interface BaseUserProfile {
    Id: string;
    PhoneNumber?: string;
    Name: string;
    NickName?: string;
    UserName?: string;
    HeadImg?: string;
    IsSystem?: boolean;
    CreatedTime?: string;
    Code?: string;
    Birthday?: null;
    Sex?: string;
    IsEnabled?: boolean;
    IsLocked?: boolean;
    UserNameUpdateTime?: null;
    ChangePasswordTime?: null;
    Organization?: BaseOrganization;
    Department?: BaseDepartment;
    Consortiums?: null;
    Roles?: { Name: string; Id: string; RoleType: string }[] | string[];
    UserExternalIdentify?: {
      WeChatQrCode: string;
    };
    Age?: number;
    UserWork?: UserProfileUserWork;
    UserCertificates?: UserCertificate[];
    WorkerType?: string;
    WorkerTitle?: string;
  }

  interface UserProfileUserWork {
    ClinicalWorkYears: string;
    ProfileDescription: string;
    QrCode: string;
  }

  interface BaseRole {
    Code?: string;
    CreatedTime?: string;
    Id?: string;
    IsAdmin?: boolean;
    IsDefault?: boolean;
    IsEnabled?: boolean;
    IsLocked?: boolean;
    IsSystem?: boolean;
    Name?: string;
    PinyinCode?: string;
    Remark?: string;
    RoleType?: string;
  }

  interface BaseDoctor {
    PracticeOrganizationName: string;
    DoctorFirstAuthTime: string;
    UserId: string;
    Name: string;
    Skilled: string;
    HeadImg: string;
    WorkerTitle: string;
    OrganizationName: string;
    DepartmentName: string;
    OrganizationId: string;
    RoleTypes: string[];
    Followed: boolean;
    FollowedTime?: string;
    Sex?: string;
  }

  interface UserCertificate {
    CertificateType: string;
    CertificateValue: string;
    UserId?: string;
  }

  interface ExecutionPointer {
    Prompt?: string;
    /**
     * 流程节点说明：
     * {
     *   当 Node: DoctorAuthentication_Submit  医生提交审核流程
     *   且：
     *     Status = 2 标识医生审核通过
     * }
     * {
     *   当 Node: DoctorAuthentication_Hospital  医院审核流程
     *   且：
     *     Status = 0 标识医院待审核 （操作：医院可以通过、拒绝,拒绝需要填写拒绝原因；）
     *     Status = 2 标识医院审核通过 （操作：1.医院可以在用户管理中注销该用户）
     *     Status = 3 标识医院审核拒绝 （操作：医院可以撤回，撤回后为医院待审核状态；）
     * }
     * {
     *   当 Node: DoctorAuthentication_Platform 平台审核流程
     *   且：
     *     Status = 0 标识平台待审核 （操作：医院可以撤回，撤回后为医院待审核状态，不显示到平台审核中；）
     *     Status = 2 标识平台审核通过 （操作：2.平台和医院可以在用户管理中禁用该用户；）
     *     Status = 3 标识平台审核拒绝 （操作：平台可以撤回，撤回后为平台待审核状态；）
     * }
     */
    Node?: string;
    ExecTime?: string;
    Status?: number;
    Reason?: string;
    Operator?: string;
  }

  interface UserClaim {
    UserId?: string;
    ClaimType?: string;
    ClaimValue?: string;
    Remark?: string;
  }

  interface UserPower {
    globalId?: string;
    organizationId?: string;
    dtos?: null;
  }

  interface UserWork {
    /** 性格描述 */
    Personality?: string;
    /** 地址 */
    InfoAddress?: string;
    /** 证书是否完整 */
    IsCertificatesComplete?: boolean;
    SourceChannel?: string;
    Questions?: string;
    ProjectIntention?: number;
    Region?: string;
    ClinicalWorkYears?: number;
  }

  interface BaseOrganization {
    Id?: string;
    Name?: string;
    Code?: string;
    PinyinCode?: string;
    Remark?: string;
    IsLocked?: boolean;
    IsEnabled?: boolean;
    IsDefault?: boolean;
    IsTechExchange?: boolean;
    Address?: string;
    City?: EpPropMergeTypeWithNull<string>;
    CityCode?: EpPropMergeTypeWithNull<string>;
    ProvinceCode?: string;
    ProvinceName?: string;
    CountryCode?: string;
    CountryName?: string;
    Phone?: string;
    HeadImg?: string;
    SignAContractTime?: string;
    AssistantQrCode?: string;
    QrCode?: string;
    LatLon?: string;
    OrganizationPersonnel?: {
      AssistantId?: string;
      AssistantName?: string;
      BackendAssistantId?: string;
      BackendAssistantName?: string;
      ManageLeaderId?: string;
      ManageLeaderName?: string;
      SaleLeaderId?: string;
      SaleLeaderName?: string;
      CreatedTime?: string;
    };
    CityName?: string;
    Category?: number;
    Alias?: string;
    CreatedTime?: string;
    Updatable?: boolean;
    Deletable?: boolean;
    IsTreatment?: boolean;
    Work?: string;
    Carousel?: {
      ImgUrl?: string;
      SortNumber?: string;
      Title?: string;
      Url?: string;
    }[];
    Level?: number;
    OrganizationConsortiums?: {
      ConsortiumId?: string;
      ConsortiumName?: string;
      Type?: number;
    }[];
    ContractStartTime?: string;
    ContractEndTime?: string;
    AdminPhone?: string;
    CustomerServicePhone?: string;
    OrgRegisterNum?: string;
    CertificateImg?: string;
    LegalPerson?: string;
    SignAContractTime?: string;
    PlatformContractPerson?: string;
    HosContractPerson?: string;
    IsReferral?: boolean;
    IsTreatmentBooking?: boolean;
    TreatmentBookingDescribe?: string;
  }

  interface BaseDepartment {
    Id?: string;
    Name?: string;
    IsMain?: boolean;
    IsEnabled: boolean;
    Code?: string;
  }

  interface BaseGauge {
    Id?: string;
    Code?: string;
    Name?: string;
    Remark?: string;
    IsEnble?: boolean; // 注意：这里的属性名可能是一个拼写错误，应该是 IsEnable
    IsDefaultPush?: boolean;
    CreatedTime?: string;
    CreatorId?: string;
    OrganizationId?: string;
    OrganizationName?: string;
    Reference?: string;
    Type?: number;
    Types?: string;
  }
}
export {};
