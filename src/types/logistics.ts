declare global {
  /** 订单快递 */
  interface OrderExpress {
    OrderNo?: string;
    ExpressNum?: string;
    ExpressComCode?: string;
    ExpressComName?: string;
    Remark?: string;
    /**
     * 物流订单类型（0=发货  1=退货）
     */
    Type?: number;
  }

  /** 快递信息 */
  interface ExpressInfo {
    IsCheck?: boolean;
    Message?: string;
    CompanyCode?: string;

    /** 快递公司 */
    Company?: string;

    /** 快递单号 */
    ExpressNum?: string;

    /** 快递轨迹详情 */
    Track?: ExpressTrack[];
    RouteInfo?: ExpressRouteInfo;
  }

  interface ExpressTrack {
    Time?: string;
    FTime?: string;

    /** 详细内容 */
    Context?: string;

    /** 当前轨迹所在地编码 */
    AreaCode?: string;

    /** 当前轨迹所在地 */
    AreaName?: string;

    /** 当前轨迹状态 */
    Status?: string;

    /** 定位城市 */
    Location?: string;

    /** 城市经纬度 */
    AreaCenter?: string;

    AreaPinYin?: string;
    StatusCode?: string;
  }

  interface ExpressRouteInfo {
    /** 出发地 */
    From?: ExpressRouteInfoDetail;
    /** 当前地 */
    Cur?: ExpressRouteInfoDetail;
    /** 到达地 */
    To?: ExpressRouteInfoDetail;
  }

  interface ExpressRouteInfoDetail {
    Number?: string;
    Name?: string;
  }
}

export {};
