<template>
  <el-card shadow="never">
    <div class="flex flex-row justify-between items-center w-full">
      <div class="flex flex-col items-stretch flex-1">
        <div class="header-wrapper">
          <el-text>待选列表：</el-text>
          <!-- 搜索功能，由 pendingSearchConfig 决定是否显示 -->
          <template v-if="props.pendingSearchConfig">
            <el-input
              v-model="queryParams.Keyword"
              class="flex-1 mr-15px ml-10px"
              :placeholder="props.pendingSearchConfig?.placeholder ?? '请输入'"
              clearable
              :disabled="props.disabled"
            />
            <el-button :disabled="props.disabled" type="primary" @click="onSearchPendingList">
              搜索
            </el-button>
          </template>
        </div>
        <el-table
          ref="pendingTableRef"
          :data="pendingList"
          border
          :row-key="props.rowKey"
          :height="pendingTableHeight"
          :disabled="props.disabled"
          :header-cell-style="{ textAlign: 'center' }"
          :cell-style="{ textAlign: 'center' }"
          @select="handleSelectChange"
          @select-all="handleSelectAllChange"
        >
          <el-table-column type="selection" :selectable="(_) => !props.disabled" width="55" />
          <slot name="pending" />
        </el-table>
        <!-- 分页控制器，由 pendingTotal 决定是否显示 -->
        <el-pagination
          v-if="props.pendingPageConfig"
          v-model:current-page="queryParams.PageIndex"
          class="pagination-wrapper"
          :page-size="queryParams.PageSize"
          layout="prev, pager, next"
          :total="props.pendingPageConfig.pendingTotal"
          :disabled="props.disabled"
          @current-change="onPageChangePendingList"
        />
      </div>
      <el-icon class="m-20px"><DArrowRight /></el-icon>
      <div class="flex flex-col items-center flex-1">
        <div class="header-wrapper">
          <el-text>已选列表：</el-text>
        </div>
        <el-table
          :data="selectedList"
          border
          :row-key="props.rowKey"
          :height="384"
          :header-cell-style="{ textAlign: 'center' }"
          :cell-style="{ textAlign: 'center' }"
        >
          <slot name="selected" />
          <!-- 已选列表，可操作状态下，显示移除按钮 -->
          <el-table-column v-if="!props.disabled" label="操作" width="100">
            <template #default="scope">
              <el-button link type="primary" @click="handleRemoveSelectedData(scope.row)">
                移除
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>
  </el-card>
</template>

<script setup lang="ts" generic="T extends Record<string, any>">
const kEnableDebug = false;
defineOptions({
  name: "TableTransfer",
});

interface TableTransferProps {
  disabled?: boolean;
  /**
   * 列表行键，默认 `Id`
   *
   * @note 点击待选列表每项的多选框时，会根据此字段来匹配【已选列表】是否存在该行数据，用于判断是否选中
   * */
  rowKey?: string;

  /** 待选列表加载状态 */
  pendingLoading?: boolean;

  /**
   * 待选列表的【搜索】配置
   *
   * 如果存在，则会在顶部显示搜索框
   */
  pendingSearchConfig?: {
    /** 搜索框占位符，默认显示“请输入” */
    placeholder?: string;

    /**
     * 点击搜索回调函数
     *
     * @param query 搜索关键词
     * @param pageIndex 当前页码（如果不存在分页器，此参数默认1）
     *
     * @returns
     *  - 如果返回 void，表示利用现有数据本地筛选
     *  - 如果返回 Promise<void>，表示网络请求数据，可以设置 `pendingLoading` 为 `true` 来配合显示加载状态
     *
     * @note 在搜索函数中，需要将搜索结果，同步赋值给 `pendingList`，以保证【待选列表】同步显示搜索结果
     */
    searchMethod: (params: { keyword?: string; pageIndex: number }) => void | Promise<void>;
  };

  /**
   * 待选列表分页配置
   *
   * 如果存在，则在底部显示分页器
   */
  pendingPageConfig?: {
    /** 分页器每页条数，默认 10 */
    pageSize?: number;

    /** 待选列表数据总数 */
    pendingTotal: number;

    /**
     * 切换分页回调函数
     */
    pageChangeMethod: (params: { pageIndex: number; keyword?: string }) => void | Promise<void>;
  };

  /** 已选列表加载状态 */
  selectedLoading?: boolean;
}

const props = withDefaults(defineProps<TableTransferProps>(), {
  disabled: false,
  rowKey: "Id",
  pendingLoading: false,
  selectedLoading: false,
});

/** 待选列表查询参数 */
const queryParams = reactive({
  PageIndex: 1,
  PageSize: props.pendingPageConfig?.pageSize ?? 10,
  Keyword: undefined as string | undefined,
});

/** 待选列表搜索数据 */
function onSearchPendingList() {
  queryParams.PageIndex = 1;
  props.pendingSearchConfig?.searchMethod({
    keyword: queryParams.Keyword,
    pageIndex: queryParams.PageIndex,
  });
}

/** 待选列表分页数据请求 */
function onPageChangePendingList() {
  props.pendingPageConfig?.pageChangeMethod({
    pageIndex: queryParams.PageIndex,
    keyword: queryParams.Keyword,
  });
}

/** 待选列表表格引用 */
const pendingTableRef = useTemplateRef("pendingTableRef");
const pendingTableHeight = computed(() => {
  return 384 - (props.pendingPageConfig ? 32 : 0);
});

/** 待选列表数据 */
const pendingList = defineModel<T[]>("pendingList", { required: true });

/** 同步待选列表数据 */
watch(pendingList, (_) => {
  kEnableDebug && console.debug("待选列表数据更新", pendingList.value);
  nextTick(() => {
    setDefaultSelection();
  });
});

/** 点击待选列表每项的多选框 */
function handleSelectChange(_: T[], row: T) {
  isInternalSelectedListUpdate.value = true;
  if (selectedList.value.find((e) => e[props.rowKey] === row[props.rowKey])) {
    selectedList.value = selectedList.value.filter((e) => e[props.rowKey] !== row[props.rowKey]);
  } else {
    selectedList.value.push(row as any);
  }
}

/** 点击待选列表全选的多选框 */
function handleSelectAllChange(selection: T[]) {
  if (props.disabled) return;

  isInternalSelectedListUpdate.value = true;
  selectedList.value = selection;
}

/** 已选列表数据 */
const selectedList = defineModel<T[]>("selectedList", { default: () => [] });

/** 是否是由于组件内部操作，导致已选列表数据更新 */
const isInternalSelectedListUpdate = ref(false);

/** 同步已选列表数据 */
watch(selectedList, (newVal) => {
  if (isInternalSelectedListUpdate.value) {
    isInternalSelectedListUpdate.value = false;
    return;
  }

  kEnableDebug && console.debug("已选列表数据更新", newVal);
  nextTick(() => {
    setDefaultSelection();
  });
});

/**
 * 同步选中已选列表
 */
function setDefaultSelection() {
  if (pendingList.value.length === 0 || selectedList.value.length === 0) return;

  selectedList.value.forEach((item) => {
    const row = pendingList.value.find((e) => e[props.rowKey] === item[props.rowKey]);
    if (row && pendingTableRef.value) {
      pendingTableRef.value?.toggleRowSelection(row, true);
    }
  });
}

/** 移除已选列表中的数据 */
function handleRemoveSelectedData(row: T) {
  isInternalSelectedListUpdate.value = true;
  selectedList.value = selectedList.value.filter((e) => e[props.rowKey] !== row[props.rowKey]);

  const item = pendingList.value.find((e) => e[props.rowKey] === row[props.rowKey]);
  if (item) {
    pendingTableRef.value?.toggleRowSelection(item, false);
  }
}
</script>

<style lang="scss" scoped>
.header-wrapper {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  width: 100%;
  height: 40px;
  padding: 0 10px;
  border-top: 1px solid #e4e7ed;
  border-right: 1px solid #e4e7ed;
  border-left: 1px solid #e4e7ed;
}

.pagination-wrapper {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  padding: 4px 0;
  border-right: 1px solid #e4e7ed;
  border-bottom: 1px solid #e4e7ed;
  border-left: 1px solid #e4e7ed;
}
</style>
