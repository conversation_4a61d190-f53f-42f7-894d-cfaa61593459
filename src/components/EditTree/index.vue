<template>
  <div class="w-full h-full">
    <el-tree
      ref="treeRef"
      :data="props.data"
      :props="props.props"
      :node-key="props.nodeKey ?? 'Id'"
      :current-node-key="currentNodeKey"
      highlight-current
      default-expand-all
      class="w-full h-full p-10px"
      @node-click="emit('nodeClick', $event)"
      @node-contextmenu="onNodeRightClick"
    />
    <!-- 右键菜单（添加、编辑、删除） -->
    <div v-show="nodeMenuBar.isShow">
      <ul
        id="menu"
        class="menu"
        :style="'top:' + menuPosition.clientY + 'px;left:' + menuPosition.clientX + 'px;'"
      >
        <li v-if="nodeMenuBar.add" class="menu_item" @click="onAdd">添加</li>
        <li v-if="nodeMenuBar.edit" class="menu_item" @click="onEdit">编辑</li>
        <li v-if="nodeMenuBar.delete" class="menu_item" @click="onDelete">删除</li>
      </ul>
    </div>
  </div>
</template>

<script setup lang="ts" generic="T extends Record<string, any>">
import { TreeInstance, TreeOptionProps } from "element-plus";

export interface TreeNodeOperations {
  /** 是否显示添加按钮 */
  add?: boolean;
  /** 是否显示编辑按钮 */
  edit?: boolean;
  /** 是否显示删除按钮 */
  delete?: boolean;
}

const kEnableDebug = false;
defineOptions({
  name: "EditTree",
});

const emit = defineEmits<{
  nodeClick: [data: T];
  nodeContextmenu: [data: T];
  add: [data: T];
  edit: [data: T];
  delete: [data: T];
}>();

const props = defineProps<{
  /** 树数据 */
  data: Array<T>;
  /** 树节点配置 */
  props?: TreeOptionProps;
  /** 树节点key */
  nodeKey?: string;
  /** 当前选中的节点key */
  currentNodeKey: string | number | undefined;
  /** 右键点击节点，需要显示的操作 */
  operations: (data: T) => TreeNodeOperations;
}>();

/** 树组件实例 */
const treeRef = useTemplateRef<TreeInstance>("treeRef");

/** 右键菜单 */
const nodeMenuBar = reactive({
  /** 是否显示右键菜单 */
  isShow: false,
  /** 是否显示添加按钮 */
  add: false,
  /** 是否显示编辑按钮 */
  edit: false,
  /** 是否显示删除按钮 */
  delete: false,
});

/**
 * 右键当前选中的节点
 */
const currentNode = ref<T | null>(null);

/** 树右键菜单弹出位置 */
const menuPosition = ref({
  clientX: 0,
  clientY: 0,
});

/**
 * 隐藏右键菜单栏
 */
function handleHideMenu() {
  kEnableDebug && console.debug("隐藏右键菜单栏");

  nodeMenuBar.isShow = false;
  currentNode.value = null;
}

/**
 * 树右键点击事件
 */
function onNodeRightClick(event: MouseEvent, data: T) {
  kEnableDebug && console.debug("当前节点", data);
  emit("nodeContextmenu", data);

  menuPosition.value.clientX = event.clientX + 10;
  var clientHeight = document.body.clientHeight; // 浏览器窗口的可见高度
  menuPosition.value.clientY = Math.min(event.clientY, clientHeight - 110);

  currentNode.value = data;
  nodeMenuBar.add = props.operations(data).add ?? false;
  nodeMenuBar.edit = props.operations(data).edit ?? false;
  nodeMenuBar.delete = props.operations(data).delete ?? false;
  nodeMenuBar.isShow = true;
}

/**
 * 点击添加子节点
 */
function onAdd() {
  kEnableDebug && console.debug("添加子节点", currentNode.value);
  nodeMenuBar.isShow = false;
  emit("add", currentNode.value!);
}

/**
 * 点击编辑节点
 */
function onEdit() {
  kEnableDebug && console.debug("编辑节点", currentNode.value);
  nodeMenuBar.isShow = false;
  emit("edit", currentNode.value!);
}

/**
 * 点击删除节点
 */
function onDelete() {
  kEnableDebug && console.debug("删除节点", currentNode.value);
  nodeMenuBar.isShow = false;
  emit("delete", currentNode.value!);
}

/**
 * 删除节点
 * @param data 当前操作的节点
 */
function remove(data: T) {
  kEnableDebug && console.debug("删除节点", data);
  treeRef.value?.remove(data);
}

/**
 * 组件挂载时添加事件监听器
 */
onMounted(() => {
  document.addEventListener("click", handleHideMenu);
});

/**
 * 组件卸载时移除事件监听器
 */
onUnmounted(() => {
  document.removeEventListener("click", handleHideMenu);
});

/**
 * 导出方法
 */
defineExpose({
  /** 移除节点 */
  remove,
});
</script>

<style lang="scss" scoped>
.menu {
  width: 80px;
  position: fixed;
  border-radius: 4px;
  border: 1px solid #eeeeee;
  background-color: #ffffff;
  color: var(--el-color-primary);
  z-index: 999;
  padding: 0;

  .menu_item {
    line-height: 30px;
    text-align: center;
  }

  li:hover {
    background-color: var(--el-color-primary);
    border-radius: 4px;
    color: white;
  }

  li {
    font-size: 14px;
    list-style: none;
  }
}
</style>
