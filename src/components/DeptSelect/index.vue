<template>
  <el-select-v2
    v-model="modelValue"
    placeholder="请选择"
    :options="options"
    filterable
    clearable
    :loading="loading"
    :multiple="props.multiple"
    :multiple-limit="props.multipleLimit"
    collapse-tags
    collapse-tags-tooltip
    :disabled="props.disabled"
    :props="{
      label: 'Name',
      value: props.keyId,
    }"
    :empty-values="[null, undefined, '']"
    :value-on-clear="() => null"
    @focus="handleFetchOptions"
    @change="emit('change', modelValue)"
  />
</template>

<script setup lang="ts">
import { getDeptList } from "@/utils/dict";

type OptionType = string | string[] | null | undefined;
const emit = defineEmits<{
  (e: "change", value: OptionType): void;
}>();

interface Props {
  // 请求数据需要的参数
  orgId?: string;

  // 内部组件需要参数
  multiple?: boolean;
  keyId?: string;
  disabled?: boolean;
  multipleLimit?: number;
}

const modelValue = defineModel<OptionType>({ required: true });
const props = withDefaults(defineProps<Props>(), {
  multiple: false,
  keyId: "Id",
  disabled: false,
  multipleLimit: 0,
});
watch(
  () => props.orgId,
  () => {
    console.log("props.orgId", props.orgId);
    modelValue.value = null;
    options.value = [];
    handleFetchOptions();
  }
);

const loading = ref(false);
const options = ref<BaseDepartment[]>([]);

const handleFetchOptions = async () => {
  if (options.value.length) return;

  loading.value = true;
  options.value = await getDeptList({
    OrgId: props.orgId || "",
  });
  loading.value = false;
};
</script>

<style scoped lang="scss"></style>
