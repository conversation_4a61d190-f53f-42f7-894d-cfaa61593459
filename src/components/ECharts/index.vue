<template>
  <div ref="chartRef" :style="{ width, height }" />
</template>

<script setup lang="ts">
// 引入 echarts 核心模块，核心模块提供了 echarts 使用必须要的接口。
import * as echarts from "echarts/core";
// 引入柱状、折线和饼图常用图表
import { Bar<PERSON>hart, LineChart, PieChart } from "echarts/charts";
// 引入标题，提示框，直角坐标系，数据集，内置数据转换器组件，
import {
  GridComponent,
  TooltipComponent,
  LegendComponent,
  TitleComponent,
} from "echarts/components";
// 引入 Canvas 渲染器，注意引入 CanvasRenderer 或者 SVGRenderer 是必须的一步
import { CanvasRenderer } from "echarts/renderers";

import { useResizeObserver } from "@vueuse/core";

// 按需注册组件
echarts.use([
  CanvasRenderer,
  BarChart,
  LineChart,
  PieChart,
  GridComponent,
  TooltipComponent,
  LegendComponent,
  TitleComponent,
]);

/** 饼图数据项接口定义 */
interface PieDataItem {
  name: string;
  value: number;
  [key: string]: any;
}

/** 饼图系列配置接口定义 */
interface PieSeriesOption {
  type: "pie";
  data: PieDataItem[];
  [key: string]: any;
}

/** 类型守卫函数：检查是否为饼图系列配置 */
const isPieSeriesOption = (series: any): series is PieSeriesOption => {
  return series && series.type === "pie" && Array.isArray(series.data);
};

/** 类型守卫函数：检查options是否包含series数组 */
const hasSeriesArray = (options: any): options is { series: any[] } => {
  return options && Array.isArray(options.series);
};

const props = defineProps<{
  options: echarts.EChartsCoreOption;
  width?: string;
  height?: string;
  automaticHighlight?: boolean;
}>();

const chartRef = ref<HTMLDivElement | null>(null);
// let chartInstance: echarts.ECharts | null = null;
const chartInstance = shallowRef<echarts.ECharts | null>(null);

// 轮播状态管理
const isCarouselActive = ref(false); // 轮播是否激活
const currentHighlightIndex = ref(0); // 当前高亮索引
const carouselTimer = ref<NodeJS.Timeout | null>(null); // 轮播定时器
const isMouseHovering = ref(false); // 鼠标是否悬浮

// 初始化图表
const initChart = () => {
  if (chartRef.value) {
    chartInstance.value = echarts.init(chartRef.value);
    if (props.options) {
      chartInstance.value.setOption(props.options);
    }

    // 设置鼠标事件监听
    setupMouseEvents();

    if (props.automaticHighlight) {
      // 使用nextTick确保图表完全渲染后再启动轮播
      nextTick(() => {
        startCarousel();
      });
    }
  }
};

/** 获取饼图数据长度 */
const getPieDataLength = (): number => {
  if (!hasSeriesArray(props.options) || props.options.series.length === 0) {
    return 0;
  }

  const firstPieSeries = props.options.series.find(isPieSeriesOption);
  const dataLength = firstPieSeries?.data?.length || 0;
  return dataLength;
};

/** 开始轮播 */
const startCarousel = () => {
  const dataLength = getPieDataLength();

  if (dataLength <= 1) {
    return; // 数据少于2项时不启用轮播
  }

  // 确保先清理现有定时器
  if (carouselTimer.value) {
    clearInterval(carouselTimer.value);
  }

  isCarouselActive.value = true;
  currentHighlightIndex.value = 0; // 重置索引

  // 立即高亮第一个项
  highlightPie(0);

  carouselTimer.value = setInterval(() => {
    if (!isMouseHovering.value) {
      highlightPie(currentHighlightIndex.value);
      currentHighlightIndex.value = (currentHighlightIndex.value + 1) % dataLength;
    }
  }, 2000); // 每2秒切换一次
};

/** 停止轮播 */
const stopCarousel = () => {
  isCarouselActive.value = false;
  if (carouselTimer.value) {
    clearInterval(carouselTimer.value);
    carouselTimer.value = null;
  }
};

/** 设置鼠标事件监听 */
const setupMouseEvents = () => {
  if (!chartInstance.value) return;

  // 鼠标悬浮事件
  chartInstance.value.on("mouseover", "series", (params: any) => {
    isMouseHovering.value = true;
    if (typeof params.dataIndex === "number") {
      highlightPie(params.dataIndex);
    }
  });

  // 鼠标移出事件
  chartInstance.value.on("mouseout", "series", () => {
    isMouseHovering.value = false;
  });
};

const highlightPie = (currentIndex: number) => {
  if (!hasSeriesArray(props.options) || props.options.series.length === 0) {
    return;
  }

  const firstPieSeries = props.options.series.find(isPieSeriesOption);
  if (!firstPieSeries?.data) {
    return;
  }

  // 遍历饼图数据，取消所有图形的高亮效果
  for (let idx = 0; idx < firstPieSeries.data.length; idx++) {
    chartInstance.value?.dispatchAction({
      type: "downplay",
      seriesIndex: 0,
      dataIndex: idx,
    });
  }

  // 高亮当前图形
  chartInstance.value?.dispatchAction({
    type: "highlight",
    seriesIndex: 0,
    dataIndex: currentIndex,
  });
};

// 监听尺寸变化，自动调整
useResizeObserver(chartRef, () => {
  chartInstance?.value?.resize();
});

// 监听 options 变化，更新图表
watch(
  () => props.options,
  (newOptions) => {
    if (chartInstance && newOptions) {
      chartInstance.value?.setOption(newOptions);

      // 如果开启了自动轮播，启动轮播（无论之前状态如何）
      if (props.automaticHighlight) {
        stopCarousel(); // 先停止现有轮播
        nextTick(() => {
          startCarousel();
        });
      }
    }
  },
  { deep: true, immediate: true }
);

// 监听automaticHighlight属性变化
watch(
  () => props.automaticHighlight,
  (newValue) => {
    if (newValue) {
      startCarousel();
    } else {
      stopCarousel();
    }
  }
);

onMounted(() => {
  nextTick(() => initChart());
});

onBeforeUnmount(() => {
  // 清理定时器
  stopCarousel();
  // 清理图表实例
  chartInstance?.value?.dispose();
});
</script>
