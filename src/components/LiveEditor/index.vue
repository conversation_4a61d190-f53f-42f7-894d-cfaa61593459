<template>
  <div class="live-editor">
    <div class="editor-area">
      <textarea v-model="code" spellcheck="false" />
    </div>
    <div class="preview-container">
      <div v-if="isLoading" class="loading-overlay">
        <div class="loading-text">加载中...</div>
      </div>
      <div v-if="errorMessage" class="error-overlay">
        <div class="error-text">{{ errorMessage }}</div>
      </div>
      <iframe ref="iframeRef" class="preview-area" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch, onMounted, onUnmounted, type Ref } from "vue";

// 工具函数：提取某个 block（template/script）
function extract(code: string, tag: string): string {
  // 处理 script setup 的特殊情况
  if (tag === "script setup") {
    const reg = /<script\s+setup[^>]*?>([\s\S]*?)<\/script>/;
    const match = code.match(reg);
    return match?.[1]?.trim() || "";
  }

  const reg = new RegExp(`<${tag}[^>]*?>([\\s\\S]*?)<\\/${tag}>`);
  const match = code.match(reg);
  return match?.[1]?.trim() || "";
}

// 提取 ref 定义的变量名
function extractRefs(script: string): string {
  // 匹配 const xxx = ref(...) 模式
  const refMatches = [...script.matchAll(/const\s+(\w+)\s*=\s*ref\s*\([^)]*\)/g)];
  // 匹配 const xxx = reactive(...) 模式
  const reactiveMatches = [...script.matchAll(/const\s+(\w+)\s*=\s*reactive\s*\([^)]*\)/g)];
  // 匹配 const xxx = computed(...) 模式
  const computedMatches = [...script.matchAll(/const\s+(\w+)\s*=\s*computed\s*\([^)]*\)/g)];

  const allVars = [
    ...refMatches.map((m) => m[1]),
    ...reactiveMatches.map((m) => m[1]),
    ...computedMatches.map((m) => m[1]),
  ];

  return allVars.join(", ");
}

function generateHTML(sourceCode: string): string {
  const template = extract(sourceCode, "template");
  const script = extract(sourceCode, "script setup");
  const refVars = extractRefs(script);

  // 调试信息
  console.log("Template:", template);
  console.log("Script:", script);
  console.log("RefVars:", refVars);

  // 移除 import 语句，因为我们使用全局变量
  const cleanScript = script.replace(/import\s+.*?from\s+['"].*?['"];?\s*/g, "");

  // 替换 script 结束标签，防止提前结束
  const safeScript = cleanScript.replace(/<\/script>/gi, "<\\/script>");

  return `<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <title>Preview</title>
  <link rel="stylesheet" href="https://unpkg.com/element-plus@2.10.5/dist/index.css" />
  <style>
    body { margin: 0; padding: 10px; font-family: sans-serif; }
  </style>
  <script src="https://unpkg.com/vue@3/dist/vue.global.prod.js"><\/script>
  <script src="https://unpkg.com/element-plus@2.10.5/dist/index.full.min.js"><\/script>
</head>
<body>
  <div id="app"></div>
  <script type="module">
    // 通知父窗口开始加载
    if (window.parent !== window) {
      window.parent.postMessage({ type: 'preview-loading' }, '*');
    }

    // 等待DOM完全加载
    document.addEventListener('DOMContentLoaded', function() {
      try {
        // 检查必要的全局变量是否已加载
        if (!window.Vue) {
          const appElement = document.getElementById('app');
          if (appElement) {
            appElement.innerHTML = '<div style="padding: 20px; color: red;">错误: Vue 未能正确加载</div>';
          }
          // 通知父窗口加载失败
          if (window.parent !== window) {
            window.parent.postMessage({ type: 'preview-error', message: 'Vue 未能正确加载' }, '*');
          }
          return;
        }
        if (!window.ElementPlus) {
          const appElement = document.getElementById('app');
          if (appElement) {
            appElement.innerHTML = '<div style="padding: 20px; color: red;">错误: Element Plus 未能正确加载</div>';
          }
          // 通知父窗口加载失败
          if (window.parent !== window) {
            window.parent.postMessage({ type: 'preview-error', message: 'Element Plus 未能正确加载' }, '*');
          }
          return;
        }

        const { createApp, ref } = Vue;
        const App = {
          template: \`${template}\`,
          setup() {
            try {
              ${safeScript}
              return { ${refVars} }
            } catch (error) {
              console.error('脚本执行错误:', error);
              // 通知父窗口脚本错误
              if (window.parent !== window) {
                window.parent.postMessage({ type: 'preview-error', message: \`脚本执行错误: \${error.message}\` }, '*');
              }
              return { error: error.message };
            }
          }
        };
        const app = createApp(App);
        app.use(ElementPlus);
        
        // 添加全局错误处理
        app.config.errorHandler = (err, vm, info) => {
          console.error('Vue应用错误:', err, info);
          const appElement = document.getElementById('app');
          if (appElement) {
            appElement.innerHTML = \`<div style="padding: 20px; color: red;">运行时错误: \${err.message}</div>\`;
          }
          // 通知父窗口应用错误
          if (window.parent !== window) {
            window.parent.postMessage({ type: 'preview-error', message: \`运行时错误: \${err.message}\` }, '*');
          }
        };
        
        app.mount('#app');
        
        // 通知父窗口加载成功
        if (window.parent !== window) {
          window.parent.postMessage({ type: 'preview-ready' }, '*');
        }
        
      } catch (error) {
        console.error('初始化错误:', error);
        const appElement = document.getElementById('app');
        if (appElement) {
          appElement.innerHTML = \`<div style="padding: 20px; color: red;">初始化错误: \${error.message}</div>\`;
        }
        // 通知父窗口初始化错误
        if (window.parent !== window) {
          window.parent.postMessage({ type: 'preview-error', message: \`初始化错误: \${error.message}\` }, '*');
        }
      }
    });
  <\/script>
</body>
</html>`;
}

const code = ref(`<template>
  <el-button type="primary" @click="count++">
    Click Me {{ count }}
  </el-button>
</template>

<script setup>
import { ref } from 'vue'
const count = ref(0)
<\/script>
`);

const iframeRef: Ref<HTMLIFrameElement | null> = ref(null);
const isLoading = ref(false);
const errorMessage = ref("");

// 消息监听器的引用，用于清理
let messageListener: ((event: MessageEvent) => void) | null = null;
let timeoutId: number | null = null;

function updateIframe(): void {
  const iframe = iframeRef.value;
  if (!iframe) {
    errorMessage.value = "iframe元素未找到";
    return;
  }

  try {
    // 清理之前的监听器和超时
    if (messageListener) {
      window.removeEventListener("message", messageListener);
      messageListener = null;
    }
    if (timeoutId) {
      clearTimeout(timeoutId);
      timeoutId = null;
    }

    isLoading.value = true;
    errorMessage.value = "";

    const doc = iframe.contentDocument || iframe.contentWindow?.document;
    if (!doc) {
      errorMessage.value = "iframe文档无法访问";
      isLoading.value = false;
      return;
    }

    // 设置消息监听器
    messageListener = (event: MessageEvent) => {
      // 确保消息来自我们的iframe
      if (event.source !== iframe.contentWindow) return;

      const { type, message } = event.data;

      switch (type) {
        case "preview-loading":
          // iframe开始加载，保持loading状态
          break;
        case "preview-ready":
          // iframe加载成功
          isLoading.value = false;
          errorMessage.value = "";
          if (timeoutId) {
            clearTimeout(timeoutId);
            timeoutId = null;
          }
          break;
        case "preview-error":
          // iframe内部发生错误
          isLoading.value = false;
          errorMessage.value = message || "预览加载失败";
          if (timeoutId) {
            clearTimeout(timeoutId);
            timeoutId = null;
          }
          break;
      }
    };

    window.addEventListener("message", messageListener);

    // 写入HTML内容
    doc.open();
    doc.write(generateHTML(code.value));
    doc.close();

    // 设置超时处理（增加到10秒）
    timeoutId = window.setTimeout(() => {
      if (isLoading.value) {
        isLoading.value = false;
        errorMessage.value = "加载超时，可能是网络连接问题或代码存在错误";
      }
      timeoutId = null;
    }, 10000);
  } catch (error) {
    isLoading.value = false;
    errorMessage.value = `更新失败: ${error instanceof Error ? error.message : "未知错误"}`;
    console.error("iframe更新错误:", error);
  }
}

watch(code, updateIframe);
onMounted(updateIframe);

// 组件卸载时清理资源
onUnmounted(() => {
  if (messageListener) {
    window.removeEventListener("message", messageListener);
    messageListener = null;
  }
  if (timeoutId) {
    clearTimeout(timeoutId);
    timeoutId = null;
  }
});
</script>

<style scoped>
.live-editor {
  display: flex;
  gap: 10px;
  width: 100%;
}
.editor-area {
  width: 50%;
}
.editor-area textarea {
  width: 100%;
  height: 500px;
  font-family: monospace;
  font-size: 14px;
  padding: 10px;
  box-sizing: border-box;
  border: 1px solid #ccc;
  border-radius: 6px;
}
.preview-container {
  width: 50%;
  position: relative;
}
.preview-area {
  width: 100%;
  height: 500px;
  border: 1px solid #ccc;
  border-radius: 6px;
}
.loading-overlay,
.error-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10;
  border-radius: 6px;
}
.loading-overlay {
  background-color: rgba(255, 255, 255, 0.9);
}
.error-overlay {
  background-color: rgba(254, 226, 226, 0.95);
}
.loading-text {
  color: #409eff;
  font-size: 16px;
  font-weight: 500;
}
.error-text {
  color: #f56c6c;
  font-size: 14px;
  text-align: center;
  padding: 20px;
  max-width: 80%;
  word-wrap: break-word;
}
</style>
