<template>
  <el-select-v2
    v-model="selectedVal"
    :options="schoolList"
    placeholder="请选择学校"
    :empty-values="['', undefined, null]"
    :value-on-clear="() => null"
    clearable
    reserve-keyword
    filterable
  />
</template>

<script setup lang="ts">
import Supplier_Common_Api from "@/api/supplier-common";

const selectedVal = defineModel({ type: String });

const schoolList = ref<OptionType[]>([]);

const handleGetSchoolList = async () => {
  const res = await Supplier_Common_Api.getSchool();
  if (res.Type === 200) {
    schoolList.value = res.Data.map((item) => ({
      label: item.School,
      value: item.School,
    }));
  }
};

onMounted(() => {
  handleGetSchoolList();
});
</script>

<style scoped lang="scss"></style>
