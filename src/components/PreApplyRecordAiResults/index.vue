<template>
  <div class="medical-report-container">
    <el-scrollbar v-loading="loading" class="report-scrollbar">
      <div class="report-content">
        <!-- 患者信息 -->
        <div class="patient-info-header">
          患者信息：{{ aiReport.name }} {{ aiReport.gender }} {{ aiReport.age }}岁
        </div>

        <!-- 初步诊断建议 -->
        <div class="section diagnosis-suggestion">
          <div class="section-title">初步诊断建议</div>
          <div class="section-content">
            <p>{{ aiReport.overview }}</p>
            <p v-if="aiReport.preliminaryDiagnosis" class="diagnosis-text">
              {{ aiReport.preliminaryDiagnosis }}。
            </p>
            <div class="ai-warning">
              <el-icon color="#E6A23C"><WarningFilled /></el-icon>
              <span>此为AI预诊，需进一步影像学检查确认。</span>
            </div>
          </div>
        </div>

        <!-- 预问诊病历摘要 -->
        <div class="section visit-summary">
          <div class="section-title">预问诊病历摘要</div>
          <div class="section-content">
            <ul class="summary-list">
              <li>
                <span class="dot blue" />
                <span class="label">主诉：</span>
                <span class="value">{{ aiReport.chiefComplaint }}</span>
              </li>
              <li>
                <span class="dot blue" />
                <span class="label">症状部位：</span>
                <span class="value">{{ aiReport.symptomLocation }}</span>
              </li>
              <li>
                <span class="dot blue" />
                <span class="label">症状持续时间：</span>
                <span class="value">{{ aiReport.symptomDuration }}</span>
              </li>
              <li>
                <span class="dot blue" />
                <span class="label">症状性质：</span>
                <span class="value">{{ aiReport.symptomNature }}</span>
              </li>
              <li>
                <span class="dot orange" />
                <span class="label">危险信号：</span>
                <span class="value">{{ aiReport.dangerSigns }}</span>
              </li>
              <li>
                <span class="dot gray" />
                <span class="label">诱发因素：</span>
                <span class="value">{{ aiReport.triggeringFactors }}</span>
              </li>
              <li>
                <span class="dot gray" />
                <span class="label">缓解方式：</span>
                <span class="value">{{ aiReport.reliefMethods }}</span>
              </li>
              <li>
                <span class="dot gray" />
                <span class="label">其他病史：</span>
                <span class="value">{{ aiReport.otherHistory }}</span>
              </li>
            </ul>
          </div>
        </div>
      </div>
    </el-scrollbar>
  </div>
</template>

<script setup lang="ts">
import Consult_Api from "@/api/consult";
import { LatestPreVisitAiAnalysisInputDTO, PreVisitAiAnalysis } from "@/api/consult/types";
import { WarningFilled } from "@element-plus/icons-vue";
import dayjs from "dayjs";

// 定义数据接口
interface AiReportData {
  name: string;
  gender: string;
  age: number | null;
  overview: string;
  preliminaryDiagnosis: string;
  chiefComplaint: string;
  symptomLocation: string;
  symptomDuration: string;
  symptomNature: string;
  dangerSigns: string;
  triggeringFactors: string;
  reliefMethods: string;
  otherHistory: string;
}

// 响应式数据
const loading = ref(false);
const aiReport = reactive<AiReportData>({
  name: "",
  gender: "男",
  age: 32,
  overview: "",
  preliminaryDiagnosis: "",
  chiefComplaint: "",
  symptomLocation: "",
  symptomDuration: "",
  symptomNature: "",
  dangerSigns: "",
  triggeringFactors: "",
  reliefMethods: "",
  otherHistory: "",
});

const onGetPreApplyRecordAiResults = async () => {
  loading.value = true;
  const params: LatestPreVisitAiAnalysisInputDTO = {
    ...props.params,
    CreatedTime: dayjs().format("YYYY-MM-DD"),
  };
  try {
    const res = await Consult_Api.getLatestPreVisitAiAnalysis(params);
    if (res.Type === 200 && res.Data) {
      handleProcessData(res.Data);
    }
  } catch (error) {
    console.error("获取AI分析结果失败:", error);
  } finally {
    loading.value = false;
  }
};

/**
 * @description 处理AI分析结果数据，解析并更新UI
 * @param {PreVisitAiAnalysis} data - 从API获取的分析结果对象
 */
const handleProcessData = (data: PreVisitAiAnalysis | undefined) => {
  if (!data) return;

  // 基础信息直接从顶层获取
  aiReport.name = data.PatientName || "";
  aiReport.gender = data.Sex || "";
  aiReport.age = data.Age ? Number.parseInt(data.Age, 10) : null;

  // 解析内嵌的AiResult JSON字符串
  if (data.AiResult) {
    try {
      const aiResultDetails = JSON.parse(data.AiResult);
      // 使用映射更新剩余字段
      const mappings = {
        overview: "概况",
        preliminaryDiagnosis: "初步诊断",
        chiefComplaint: "主诉",
        symptomLocation: "症状部位",
        symptomDuration: "症状持续时间",
        symptomNature: "症状性质",
        dangerSigns: "危险信号",
        triggeringFactors: "诱发因素",
        reliefMethods: "缓解方式",
        otherHistory: "其他病史",
      };

      for (const [targetKey, sourceKey] of Object.entries(mappings)) {
        (aiReport as any)[targetKey] = aiResultDetails[sourceKey] ?? "";
      }
    } catch (error) {
      ElMessage.error("解析AI分析数据失败");
      console.error("解析AI分析数据失败:", error);
    }
  }
};

interface Props {
  params: LatestPreVisitAiAnalysisInputDTO;
}
const props = defineProps<Props>();

watch(
  () => props.params,
  (newVal) => {
    if (newVal && newVal.DoctorId && newVal.PatientId) {
      onGetPreApplyRecordAiResults();
    }
  },
  { immediate: true }
);
</script>

<style lang="scss" scoped>
.medical-report-container {
  height: 100%;
  background-color: var(--el-bg-color);
  border-radius: 8px;
  overflow: hidden;

  .report-scrollbar {
    height: 100%;

    :deep(.el-scrollbar__view) {
      height: 100%;
    }
  }

  .report-content {
    padding: 16px;
    font-size: 14px;
    line-height: 1.6;
    color: var(--el-text-color-primary);
  }

  .patient-info-header {
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 16px;
    padding: 8px 12px;
    background-color: var(--el-color-primary-light-9);
    color: var(--el-color-primary);
    border-radius: 6px;
    border-left: 4px solid var(--el-color-primary);
  }

  .section {
    margin-bottom: 16px;

    .section-title {
      font-size: 15px;
      font-weight: 600;
      color: var(--el-text-color-primary);
      margin-bottom: 12px;
      padding: 8px 12px;
      background-color: var(--el-fill-color-light);
      border-radius: 6px;
    }

    .section-content {
      padding: 0 12px;
    }
  }

  .diagnosis-suggestion {
    .section-title {
      background-color: #00c6b8;
      color: #fff;
    }
    .section-content {
      p {
        margin: 0 0 10px;
      }
      .diagnosis-text {
        color: var(--el-text-color-regular);
      }
      .ai-warning {
        display: flex;
        align-items: center;
        font-size: 13px;
        color: var(--el-text-color-secondary);
        .el-icon {
          margin-right: 6px;
        }
      }
    }
  }

  .visit-summary {
    .section-title {
      background-color: var(--el-bg-color-page);
    }
    .summary-list {
      list-style: none;
      padding: 0;
      margin: 0;

      li {
        display: flex;
        align-items: center;
        margin-bottom: 10px;
        font-size: 13px;

        .dot {
          width: 8px;
          height: 8px;
          border-radius: 50%;
          margin-right: 10px;
          flex-shrink: 0;

          &.blue {
            background-color: #409eff;
          }
          &.orange {
            background-color: #e6a23c;
          }
          &.gray {
            background-color: #909399;
          }
        }

        .label {
          min-width: 90px;
          color: var(--el-text-color-regular);
          flex-shrink: 0;
        }

        .value {
          color: var(--el-text-color-primary);
          word-break: break-all;
        }
      }
    }
  }
}

// 暗黑模式适配
html.dark {
  .medical-report-container {
    .patient-info-header {
      background-color: var(--el-color-primary-light-8);
      color: var(--el-color-primary-light-3);
      border-left-color: var(--el-color-primary-light-3);
    }
    .diagnosis-suggestion {
      .section-title {
        background-color: #009a8f;
      }
    }
    .visit-summary {
      .section-title {
        background-color: var(--el-fill-color-darker);
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .medical-report-container .report-content {
    padding: 12px;
  }
}
</style>
