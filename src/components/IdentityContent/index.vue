<template>
  <div class="h-400px overflow-y-auto">
    <el-form
      ref="formRef"
      :model="certificates"
      :rules="rules"
      label-position="right"
      scroll-to-first-error
      :disabled="isPreview"
      style="padding: 0 !important"
    >
      <!-- 身份证号输入 -->
      <el-form-item label="身份证号" prop="idCard">
        <el-input
          v-model="certificates.idCard"
          placeholder="请输入身份证号"
          type="text"
          style="width: 300px"
          maxlength="18"
          show-word-limit
        />
      </el-form-item>

      <!-- 身份证正反面上传 -->
      <FormItemContainer>
        <el-form-item label="身份证正面" prop="idCard_front">
          <SingleImageUpload
            v-model="certificates.idCard_front"
            :disabled="isPreview"
            :style="{ width: '150px', height: '150px' }"
            accept="image/*"
          />
        </el-form-item>
        <el-form-item label="身份证背面" prop="idCard_back" class="ml-16px">
          <SingleImageUpload
            v-model="certificates.idCard_back"
            :disabled="isPreview"
            :style="{ width: '150px', height: '150px' }"
            accept="image/*"
          />
        </el-form-item>
      </FormItemContainer>
    </el-form>
  </div>
</template>

<script setup lang="ts">
import { FormInstance, FormRules } from "element-plus";
import SingleImageUpload from "@/components/Upload/SingleImageUpload.vue";
import FormItemContainer from "@/components/FormItemContainer/index.vue";

interface Props {
  userCertificates: UserCertificate[];
  userId: string;
  disabled?: boolean;
}

const props = defineProps<Props>();
const formRef = ref<FormInstance>();
const isPreview = inject("isPreview", ref(false)) as Ref<boolean>;

// 身份证信息
const certificates = ref({
  idCard: "",
  idCard_front: "",
  idCard_back: "",
});

// 表单验证规则
const rules = reactive<FormRules>({
  idCard: [
    {
      pattern:
        /^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/,
      message: "请输入正确的身份证号格式",
      trigger: "blur",
    },
  ],
});

// 监听props变化，初始化表单数据
watch(
  () => props.userCertificates,
  (newVal) => {
    if (newVal && newVal.length > 0) {
      // 从userCertificates中提取身份证相关信息
      newVal.forEach((cert) => {
        if (cert.CertificateType === "idCard") {
          certificates.value.idCard = cert.CertificateValue;
        } else if (cert.CertificateType === "idCard_front") {
          certificates.value.idCard_front = cert.CertificateValue ?? "";
        } else if (cert.CertificateType === "idCard_back") {
          certificates.value.idCard_back = cert.CertificateValue ?? "";
        }
      });
    }
  },
  { immediate: true }
);

// 比较当前表单数据与props数据是否相同
const isDataSameAsProps = (): boolean => {
  // 将当前表单数据转换为AuthenticationInfo格式
  const currentCertificates: UserCertificate[] = [];
  Object.entries(certificates.value).forEach(([key, value]) => {
    let certValue: string = "";
    if (value === null || value === undefined) {
      certValue = "";
    } else if (typeof value === "object") {
      certValue = JSON.stringify(value);
    } else {
      certValue = String(value);
    }

    const item: UserCertificate = {
      CertificateType: key,
      CertificateValue: certValue,
    };
    currentCertificates.push(item);
  });
  return true;
};

// 提交表单数据
const handleSubmit = (): Promise<UserCertificate[] | null> => {
  return new Promise((resolve) => {
    formRef.value?.clearValidate();
    formRef.value?.validate((valid: boolean) => {
      if (valid) {
        const userCertificates: UserCertificate[] = [];
        // 将身份证信息转换为AuthenticationInfo格式
        Object.entries(certificates.value).forEach(([key, value]) => {
          let certValue: string = "";
          if (value === null || value === undefined) {
            certValue = "";
          } else if (typeof value === "object") {
            certValue = JSON.stringify(value);
          } else {
            certValue = String(value);
          }

          const item: UserCertificate = {
            CertificateType: key,
            CertificateValue: certValue,
            UserId: props.userId,
          };
          userCertificates.push(item);
        });
        resolve(userCertificates);
      } else {
        resolve(null);
      }
    });
  });
};

defineExpose({
  handleSubmit,
});

onMounted(() => {
  if (props.disabled === true) {
    isPreview.value = true;
  }
});
</script>

<style lang="scss" scoped>
:deep(.el-form-item) {
  margin-bottom: 20px;
}

:deep(.el-form-item__label) {
  font-weight: 500;
}
</style>
