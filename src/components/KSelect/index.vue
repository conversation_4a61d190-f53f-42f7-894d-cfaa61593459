<template>
  <el-select
    v-model="innerModel"
    :placeholder="placeholder"
    :clearable="clearable"
    :disabled="disabled"
    :multiple="multiple"
    :multiple-limit="multipleLimit"
    :collapse-tags="collapseTags"
    :max-collapse-tags="maxCollapseTags"
    :filterable="filterable"
    :filter-method="filterMethod"
    :remote="remote && remoteMethod !== undefined && remoteMethod !== null"
    :remote-method="remoteMethod"
    :loading="loading"
    :empty-values="[undefined, null]"
    :value-on-clear="() => undefined"
    @change="onChange"
  >
    <el-option
      v-for="(item, index) in options"
      :key="`${index}-${item.label}-${item.value}`"
      :label="item.label"
      :value="item.value"
      :disabled="item.disabled"
    />
  </el-select>
</template>

<script setup lang="ts">
const kDebugEnabled = false;
defineOptions({
  name: "KSelect",
});

type OptionValue = string | number | boolean | object;
interface Option {
  label: string | number;
  value: OptionValue;
  disabled?: boolean;
}
const options = ref<Option[]>([]);

// 特殊标记字段，表示全部
const ALL_VALUE = "all";

interface Props {
  /**
   * 选项数据，默认从 data 中获取 label 和 value
   *
   * label（默认显示：`选项${index + 1}`）
   * value（默认值：undefined）
   * disabled（默认值：false）
   */
  data: Array<{ [key: string]: any }>;
  /**
   * 动态配置选项数据
   *
   * label: 选项的标签，默认 data 中的 'label' 字段
   * value: 选项的值，默认 data 中的 'value' 字段
   * disabled: 选项的禁用状态，默认 data 中的 'disabled' 字段
   */
  props?: {
    label?: string;
    value?: string;
    disabled?: string;
  };
  placeholder?: string;
  clearable?: boolean;
  multiple?: boolean;
  multipleLimit?: number;
  collapseTags?: boolean;
  maxCollapseTags?: number;
  disabled?: boolean;
  /** 是否显示"全部"选项 */
  showAll?: boolean;
  /**
   * 全部选项的配置
   *
   * label: 全部选项的标签，默认"全部"
   * value: 全部选项的值，默认 undefined
   *
   * 注意：如果是多选，则 value 不能为空
   */
  allProps?: { label?: string; value?: OptionValue | undefined };
  /** 其中的选项是否从服务器远程加载 */
  remote?: boolean;
  /** 自定义远程搜索方法 */
  remoteMethod?: (query: string) => void;
  /** 是否正在从远程获取数据 */
  loading?: boolean;
  /** 是否可过滤（开启输入框） */
  filterable?: boolean;
  /** 自定义过滤方法 */
  filterMethod?: () => void;
}

// 定义 props 并设置默认值
const props = withDefaults(defineProps<Props>(), {
  props: () => ({
    label: "label",
    value: "value",
    disabled: "disabled",
  }),
  placeholder: "请选择",
  clearable: true,
  multiple: false,
  multipleLimit: 0,
  collapseTags: false,
  maxCollapseTags: 1,
  filterable: false,
  remote: false,
  loading: false,
  disabled: false,
  showAll: false,
  allProps: () => ({
    label: "全部",
  }),
});

// 如果 props.data 为响应式数据，初始化之后，并更改了则重新动态映射选项
watch(
  () => props.data,
  () => {
    kDebugEnabled && console.debug("数据源更新", props.data);

    mapOptions(props.data);
  },
  { deep: true }
);

// 内部使用 model
type VModelTypeOrNull = string | number | boolean | object | Array<any> | undefined;
const innerModel = ref<VModelTypeOrNull>();

// 是否是通过选择 option 触发 model 的更新
const innerChangedModel = ref<boolean>(false);

// 外部定义 model
const model = defineModel<VModelTypeOrNull>({
  required: true,
});

// 外部更改了绑定的model，则重新赋值给 innerModel
watch(model, () => {
  kDebugEnabled && console.debug("model 更新", model.value);

  if (
    innerModel.value === model.value ||
    (innerModel.value === ALL_VALUE &&
      model.value === props.allProps!.value &&
      innerChangedModel.value)
  ) {
    innerChangedModel.value = false;
    return;
  }

  innerModel.value = model.value;
  innerChangedModel.value = false;
});

// 定义外部方法
const emit = defineEmits<{
  change: [value: OptionValue | OptionValue[] | undefined];
}>();

onMounted(() => {
  innerModel.value = model.value;
  mapOptions(props.data);
});

// 动态映射 data 中的字段
function mapOptions(data: Array<{ [key: string]: any }>) {
  const labelKey = props.props.label ?? "label";
  const valueKey = props.props.value ?? "value";
  const disabledKey = props.props.disabled ?? "disabled";
  const mappedOptions: Option[] = data.map((item, index) => {
    return {
      label: item[labelKey] ? item[labelKey] : `选项${index + 1}`,
      value: item[valueKey] !== undefined && item[valueKey] !== null ? item[valueKey] : "",
      disabled:
        item[disabledKey] !== undefined && item[disabledKey] !== null ? item[disabledKey] : false,
    };
  });

  // 如果需要显示"全部"选项，则添加"全部"选项
  if (props.showAll && mappedOptions.length > 0) {
    mappedOptions.unshift({
      label: props.allProps!.label!,
      value: props.allProps!.value ?? (props.multiple ? "" : ALL_VALUE),
      disabled: false,
    });
  }
  options.value = mappedOptions;
}

// 选中值发生变化时触发
function onChange(v: OptionValue | OptionValue[]) {
  innerChangedModel.value = true;

  // 多选
  if (Array.isArray(v)) {
    const values: (OptionValue | undefined)[] = v.map((item) => {
      if (item === ALL_VALUE) {
        return props.allProps!.value;
      }
      return item;
    });
    model.value = values;
    emit("change", values);
    return;
  }

  // 单选
  if (v === ALL_VALUE) {
    model.value = props.allProps!.value;
  } else {
    model.value = v;
  }
  emit("change", model.value);
}
</script>

<style lang="scss" scoped></style>
