<template>
  <div class="p-20px overflow-y-auto max-h-600px">
    <el-form ref="ruleFormRef" :model="formData" :rules="rules" label-width="auto">
      <el-form-item label="题干" prop="Title">
        <el-input v-model="formData.Title" :autofocus="true" placeholder="请输入题干" />
      </el-form-item>
      <el-row>
        <el-col v-if="formData.ProblemType === 1 || formData.ProblemType === 2" :span="12">
          <el-form-item label="题目类型">
            <el-radio-group v-model="formData.ProblemType">
              <el-radio :value="1">单选题</el-radio>
              <el-radio :value="2">多选题</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="是否必填" prop="IsRequired">
            <el-switch v-model="formData.IsRequired" />
          </el-form-item>
        </el-col>
      </el-row>
      <template v-if="formData.ProblemType === 1 || formData.ProblemType === 2">
        <el-form-item label="选项">
          <DragContainer
            v-model="formData.GaugeProblemDetails"
            class="flex-1"
            handle=".sort-handle"
          >
            <el-row
              v-for="(option, index) in formData.GaugeProblemDetails"
              :key="`${option.ProblemOption}-${option.Id}`"
              style="padding-bottom: 18px"
            >
              <el-form-item
                class="flex-1 mr-10px!"
                :prop="'GaugeProblemDetails.' + index + '.ProblemOption'"
                :rules="[{ required: true, message: '请输入选项', trigger: 'blur' }]"
              >
                <el-input v-model.number="option.ProblemOption" placeholder="请输入选项内容" />
              </el-form-item>
              <el-form-item
                class="mr-10px!"
                :prop="'GaugeProblemDetails.' + index + '.Points'"
                :rules="[
                  { validator: validatePoints, trigger: 'blur' },
                  { type: 'number', message: '分值为数字' },
                ]"
              >
                <el-input
                  v-model.number="option.Points"
                  type="number"
                  class="w-80px!"
                  placeholder="分值"
                />
              </el-form-item>
              <el-form-item class="mr-10px!">
                <el-icon size="20px" color="#f56c6c" @click="onDeleteOption(index)">
                  <Remove />
                </el-icon>
              </el-form-item>
              <el-form-item>
                <img class="sort-handle" src="@/assets/icons/sort.svg" alt="SVG Image" />
              </el-form-item>
            </el-row>
          </DragContainer>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="onAddOption">增加单个选项</el-button>
        </el-form-item>
      </template>
    </el-form>
  </div>
  <!-- 底部按钮 -->
  <div class="flex justify-end">
    <el-button @click="$emit('cancel')">取消</el-button>
    <el-button type="primary" @click="onSubmitForm(ruleFormRef)">确定</el-button>
  </div>
</template>

<script setup lang="ts">
import { GaugeProblem } from "@/api/training/types";
import { FormRules, FormInstance } from "element-plus";

const kEnableDebug = false;
defineOptions({
  name: "GaugeQuestion",
});

const emit = defineEmits(["cancel", "submit"]);

const props = defineProps<{
  question: GaugeProblem;
}>();

const formData = reactive<GaugeProblem>({});
const ruleFormRef = ref<FormInstance>();

const rules = reactive<FormRules<GaugeProblem>>({
  Title: [{ required: true, message: "请输入题干", trigger: "blur" }],
});

// 验证分值
const validatePoints = (rule: any, value: any, callback: any) => {
  const reg =
    /(^([-]?)[1-9]([0-9]+)?(\.[0-9]{1,2})?$)|(^([-]?)(0){1}$)|(^([-]?)[0-9]\.[0-9]([0-9])?$)/;
  // 编辑模式，才验证分数；添加模式不验证
  if (value !== undefined && !reg.test(value) && formData.Id) {
    callback(new Error("最多两位小数"));
  } else {
    callback();
  }
};

// 增加单个选项
function onAddOption() {
  formData.GaugeProblemDetails = formData.GaugeProblemDetails ?? [];
  formData.GaugeProblemDetails.push({});
}

// 删除单个选项
function onDeleteOption(index: number) {
  formData.GaugeProblemDetails!.splice(index, 1);
}

// 点击确认
function onSubmitForm(ruleFormRef: FormInstance | undefined) {
  if (!ruleFormRef) return;
  if (
    (formData.GaugeProblemDetails?.length ?? 0) < 2 &&
    (formData.ProblemType === 1 || formData.ProblemType === 2)
  ) {
    ElMessage.warning("请至少添加2个选项");
    return;
  }

  ruleFormRef.validate((valid, fields) => {
    if (valid) {
      emit("submit", formData);
    } else {
      kEnableDebug && console.debug("题目 - 提交失败", fields, formData);
    }
  });
}

onMounted(() => {
  const data = JSON.parse(JSON.stringify(props.question));
  Object.assign(formData, data);
});
</script>

<style lang="scss" scoped>
.sort-handle {
  cursor: move;
  cursor: grabbing;
}
</style>
