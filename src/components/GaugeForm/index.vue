<template>
  <div v-loading="formLoading" class="p-20px overflow-y-auto">
    <el-form
      ref="ruleFormRef"
      :model="formData"
      :rules="rules"
      label-width="auto"
      :inline="true"
      :disabled="props.disabled"
    >
      <!-- 标签页 -->
      <el-tabs v-model="activeTab" type="card">
        <el-tab-pane label="基本信息" :name="0">
          <!-- 基本信息 -->
          <div class="h-550px overflow-y-auto">
            <el-form-item label="量表名称" prop="Name">
              <el-input v-model="formData.Name" clearable placeholder="请输入量表名称" />
            </el-form-item>
            <el-form-item label="编码" prop="Code">
              <el-input v-model="formData.Code" clearable placeholder="请输入编码" />
            </el-form-item>
            <el-form-item label="是否启用">
              <el-switch v-model="formData.IsEnble" />
            </el-form-item>
            <el-form-item v-if="props.page === 'gauge'" label="是否默认推送" label-width="120px">
              <el-switch v-model="formData.IsDefaultPush" />
            </el-form-item>
            <el-row>
              <el-form-item label="康复分类" prop="DictType">
                <TagsSelect
                  v-model="selectedRecoveryTypes"
                  :options="props.recoveryTypes"
                  :props="{ label: 'Key', value: 'Key' }"
                  :disabled="props.disabled"
                />
              </el-form-item>
            </el-row>

            <el-form-item label="量表说明" prop="Remark" class="flex! w-full">
              <el-input
                v-model="formData.Remark"
                maxlength="500"
                show-word-limit
                placeholder="请输入量表说明"
                type="textarea"
                :rows="3"
              />
            </el-form-item>
            <el-card shadow="never" class="text-center mt-10px">
              <el-transfer
                v-model="selectedIds"
                class="flex items-center justify-center"
                style="height: 370px"
                filterable
                :filter-method="filterMethod"
                filter-placeholder="请输入名称"
                :titles="['待选病种', '已选病种']"
                :data="props.diseases"
                :props="{
                  key: 'Id',
                  label: 'Key',
                }"
              />
            </el-card>
          </div>
        </el-tab-pane>
        <el-tab-pane label="表单内容" :name="1">
          <!-- 表单内容 -->
          <div class="h-550px overflow-y-auto">
            <DragContainer v-model="formData.GaugeProblems" ghost-class="ghost-class">
              <div
                v-for="(question, index) in formData.GaugeProblems"
                :key="`${question.Title}-${question.Id}`"
                class="m-5px b-rd-4px border border-solid border-[#E7E7EE] p-20px bg-white"
              >
                <el-row align="middle">
                  <!-- 题目内容 -->
                  <span class="flex-1">
                    <div
                      :class="{
                        'mb-10px': question.ProblemType === 1 || question.ProblemType === 2,
                      }"
                    >
                      <el-text tag="b">{{ index + 1 }}.{{ question.Title }}</el-text>
                      <span v-if="question.IsRequired" style="color: red">*</span>
                    </div>
                    <div
                      v-for="(option, optionIndex) in question.GaugeProblemDetails"
                      v-if="question.ProblemType === 1 || question.ProblemType === 2"
                      :key="`${option.ProblemOption}-${option.Id}`"
                      :class="{
                        'mb-5px': optionIndex !== question.GaugeProblemDetails!.length - 1,
                      }"
                    >
                      <el-text
                        v-if="option.Points !== undefined && option.Points !== null"
                        class="pr-10px!"
                      >
                        {{ option.Points }}分
                      </el-text>
                      <el-text>{{ option.ProblemOption }}</el-text>
                    </div>
                  </span>
                  <!-- 操作按钮组 -->
                  <el-row v-if="!props.disabled" align="middle">
                    <el-icon size="40px" class="p-10px" @click="onCopyQuestion(index)">
                      <CopyDocument />
                    </el-icon>
                    <el-icon size="40px" class="p-10px" @click="onEditQuestion(index)">
                      <Edit />
                    </el-icon>
                    <el-icon
                      size="40px"
                      class="p-10px"
                      color="#f56c6c"
                      @click="onDeleteQuestion(index)"
                    >
                      <Delete />
                    </el-icon>
                  </el-row>
                </el-row>
              </div>
            </DragContainer>
            <el-row v-if="!props.disabled" id="addQuestionRow" justify="center">
              <el-button type="primary" @click="onAddQuestion(1)">添加一道选择题</el-button>
              <el-button type="primary" @click="onAddQuestion(3)">添加一道填空题</el-button>
              <el-button type="primary" @click="onAddQuestion(4)">添加一道分值题</el-button>
            </el-row>
          </div>
        </el-tab-pane>
        <el-tab-pane label="评估结果" :name="2">
          <!-- 评估结果 -->
          <div class="h-550px overflow-y-auto">
            <el-row v-for="(result, index) in formData.GaugeResults" :key="index">
              <el-form-item
                label="分值区间"
                :prop="'GaugeResults.' + index + '.StartPoint'"
                :rules="[
                  { validator: validatePoints, trigger: 'blur' },
                  { type: 'number', message: '分值为数字' },
                ]"
                class="mr-10px! items-center flex!"
              >
                <el-input
                  v-model.number="result.StartPoint"
                  class="w-100px!"
                  type="number"
                  placeholder="分值"
                />
              </el-form-item>
              <el-form-item label-width="0" class="mr-10px!">-</el-form-item>
              <el-form-item
                label-width="0"
                :prop="'GaugeResults.' + index + '.EndPoint'"
                :rules="[
                  { validator: validatePoints, trigger: 'blur' },
                  { type: 'number', message: '分值为数字' },
                ]"
                class="mr-15px!"
              >
                <el-input
                  v-model.number="result.EndPoint"
                  class="w-100px!"
                  type="number"
                  placeholder="分值"
                />
              </el-form-item>
              <el-form-item label-width="0" prop="Content" class="mr-10px! flex-1">
                <el-input v-model="result.Content" class="flex-1" placeholder="请输入结果" />
              </el-form-item>
              <el-form-item label-width="0" class="mr-10px!">
                <el-icon
                  v-if="!props.disabled"
                  size="40px"
                  color="#f56c6c"
                  @click="onDeleteResult(index)"
                >
                  <Remove class="p-10px" />
                </el-icon>
              </el-form-item>
            </el-row>
            <div v-if="!props.disabled" class="flex justify-center mt-10px">
              <el-button type="primary" @click="onAddResult">添加结果</el-button>
            </div>
          </div>
        </el-tab-pane>
      </el-tabs>
    </el-form>
  </div>
  <!-- 底部按钮 -->
  <div class="flex justify-end">
    <el-button @click="$emit('cancel')">取消</el-button>
    <el-button v-if="!props.disabled" type="primary" @click="onSubmitForm()">确定</el-button>
  </div>

  <!-- 新增/编辑题目弹框 -->
  <el-dialog
    v-model="showQuestionDialog.isShow"
    :title="showQuestionDialog.title"
    destroy-on-close
    width="700"
  >
    <GaugeQuestion
      :question="showQuestionDialog.question"
      @cancel="showQuestionDialog.isShow = false"
      @submit="onConfirmQuestion"
    />
  </el-dialog>
</template>

<script setup lang="ts">
import Training_Api from "@/api/training";
import { FormRules, FormInstance, TransferDataItem } from "element-plus";
import { useUserStore } from "@/store";
import { GaugeDetail, GaugeDiseaseRelation, GaugeProblem, GaugeResult } from "@/api/training/types";

const kEnableDebug = false;
const props = defineProps<{
  gauge: GaugeDetail;
  /** 康复分类 */
  recoveryTypes: ReadDict[];

  /** 疾病列表 */
  diseases: ReadDict[];

  /** 使用页面：表单管理、医院表单管理 */
  page: "gauge" | "hospitalForm";
  disabled: boolean;
}>();

const emit = defineEmits(["cancel", "submit"]);

onMounted(() => {
  const data = JSON.parse(JSON.stringify(props.gauge));
  Object.assign(formData, data);
  if (data.GaugeDiseaseRelations) {
    selectedIds.value = data.GaugeDiseaseRelations.map(
      (item: GaugeDiseaseRelation) => item.DiseaseId ?? ""
    );
  }
  formData.GaugeProblems = data.GaugeProblems ?? [];
  try {
    const types: string[] = JSON.parse(props.gauge.Types!);
    selectedRecoveryTypes.value = props.recoveryTypes.filter(
      (item: ReadDict) => item.Key && types.includes(item.Key)
    );
  } catch (error) {
    selectedRecoveryTypes.value = [];
  }
});

const activeTab = ref(0);
const formLoading = ref(false);
// 表单实例
const formRef = useTemplateRef<FormInstance>("ruleFormRef");
// 表单数据
const formData = reactive<GaugeDetail>({});
// 选中康复分类
const selectedRecoveryTypes = ref<ReadDict[]>([]);

// 表单验证规则
const rules = reactive<FormRules<GaugeDetail>>({
  Name: [{ required: true, message: "请输入量表名称", trigger: "blur" }],
  Code: [{ required: true, message: "请输入编码", trigger: "blur" }],
});
// 验证分值
const validatePoints = (rule: any, value: any, callback: any) => {
  if (value === undefined || value === null || value.length === 0) {
    callback();
    return;
  }

  kEnableDebug && console.log("validatePoints", value, typeof value);
  // 最多两位小数
  const reg =
    /(^([-]?)[1-9]([0-9]+)?(\.[0-9]{1,2})?$)|(^([-]?)(0){1}$)|(^([-]?)[0-9]\.[0-9]([0-9])?$)/;
  if (!reg.test(value)) {
    callback(new Error("最多两位小数"));
    return;
  }

  // 是否在其他区间
  const otherResults = formData.GaugeResults?.filter((item) => {
    if (item.StartPoint === undefined && item.EndPoint === undefined) {
      return false;
    } else if (item.StartPoint !== undefined) {
      return value >= item.StartPoint && value <= (item.EndPoint ?? 1000);
    } else {
      return value <= item.EndPoint!;
    }
  });
  if ((otherResults?.length ?? 0) > 1) {
    callback(new Error("区间重合"));
    return;
  }

  callback();
};

// 已选病种
const selectedIds = ref<string[]>([]);
// 过滤方法
const filterMethod = (query: string, item: TransferDataItem) => {
  return item?.Key?.includes(query);
};

const showQuestionDialog = reactive({
  isShow: false,
  title: "",
  disabled: false,
  question: {} as GaugeProblem, // 新增/编辑题目详情内容
  questionIndex: -1, // 正在操作的新增/编辑题目所在索引
});

// 添加题目
function onAddQuestion(type: number) {
  kEnableDebug && console.log("addQuestion", type);

  showQuestionDialog.question = {
    ProblemType: type,
    IsRequired: true,
    GaugeProblemDetails: type === 1 || type === 2 ? [{}, {}] : undefined, // 选择题至少2个选项
  };
  showQuestionDialog.questionIndex = formData.GaugeProblems!.length;
  showQuestionDialog.title = "添加题目";
  showQuestionDialog.disabled = false;
  showQuestionDialog.isShow = true;
}

// 复制题目
function onCopyQuestion(index: number) {
  kEnableDebug && console.log("copyQuestion", index);

  const question = formData.GaugeProblems![index];
  showQuestionDialog.question = {
    Title: question.Title,
    Sort: question.Sort,
    EvaluateGaugeId: question.EvaluateGaugeId,
    ProblemType: question.ProblemType,
    IsRequired: question.IsRequired,
    GaugeProblemDetails: question.GaugeProblemDetails?.map((item) => ({
      Answer: item.Answer,
      Points: item.Points,
      ProblemOption: item.ProblemOption,
      Sort: item.Sort,
    })),
  };
  showQuestionDialog.questionIndex = formData.GaugeProblems!.length;
  showQuestionDialog.title = "添加题目";
  showQuestionDialog.disabled = false;
  showQuestionDialog.isShow = true;
}

// 编辑题目
function onEditQuestion(index: number) {
  kEnableDebug && console.log("editQuestion", index);

  showQuestionDialog.question = formData.GaugeProblems![index];
  showQuestionDialog.questionIndex = index;
  showQuestionDialog.title = "编辑题目";
  showQuestionDialog.disabled = false;
  showQuestionDialog.isShow = true;
}

// 点击新增/编辑题目确认
async function onConfirmQuestion(question: GaugeProblem) {
  kEnableDebug && console.log("confirmQuestion", question);

  if (
    showQuestionDialog.questionIndex !== -1 &&
    showQuestionDialog.questionIndex < formData.GaugeProblems!.length
  ) {
    // 编辑
    formData.GaugeProblems![showQuestionDialog.questionIndex] = question;
  } else {
    // 新增
    formData.GaugeProblems!.push(question);
    await nextTick();
    // 滚动到底部
    const element = document.getElementById("addQuestionRow");
    if (element) {
      element.scrollIntoView({ behavior: "smooth" }); // 平滑滚动
    }
  }
  showQuestionDialog.question = {};
  showQuestionDialog.questionIndex = -1;
  showQuestionDialog.isShow = false;
}

// 删除题目
function onDeleteQuestion(index: number) {
  kEnableDebug && console.log("deleteQuestion", index);
  formData.GaugeProblems?.splice(index, 1);
}

// 删除评估结果
function onDeleteResult(index: number) {
  kEnableDebug && console.log("deleteResult", index);
  formData.GaugeResults?.splice(index, 1);
}

// 添加评估结果
function onAddResult() {
  kEnableDebug && console.log("addResult");
  formData.GaugeResults = formData.GaugeResults ?? [];
  const data: GaugeResult = {};
  if (formData.GaugeResults.length > 0) {
    const lastResult = formData.GaugeResults![formData.GaugeResults!.length - 1];
    if (lastResult.EndPoint !== undefined && lastResult.EndPoint !== null) {
      data.StartPoint = lastResult.EndPoint + 1;
    }
  }
  formData.GaugeResults?.push(data);
}

// 提交表单
function onSubmitForm() {
  if (!formRef.value) return;
  if (!formData.GaugeProblems?.length) {
    ElMessage.warning("请至少添加一道题目");
    activeTab.value = 1;
    return;
  }

  formRef.value.validate((valid, fields) => {
    if (valid) {
      requestAddOrUpdateGauge();
    } else {
      kEnableDebug && console.debug("量表 - 提交失败", fields, formData);
    }
  });
}

/** 添加更新量表 */
async function requestAddOrUpdateGauge() {
  kEnableDebug && console.log("requestAddOrUpdateGauge", formData);

  formLoading.value = true;
  const types = selectedRecoveryTypes.value.map((item) => item.Key ?? "");
  const params: GaugeDetail = {
    ...formData,
    IsEnble: formData.IsEnble ?? false,
    IsDefaultPush: formData.IsDefaultPush,
    Types: JSON.stringify(types),
    GaugeDiseaseRelations: selectedIds.value.map((id) => ({
      DiseaseId: id,
    })),
    GaugeProblems: formData.GaugeProblems?.map((item, index) => ({
      ...item,
      Sort: index,
      GaugeProblemDetails: item.GaugeProblemDetails?.map((option, optionIndex) => ({
        ...option,
        Sort: optionIndex,
      })),
    })),
  };
  if (!params.CreatorId) {
    params.CreatorId = useUserStore().userInfo.Id;
  }
  const r = await Training_Api.insertOrUpdateGauge(params);
  formLoading.value = false;
  if (r.Type === 200) {
    emit("submit");
  } else {
    ElMessage.error(r.Content);
  }
}
</script>

<style lang="scss" scoped>
// 拖拽占位符样式
.ghost-class {
  border-color: #4080ff;
  opacity: 0.5;
}

// 修改transfer组件样式
:deep(.el-transfer-panel) {
  flex: 1;
  width: 100% !important;
}
</style>
