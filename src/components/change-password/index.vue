<template>
  <div>
    <el-form
      ref="changePasswordFormRef"
      :model="changePasswordForm"
      :rules="rules"
      :disabled="isSubmitting"
      label-position="left"
      label-width="auto"
      @submit.prevent="submitChangePassword"
    >
      <el-form-item label="手机号" prop="phone" required>
        <el-input v-model="changePasswordForm.phone" type="text" disabled />
      </el-form-item>
      <el-form-item label="验证码" prop="code" required inline>
        <el-input v-model="changePasswordForm.code" type="text" clearable maxlength="4">
          <template #append>
            <el-button
              type="primary"
              :disabled="isSendingCode || codeCountdown > 0"
              :loading="isSendingCode"
              @click="sendCode"
            >
              {{
                isSendingCode
                  ? "发送中..."
                  : codeCountdown > 0
                    ? `重新发送(${codeCountdown}s)`
                    : "发送验证码"
              }}
            </el-button>
          </template>
        </el-input>
      </el-form-item>
      <el-form-item label="新密码" prop="password" required inline-message>
        <el-input
          v-model="changePasswordForm.password"
          type="password"
          clearable
          maxlength="12"
          show-password
        />
      </el-form-item>
      <el-form-item label="确认新密码" prop="confirmPassword" required>
        <el-input
          v-model="changePasswordForm.confirmPassword"
          type="password"
          clearable
          maxlength="12"
          show-password
        />
      </el-form-item>
      <div style="font-size: 12px; color: red; padding: 10px">
        {{ protectionStore.tipMessage }}
      </div>
      <div style="text-align: center">
        <el-button
          type="primary"
          size="default"
          style="width: 180px"
          native-type="submit"
          :loading="isSubmitting"
        >
          提交
        </el-button>
      </div>
    </el-form>
  </div>
</template>

<script setup lang="ts">
import { sendCaptcha } from "@/api/cloudInfra";
import PassportAPI from "@/api/passport";
import { useUserStoreHook } from "@/store";
import { useProtectionStore } from "@/store/modules/protection";
import { getToken } from "@/utils/auth";
import strongPasswordCheck from "@/utils/strong-password-check";
import { FormInstance, FormRules } from "element-plus";

const protectionStore = useProtectionStore();

const emit = defineEmits(["success"]);

const changePasswordFormRef = useTemplateRef<FormInstance>("changePasswordFormRef");
const isSubmitting = ref(false);
const changePasswordForm = reactive({
  phone: "",
  code: "",
  password: "",
  confirmPassword: "",
});
const rules: FormRules = {
  phone: [
    {
      validator: (rule, value, callback) => {
        if (!value) {
          callback(new Error("请输入手机号"));
        } else {
          callback();
        }
      },
      trigger: "blur",
    },
  ],
  code: [
    {
      validator: (rule, value, callback) => {
        if (!value) {
          callback(new Error("请输入验证码"));
        } else {
          callback();
        }
      },
      trigger: "blur",
    },
  ],
  password: [
    {
      validator: (rule, value, callback) => {
        if (!value) {
          callback(new Error("请输入密码"));
        } else {
          if (!strongPasswordCheck.check(value)) {
            callback(new Error(strongPasswordCheck.getWarnText()));
          }
          if (changePasswordForm.confirmPassword !== "") {
            changePasswordFormRef.value?.validateField("confirmPassword");
          }
          callback();
        }
      },
      trigger: "blur",
    },
  ],
  confirmPassword: [
    {
      validator: (rule, value, callback) => {
        if (!value) {
          callback(new Error("请再次输入密码"));
        } else if (value !== changePasswordForm.password) {
          callback(new Error("两次输入密码不一致!"));
        } else {
          callback();
        }
      },
      trigger: "blur",
    },
  ],
};

const isSendingCode = ref(false);
const codeCountdown = ref(0);
const sessionId = ref("");

onMounted(() => {
  changePasswordForm.phone = useUserStoreHook().userInfo.PhoneNumber || "";
});

const sendCode = async () => {
  isSendingCode.value = true;
  const res = await sendCaptcha({
    phoneNumber: changePasswordForm.phone,
    templateType: "UpdatePwd",
    token: getToken(),
  });
  // await new Promise((resolve) => setTimeout(resolve, 1000));
  isSendingCode.value = false;
  if (res.Type != 200) {
    ElMessage.error(res.Content);
    return;
  }

  sessionId.value = res.Data;
  codeCountdown.value = 60;
  const timer = setInterval(() => {
    codeCountdown.value--;
    if (codeCountdown.value <= 0) {
      clearInterval(timer);
    }
  }, 1000);
};

const submitChangePassword = () => {
  // console.log("submitChangePassword");
  changePasswordFormRef.value?.validate(async (valid, errorFields) => {
    if (valid) {
      isSubmitting.value = true;
      const res = await PassportAPI.changePassword({
        sessionId: sessionId.value,
        captcha: changePasswordForm.code,
        newPassword: changePasswordForm.password,
      });

      // await new Promise((resolve) => setTimeout(resolve, 1000));
      isSubmitting.value = false;
      if (res.Type != 200) {
        ElMessage.error(res.Content);
        return;
      }

      ElNotification.success("修改密码成功");

      protectionStore.reset();
      emit("success");
    } else {
      console.warn("error submit!!", errorFields);
    }
  });
};
</script>
