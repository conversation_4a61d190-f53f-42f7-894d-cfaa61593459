export enum OrderStateEnum {
  // ['待支付', '待发货', '待收货', '已完成', '已取消'], // 订单状态 0:待支付 1：已支付/待发货 2：已发货/待收货 3、已完成  已取消
  Pending = 0, // 待支付
  Paid = 1, // 已支付/待发货
  Shipped = 2, // 已发货/待收货
  Completed = 3, // 已完成
  Canceled = 4, // 已取消
}
/**
 * 支付方式
 */
export enum PayTypeEnum {
  WeChat = 0, // 微信
  Alipay = 1, // 支付宝
  Free = 2, // 免费
  Other = 3, // 其他
}
/** 协诊状态 */
export enum ConsultationStateEnum {
  Pending = 1, // 待接受
  Canceled = 2, // 已取消
  Accepted = 3, // 已接受
  Completed = 4, // 已结束
  Rejected = 5, // 已拒绝
  Expired = 6, // 已过期
}
