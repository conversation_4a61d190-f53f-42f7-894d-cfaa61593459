@use "./reset";

.app-container {
  padding: 10px;
  height: 100%;
}

.search-bar {
  padding: 18px 10px 0 10px;
}

.table-container > .el-card__header {
  padding: calc(var(--el-card-padding) - 8px) var(--el-card-padding);
}

.link-type,
.link-type:focus {
  color: #337ab7;
  cursor: pointer;

  &:hover {
    color: rgb(32 160 255);
  }
}
.el-form--inline {
  .el-form-item {
    .el-input,
    .el-cascader,
    .el-select,
    .el-autocomplete {
      width: 180px;
    }
  }
}
.el-form--inline .el-form-item {
  margin-right: 12px !important;
  // margin-bottom: 12px !important; // 底部距离小于18px 会导致表单项错误提示显示不全
}
.el-radio {
  margin-right: 12px !important;
}
.el-scrollbar__bar.is-horizontal {
  height: 10px !important;
}
.el-scrollbar__bar.is-vertical {
  width: 10px !important;
}
.menu {
  width: 80px;
  position: fixed;
  border-radius: 10px;
  border: 1px solid #eeeeee;
  background-color: #ffffff;
  color: var(--el-color-primary);
  z-index: 999;
  padding: 0;
  .menu_item {
    line-height: 30px;
    text-align: center;
  }
  li:hover {
    background-color: var(--el-color-primary);
    color: white;
  }
  li {
    font-size: 14px;
    list-style: none;
  }
}
