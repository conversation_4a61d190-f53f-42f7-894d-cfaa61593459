import { kDebug } from "./utils";

let lastScripts: string[] | null = null;

const scriptReg = /<script[^>]*src="([^"]*)"[^>]*>/gm;
/**获取最新页面中的script链接 */
function getScriptSrc(html: string) {
  scriptReg.lastIndex = 0;
  const result = [];
  let match;
  while ((match = scriptReg.exec(html))) {
    result.push(match[1]);
  }
  return result;
}

async function needUpdate() {
  const res = await fetch("/?_timestamp=" + Date.now());
  if (!res.ok) return false;

  const html = await res.text();
  if (!html) return false;

  const scripts = getScriptSrc(html);
  // console.log(scripts, lastScripts);

  if (lastScripts) {
    if (scripts.length !== lastScripts.length) {
      return true;
    }
    for (let index = 0; index < scripts.length; index++) {
      if (scripts[index] !== lastScripts[index]) {
        return true;
      }
    }
  }

  lastScripts = scripts;
  return false;
}

// 每隔20s 检查一次
const TIMEOUT = 20000;
function autoRefresh() {
  if (kDebug) return;

  setTimeout(async () => {
    const willUpdate = await needUpdate().catch(() => false);
    if (willUpdate) {
      const r = confirm("页面有更新，是否重新加载");
      if (r) {
        // location.reload(true); // 有兼容性问题，仅在 Firefox 中支持
        // 添加时间戳参数确保获取最新资源
        const currentUrl = location.href;
        const separator = currentUrl.includes("?") ? "&" : "?";
        location.href = currentUrl + separator + "_timestamp=" + Date.now();
      }
    }
    autoRefresh();
  }, TIMEOUT);
}

autoRefresh();
