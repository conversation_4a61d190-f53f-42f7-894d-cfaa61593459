<template>
  <div class="app-container">
    <BaseTableSearchContainer @size-changed="tableResize">
      <template #search>
        <TBSearchContainer>
          <template #left>
            <el-form ref="queryFormRef" label-position="right" :model="queryParams" :inline="true">
              <el-form-item label="类型" prop="TreatType">
                <el-select
                  v-model="queryParams.TreatType"
                  placeholder="请选择"
                  clearable
                  :empty-values="[null, undefined, '']"
                  :value-on-clear="() => null"
                >
                  <el-option label="居家" :value="AdviceMoItemUseScope.Home" />
                  <el-option label="线下" :value="AdviceMoItemUseScope.Community" />
                  <el-option label="院内" :value="AdviceMoItemUseScope.Hospital" />
                </el-select>
              </el-form-item>
              <el-form-item v-if="!props.orgId" label="医院" prop="OrgIds">
                <el-select
                  v-model="queryParams.OrgIds"
                  placeholder="请选择"
                  multiple
                  collapse-tags
                  filterable
                  clearable
                  :empty-values="[undefined, null, '']"
                  :value-on-clear="() => []"
                  collapse-tags-tooltip
                  @change="handleFetchDeptList"
                >
                  <el-option
                    v-for="item in organizationList"
                    :key="item.Id"
                    :label="item.Name"
                    :value="item.Id!"
                  />
                </el-select>
              </el-form-item>
              <el-form-item label="科室" prop="DeptIds">
                <el-select
                  v-model="queryParams.DeptIds"
                  placeholder="请选择"
                  :disabled="queryParams.OrgIds.length !== 1"
                  multiple
                  collapse-tags
                  filterable
                  clearable
                  collapse-tags-tooltip
                  :empty-values="[undefined, null, '']"
                  :value-on-clear="() => []"
                >
                  <el-option
                    v-for="item in deptList"
                    :key="item.Id"
                    :label="item.Name"
                    :value="item.Id!"
                  />
                </el-select>
              </el-form-item>
              <el-form-item label="创建人" prop="CreatorIds">
                <UserSelect
                  v-model="queryParams.CreatorIds"
                  :role-types="['doctor', 'therapist', 'nurse']"
                  multiple
                  :org-ids="props.orgId ? [props.orgId] : null"
                />
              </el-form-item>
              <el-form-item label="医嘱" prop="MoItemName">
                <el-select-v2
                  v-model="queryParams.MoItemName"
                  placeholder="请选择"
                  filterable
                  clearable
                  :empty-values="[undefined, null, '']"
                  value-on-clear=""
                  :options="moItemList"
                  :props="{
                    label: 'Name',
                    value: 'Name',
                  }"
                />
              </el-form-item>
              <el-form-item label="是否启用" prop="IsEnable">
                <el-select
                  v-model="queryParams.IsEnable"
                  placeholder="请选择"
                  clearable
                  :empty-values="[undefined, '', null]"
                  :value-on-clear="() => null"
                  style="width: 100px"
                >
                  <el-option label="是" :value="true" />
                  <el-option label="否" :value="false" />
                </el-select>
              </el-form-item>
              <el-form-item label="关键字" prop="KeyWord">
                <el-input
                  v-model="queryParams.KeyWord"
                  placeholder="输入关键字筛选"
                  @keyup.enter="handleQuery"
                />
              </el-form-item>
            </el-form>
          </template>
          <template #right>
            <el-button type="primary" icon="search" @click="handleQuery">搜索</el-button>
            <template v-if="!props.from">
              <el-button type="primary" @click="handleOperationClick">批量操作</el-button>
              <el-button type="primary" @click="handleAdd">新增</el-button>
            </template>
          </template>
        </TBSearchContainer>
      </template>
      <template #table>
        <el-table
          ref="tableRef"
          v-loading="tableLoading"
          :data="pageData"
          highlight-current-row
          row-key="Id"
          border
          :height="props.from === 'pushHospitalServicePackage' ? '600' : tableFluidHeight"
          style="flex: 1; text-align: center"
          @select="handleTableSelect"
          @select-all="handleTableSelect"
        >
          <el-table-column type="selection" width="55" align="center" reserve-selection />
          <el-table-column
            prop="PrescriptionName"
            label="方案名称"
            :show-overflow-tooltip="true"
            align="center"
            width="140"
          />
          <el-table-column label="类型" width="100" align="center">
            <template #default="scope">
              {{ ["", "居家", "线下", "院内"][scope.row.TreatType] }}
            </template>
          </el-table-column>
          <el-table-column
            prop="OrgName"
            label="医院"
            :show-overflow-tooltip="true"
            align="center"
            width="120"
          />
          <el-table-column
            prop="DeptName"
            label="科室"
            :show-overflow-tooltip="true"
            align="center"
            width="100"
          />
          <el-table-column
            prop="CreatorName"
            label="创建人"
            :show-overflow-tooltip="true"
            align="center"
            width="80"
          />
          <el-table-column
            prop="DoctorName"
            label="医生"
            :show-overflow-tooltip="true"
            align="center"
            width="80"
          />
          <el-table-column
            prop="TherapistName"
            label="治疗师"
            :show-overflow-tooltip="true"
            align="center"
            width="80"
          />
          <el-table-column
            prop="NurseName"
            label="护士"
            :show-overflow-tooltip="true"
            align="center"
            width="80"
          />
          <el-table-column
            label="诊断"
            prop="DiagnoseName"
            width="120"
            align="center"
            show-overflow-tooltip
          />
          <el-table-column label="医嘱明细" width="220" align="center">
            <template #default="scope">
              <div v-for="item in handleGetMoItemDetail(scope.row.Detail)" :key="item">
                {{ item }}
              </div>
            </template>
          </el-table-column>
          <el-table-column
            label="部位"
            prop="PartName"
            width="150"
            align="center"
            show-overflow-tooltip
          />
          <el-table-column label="费用" prop="TotalAmount" width="80" align="center" />
          <el-table-column label="是否启用" width="80" align="center">
            <template #default="scope">
              <el-switch
                v-model="scope.row.IsEnable"
                :disabled="!!props.from"
                active-color="#3ab078"
                inactive-color="#e7e9ee"
                @change="(e) => handleIsEnableChange(e as boolean, scope.row.Id)"
              />
            </template>
          </el-table-column>
          <el-table-column label="二维码" width="100" align="center">
            <template #default="scope">
              <el-button
                link
                type="primary"
                :loading="loadingQRCode.has(scope.row.Id)"
                @click="handleQrCodeClick(scope.row)"
              >
                查看
              </el-button>
              <el-button link type="primary" @click="handleSalesQrCodeClick(scope.row)">
                销售二维码
              </el-button>
            </template>
          </el-table-column>
          <el-table-column fixed="right" label="操作" width="140" align="center">
            <template #default="scope">
              <el-button
                link
                size="small"
                type="primary"
                @click="handlePreviewOrEdit(scope.row, true)"
              >
                查看
              </el-button>
              <template v-if="!props.from">
                <el-button
                  link
                  size="small"
                  type="primary"
                  @click="handlePreviewOrEdit(scope.row, false)"
                >
                  编辑
                </el-button>
                <el-button link size="small" type="primary" @click="handleDelete(scope.row)">
                  删除
                </el-button>
              </template>
            </template>
          </el-table-column>
        </el-table>
      </template>
      <template #pagination>
        <Pagination
          v-if="total > 0"
          v-model:total="total"
          v-model:page="queryParams.PageIndex"
          v-model:limit="queryParams.PageSize"
          @pagination="handleGetTableList"
        />
      </template>
    </BaseTableSearchContainer>

    <div v-show="pushMenuShow" ref="menuRef">
      <ul id="menu" class="menu" :style="'top:' + clickPoint.y + 'px;left:' + clickPoint.x + 'px;'">
        <li class="menu_item" @click="handleEnableOperationClick(true)">启用</li>
        <li class="menu_item" @click="handleEnableOperationClick(false)">停用</li>
      </ul>
    </div>
    <el-dialog v-model="dialogVisible.qrCode" title="二维码" width="360" class="qr-dialog">
      <div class="qr-container">
        <div class="creator-info">
          <div class="creator-name">{{ qrCodeInfo?.CreatorName }}</div>
          <div class="creator-detail">
            <span>{{ qrCodeInfo?.CreatorDptName }}</span>
            <span class="divider">|</span>
            <span>{{ qrCodeInfo?.CreatorWorkTitle }}</span>
          </div>
        </div>
        <div class="prescription-info">
          <div class="prescription-title">
            <span class="prescription-name">{{ qrCodeInfo?.PrescriptionName }}</span>
            <span class="prescription-days">{{ qrCodeInfo?.ExecutDay }}</span>
          </div>
        </div>
        <div class="qr-wrapper">
          <QRCode :value="qrCodeInfo?.qrCodeUrl || ''" :size="200" />
        </div>
        <div class="scan-tip">
          <i class="el-icon-wechat" />
          <span>微信扫一扫获取治疗方案</span>
        </div>
      </div>
    </el-dialog>
    <el-dialog v-model="dialogVisible.salesQrCode" title="二维码" width="360" class="qr-dialog">
      <div class="qr-container">
        <FormItem label="销售" required>
          <UserSelect
            ref="salesUserSelectRef"
            v-model="salesQrCodeInfo!.SalesId"
            style="width: 120px"
            @change="handleSalesIdChange"
          />
        </FormItem>
        <div class="creator-info">
          <div class="creator-name">
            {{
              salesQrCodeInfo?.CreatorName +
              (salesQrCodeInfo?.SalesId ? "-" + salesQrCodeInfo?.SalesName : "")
            }}
          </div>
          <div class="creator-detail">
            <span>{{ salesQrCodeInfo?.CreatorDptName }}</span>
            <span class="divider">|</span>
            <span>{{ salesQrCodeInfo?.CreatorWorkTitle }}</span>
          </div>
        </div>
        <div class="prescription-info">
          <div class="prescription-title">
            <span class="prescription-name">
              {{ salesQrCodeInfo?.PrescriptionName }}
            </span>
            <span class="prescription-days">{{ salesQrCodeInfo?.ExecutDay }}</span>
          </div>
        </div>
        <div v-if="salesQrCodeInfo?.SalesId" v-loading="salesQrCodeLoading" class="qr-wrapper">
          <QRCode :value="salesQrCodeInfo?.qrCodeUrl || ''" :size="200" :need-upload="false" />
        </div>
        <div class="scan-tip">
          <i class="el-icon-wechat" />
          <span>微信扫一扫获取治疗方案</span>
        </div>
      </div>
    </el-dialog>
    <el-dialog
      v-model="dialogVisible.treatmentSchemeContent"
      :title="treatmentSchemeContentTitle"
      width="800"
      destroy-on-close
      :close-on-click-modal="isOnlyPreview"
      :close-on-press-escape="isOnlyPreview"
    >
      <TreatmentSchemeContent ref="treatmentSchemeContentRef" :scheme-detail="schemeDetail" />
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialogVisible.treatmentSchemeContent = false">取消</el-button>
          <el-button
            v-if="!isOnlyPreview"
            type="primary"
            :loading="dialogConfirmLoading"
            @click="handleTreatmentSchemeContentSubmit"
          >
            确定
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { useTemplateRef } from "vue";
import Consult_Api from "@/api/consult";
import {
  PlatformRxTempInputDTO,
  PlatformRxTempItem,
  RxTemplateDetail,
  SaveQRCodeInputDTO,
} from "@/api/consult/types";
import Content_Api from "@/api/content";
import { MoItemPageData } from "@/api/content/types";
import { AdviceMoItemUseScope } from "@/enums/AdviceEnum";
import { getBaseOrganizationList, getDeptList, getDiseaseList } from "@/utils/dict";
import { useTableConfig } from "@/hooks/useTableConfig";
import { useClickPoint } from "@/hooks/useClickPoint";
import { useUserStore } from "@/store";
import TreatmentSchemeContent from "./components/TreatmentSchemeContent.vue";

const userStore = useUserStore();
const menuRef = useTemplateRef("menuRef");
const { tableLoading, pageData, total, tableRef, selectedTableIds, tableFluidHeight, tableResize } =
  useTableConfig<PlatformRxTempItem>();
const { clickPoint, handleClick } = useClickPoint();
const schemeDetail = ref<RxTemplateDetail | null>(null);
defineOptions({
  name: "CommonSchemeManagement",
});
onClickOutside(menuRef, (event) => {
  pushMenuShow.value = false;
});
interface DialogVisible {
  qrCode: boolean;
  treatmentSchemeContent: boolean;
  salesQrCode: boolean;
}
interface QrCodeInfo {
  qrCodeUrl: string;
  CreatorName: string;
  CreatorDptName: string;
  CreatorWorkTitle: string;
  PrescriptionName: string;
  ExecutDay: string;
}
interface SalesQrCodeInfo {
  qrCodeUrl: string;
  CreatorName: string;
  CreatorDptName: string;
  CreatorWorkTitle: string;
  PrescriptionName: string;
  ExecutDay: string;
  SalesId: string;
  SalesName: string;
  SchemeId: string;
  CreatorId: string;
}
const organizationList = ref<BaseOrganization[]>([]);
const deptList = ref<BaseDepartment[]>([]);
const diseaseList = ref<ReadDict[]>([]);
const moItemList = ref<MoItemPageData[]>([]);
const pushMenuShow = ref<boolean>(false);
const dialogConfirmLoading = ref<boolean>(false);
const qrCodeInfo = ref<QrCodeInfo | null>(null);
const salesQrCodeInfo = ref<SalesQrCodeInfo | null>(null);
const isOnlyPreview = ref<boolean>(false);
const salesQrCodeLoading = ref<boolean>(false);
const loadingQRCode = ref<Set<string>>(new Set());
const salesUserSelectRef = useTemplateRef("salesUserSelectRef");
const treatmentSchemeContentTitle = ref<string>("添加常用方案");
const treatmentSchemeContentRef = ref<InstanceType<typeof TreatmentSchemeContent> | null>(null);
const dialogVisible = ref<DialogVisible>({
  qrCode: false,
  treatmentSchemeContent: false,
  salesQrCode: false,
});
const queryParams = ref<PlatformRxTempInputDTO>({
  TreatType: null,
  CreatorIds: [],
  OrgIds: [],
  DeptIds: [],
  MoItemName: "",
  IsEnable: null,
  KeyWord: "",
  PageIndex: 1,
  PageSize: 20,
  Scopeable: 1,
});
provide("deptList", deptList);
provide("diseaseList", diseaseList);
provide("isOnlyPreview", isOnlyPreview);

const handleFetchOrganizationList = async () => {
  organizationList.value = await getBaseOrganizationList({
    IsEnabled: true,
    Keyword: "",
    DtoTypeName: "QueryOrgDtoForDropDownList",
    PageIndex: 1,
    PageSize: 1000,
    Pageable: true,
    Scopeable: true,
  });
};
const handleFetchDeptList = async () => {
  queryParams.value.DeptIds = [];
  deptList.value = await getDeptList({
    OrgId: queryParams.value.OrgIds[0],
  });
};
const handleFetchMoItemList = async () => {
  const res = await Content_Api.getMoItemPageData({
    isEnable: true,
    page: 1,
    pageSize: 10000,
    organizationIds: props.orgId ? [props.orgId] : null,
  });
  if (res.Type === 200) {
    moItemList.value = res.Data.Data;
  } else {
    moItemList.value = [];
  }
};
const handleQuery = () => {
  queryParams.value.PageIndex = 1;
  handleGetTableList();
};
const handleGetTableList = async () => {
  const copyData = JSON.parse(JSON.stringify(queryParams.value));
  Object.keys(copyData).forEach((key) => {
    if (copyData[key] === null) {
      delete copyData[key];
    }
  });
  tableLoading.value = true;
  const res = await Consult_Api.getPlatformRxTemp(copyData);
  if (res.Type === 200) {
    pageData.value = res.Data.Data;
    total.value = res.Data.TotalCount;
  }
  tableLoading.value = false;
};
const handleIsEnableChange = async (e: boolean, Id: string) => {
  const res = await Consult_Api.updateRxTemplateEnable({ Id, IsEnable: e });
  if (res.Type === 200) {
    ElNotification.success(res.Content);
  } else {
    ElNotification.warning(res.Content);
    handleGetTableList();
  }
};
const handleGetMoItemDetail = (detail: string): string[] => {
  if (!detail) return [];
  return detail.split("\n").map((item) => item.trim());
};
const handleTableSelect = (selection: PlatformRxTempItem[]) => {
  selectedTableIds.value = selection.map((item) => item.Id);
};
const handleOperationClick = (event: MouseEvent) => {
  if (!selectedTableIds.value.length) {
    ElMessage.warning("请选择常用方案");
    return;
  }
  handleClick(event);
  clickPoint.value.x = event.clientX - 90;
  clickPoint.value.y = event.clientY + 10;
  pushMenuShow.value = true;
};
const handleEnableOperationClick = (e: boolean) => {
  ElMessageBox.confirm("确定要" + (e ? "启用" : "停用") + "吗？", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(() => {
    Consult_Api.batchOperationRxTemp({ Ids: selectedTableIds.value, IsEnable: e })
      .then((res) => {
        if (res.Type === 200) {
          ElMessage.success("操作成功");
          // 清空选中
          selectedTableIds.value = [];
          tableRef.value?.clearSelection();
          handleGetTableList();
        } else {
          ElNotification.error(res.Content);
        }
      })
      .catch(() => {
        ElNotification.error("操作失败");
      });
  });
};

const onCheckIsHaveCreatorIdCard = (row: PlatformRxTempItem) => {
  !row.CreatorIdCard && ElMessage.warning("该创建人未实名认证，请先联系其完成实名认证");
  return row.CreatorIdCard;
};
const handleQrCodeClick = async (row: PlatformRxTempItem) => {
  if (!onCheckIsHaveCreatorIdCard(row)) {
    return;
  }
  if (row.QuickPrescriptionId) {
    qrCodeInfo.value = {
      qrCodeUrl: `https://oss-biz.kangfx.com?Type=1&QuickPrescriptionId=${row.QuickPrescriptionId}&CreatorId=${row.CreatorId}`,
      CreatorName: row.CreatorName,
      CreatorDptName: row.CreatorDptName,
      CreatorWorkTitle: row.CreatorWorkTitle,
      PrescriptionName: row.PrescriptionName,
      ExecutDay: `(${row.ExecutDay}天)`,
    };
    dialogVisible.value.qrCode = true;
  } else {
    const data: SaveQRCodeInputDTO = {
      Type: 1,
      RelatedType: 1,
      RelatedId: row.Id,
      CreatorId: userStore.userInfo.Id,
      IsMain: true,
    };
    loadingQRCode.value.add(row.Id);
    const res = await Consult_Api.saveQRCode(data);
    if (res.Type === 200) {
      qrCodeInfo.value = {
        qrCodeUrl: `https://oss-biz.kangfx.com?Type=1&QuickPrescriptionId=${res.Data}&CreatorId=${row.CreatorId}`,
        CreatorName: row.CreatorName,
        CreatorDptName: row.CreatorDptName,
        CreatorWorkTitle: row.CreatorWorkTitle,
        PrescriptionName: row.PrescriptionName,
        ExecutDay: `(${row.ExecutDay}天)`,
      };
      dialogVisible.value.qrCode = true;
    } else {
      ElNotification.error("二维码获取失败");
    }
    loadingQRCode.value.delete(row.Id);
  }
};
const handleSalesQrCodeClick = async (row: PlatformRxTempItem) => {
  if (!onCheckIsHaveCreatorIdCard(row)) {
    return;
  }
  salesQrCodeInfo.value = {
    CreatorId: row.CreatorId,
    CreatorName: row.CreatorName,
    SalesName: "",
    CreatorDptName: row.CreatorDptName,
    CreatorWorkTitle: row.CreatorWorkTitle,
    PrescriptionName: row.PrescriptionName,
    ExecutDay: `(${row.ExecutDay}天)`,
    SalesId: "",
    SchemeId: row.Id,
    qrCodeUrl: "",
  };
  dialogVisible.value.salesQrCode = true;
};
const handleSalesIdChange = (e: any) => {
  if (!e) {
    return;
  }
  salesQrCodeLoading.value = true;
  const name = salesUserSelectRef.value?.userOptions.find((item) => item.Id === e)?.Name;
  salesQrCodeInfo.value!.SalesName = name!;
  handleSaveSalesQrCode();
};
const handleSaveSalesQrCode = async () => {
  const res = await Consult_Api.saveQRCode({
    Type: 1,
    RelatedType: 1,
    RelatedId: salesQrCodeInfo.value!.SchemeId,
    CreatorId: userStore.userInfo.Id,
    IsMain: false,
    ExtendUserId: salesQrCodeInfo.value!.SalesId,
  });
  if (res.Type === 200) {
    salesQrCodeInfo.value!.qrCodeUrl = `https://oss-biz.kangfx.com?Type=1&QuickPrescriptionId=${res.Data}&CreatorId=${salesQrCodeInfo.value!.CreatorId}`;
    salesQrCodeLoading.value = false;
  } else {
    ElNotification.error("二维码获取失败");
  }
};
const handleAdd = () => {
  isOnlyPreview.value = false;
  treatmentSchemeContentTitle.value = "添加常用方案";
  schemeDetail.value = null;
  dialogVisible.value.treatmentSchemeContent = true;
};
const handleDelete = (row: PlatformRxTempItem) => {
  ElMessageBox.confirm("确定要删除吗？", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(() => {
    Consult_Api.deleteRxTemplate({ TemplateId: row.Id })
      .then((res) => {
        if (res.Type === 200) {
          ElNotification.success("删除成功");
          handleGetTableList();
        } else {
          ElNotification.error(res.Content);
        }
      })
      .catch(() => {
        ElNotification.error("删除失败");
      });
  });
};
const handlePreviewOrEdit = async (row: PlatformRxTempItem, isPreview: boolean) => {
  console.log("查看/编辑", row, isPreview);
  isOnlyPreview.value = isPreview;
  treatmentSchemeContentTitle.value = isPreview
    ? `查看${row.PrescriptionName}常用方案`
    : `编辑${row.PrescriptionName}常用方案`;
  const res = await Consult_Api.getRxTemplateById({
    templateId: row.Id,
  });
  if (res.Type === 200) {
    schemeDetail.value = res.Data;
  }
  dialogVisible.value.treatmentSchemeContent = true;
};
const fetchDiseaseList = async () => {
  const list = await getDiseaseList();
  diseaseList.value = list;
};
const handleTreatmentSchemeContentSubmit = async () => {
  const sendData = await treatmentSchemeContentRef.value?.handleSubmit();
  if (!sendData) {
    return;
  }
  console.log("sendData", sendData);
  const func = sendData.PrescriptionInput.RxTempId
    ? Consult_Api.updateRxTemplate
    : Consult_Api.saveRxTemplate;
  dialogConfirmLoading.value = true;
  func(sendData)
    .then((res) => {
      if (res.Type === 200) {
        ElNotification.success("操作成功");
        dialogVisible.value.treatmentSchemeContent = false;
        handleGetTableList();
      } else {
        ElNotification.error(res.Content);
      }
    })
    .finally(() => {
      dialogConfirmLoading.value = false;
    });
};
onMounted(() => {
  handleFetchOrganizationList();
  handleFetchMoItemList();
  fetchDiseaseList();
});
onUnmounted(() => {
  sessionStorage.removeItem("schemeCreatorInfo");
});
onActivated(() => {
  // 调用时机为首次挂载
  // 以及每次从缓存中被重新插入时
  handleGetTableList();
});
interface Props {
  from: string;
  orgId?: string;
}
const props = defineProps<Props>();
watch(
  () => props.orgId,
  (newVal) => {
    if (newVal) {
      queryParams.value.OrgIds = [newVal];
      handleFetchDeptList();
      handleGetTableList();
    }
  },
  {
    immediate: true,
  }
);
const handleGetSelectIds = (): string[] => {
  if (!selectedTableIds.value.length) {
    return [];
  }
  return selectedTableIds.value;
};
defineExpose({
  handleGetSelectIds,
});
</script>

<style lang="scss" scoped>
.menu {
  position: fixed;
  z-index: 999;
  width: 80px;
  padding: 0;
  color: var(--el-color-primary);
  background-color: #fff;
  border: 1px solid #eee;
  border-radius: 10px;

  .menu_item {
    line-height: 30px;
    text-align: center;
  }

  li:hover {
    color: white;
    background-color: var(--el-color-primary);
  }

  li {
    font-size: 14px;
    list-style: none;
  }
}

.qr-dialog {
  :deep(.el-dialog__header) {
    padding-bottom: 15px;
    margin-right: 0;
    border-bottom: 1px solid #eee;
  }

  :deep(.el-dialog__body) {
    padding: 20px;
  }
}

.qr-container {
  display: flex;
  flex-direction: column;
  align-items: center;

  .creator-info {
    margin-bottom: 16px;
    text-align: center;

    .creator-name {
      margin-bottom: 8px;
      font-size: 18px;
      font-weight: 600;
      color: #333;
    }

    .creator-detail {
      font-size: 14px;
      color: #666;

      .divider {
        margin: 0 8px;
        color: #ddd;
      }
    }
  }

  .prescription-info {
    margin-bottom: 24px;
    text-align: center;

    .prescription-title {
      display: flex;
      gap: 8px;
      align-items: center;
      justify-content: center;

      .prescription-name {
        font-size: 16px;
        color: #333;
      }

      .prescription-days {
        font-size: 14px;
        color: #666;
      }
    }
  }

  .qr-wrapper {
    padding: 15px;
    margin-bottom: 20px;
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 12px rgb(0 0 0 / 10%);
  }

  .scan-tip {
    display: flex;
    align-items: center;
    font-size: 14px;
    color: #666;

    i {
      margin-right: 6px;
      font-size: 20px;
      color: #07c160;
    }
  }
}
</style>
