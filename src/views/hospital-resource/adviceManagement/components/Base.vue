<template>
  <el-form
    ref="formRef"
    :model="formData"
    label-position="right"
    scroll-to-first-error
    @submit.prevent="handleFormSubmit"
  >
    <el-row :gutter="16">
      <el-col :span="8">
        <el-form-item
          prop="MoItemUseScope"
          label="使用范围"
          :rules="[{ required: true, message: '请选择使用范围' }]"
        >
          <el-select
            v-model="formData.MoItemUseScope"
            placeholder="请选择使用范围"
            :disabled="true"
            @change="handleUseScopeChange"
          >
            <el-option :value="AdviceMoItemUseScope.Home" label="居家" />
            <el-option :value="AdviceMoItemUseScope.Community" label="线下" />
            <el-option :value="AdviceMoItemUseScope.Hospital" label="院内" />
          </el-select>
        </el-form-item>
      </el-col>
      <el-col :span="8">
        <el-form-item
          prop="Name"
          label="医嘱名称"
          :rules="[{ required: true, message: '请输入医嘱名称' }]"
        >
          <el-input v-model="formData.Name" placeholder="请输入医嘱名称" :disabled="true" />
        </el-form-item>
      </el-col>
      <el-col :span="8" @blur="handleNameBlur">
        <el-form-item prop="AliasName" label="医嘱简称">
          <el-input v-model="formData.AliasName" placeholder="请输入医嘱简称" :disabled="true" />
        </el-form-item>
      </el-col>
    </el-row>

    <el-row :gutter="16">
      <el-col :span="8">
        <el-form-item
          prop="PYM"
          label="拼音码"
          :rules="[{ required: true, message: '请输入拼音码' }]"
        >
          <el-input v-model="formData.PYM" placeholder="请输入拼音码" :disabled="true" />
        </el-form-item>
      </el-col>
      <el-col :span="8">
        <el-form-item prop="Code" label="编码" :rules="[{ required: true, message: '请输入编码' }]">
          <el-input v-model="formData.Code" placeholder="请输入编码" :disabled="true" />
        </el-form-item>
      </el-col>
      <el-col :span="8">
        <el-form-item
          prop="MoType"
          label="医嘱类别"
          :rules="[{ required: true, message: '请选择医嘱类别' }]"
        >
          <el-select v-model="formData.MoType" placeholder="请选择医嘱类别" :disabled="true">
            <el-option
              v-for="item in moItemTypeList"
              :key="item.Value"
              :value="item.Value"
              :label="item.Name"
            />
          </el-select>
        </el-form-item>
      </el-col>
    </el-row>

    <el-row :gutter="16">
      <el-col :span="8">
        <el-form-item prop="Manufacturer" label="厂商">
          <el-select
            v-model="formData.Manufacturer"
            placeholder="请选择厂商"
            :disabled="true"
            clearable
          >
            <el-option :value="1" label="易脑复苏VR" />
            <el-option :value="2" label="赛客呼吸康复" />
          </el-select>
        </el-form-item>
      </el-col>
      <el-col :span="8">
        <el-form-item
          prop="MoItemMethod"
          label="下达方式"
          :rules="[{ required: true, message: '请选择下达方式' }]"
        >
          <el-select
            v-model="formData.MoItemMethod"
            placeholder="请选择下达方式"
            :disabled="true"
            @change="handleMoItemMethodChange"
          >
            <el-option :value="AdviceMoItemMethod.General" label="普通医嘱" />
            <el-option :value="AdviceMoItemMethod.Acupoint" label="穴位" />
            <el-option :value="AdviceMoItemMethod.Consultation" label="咨询" />
            <el-option :value="AdviceMoItemMethod.Evaluation" label="评定" />
            <el-option :value="AdviceMoItemMethod.Equipment" label="辅具" />
            <el-option :value="AdviceMoItemMethod.Consumables" label="耗材" />
            <el-option :value="AdviceMoItemMethod.Education" label="宣教" />
          </el-select>
        </el-form-item>
      </el-col>
      <el-col :span="8">
        <el-form-item
          prop="ChargeMode"
          label="计费方式"
          :rules="[{ required: true, message: '请选择计费方式' }]"
        >
          <el-select v-model="formData.ChargeMode" placeholder="请选择计费方式" :disabled="true">
            <el-option
              v-for="item in chargeMode"
              :key="item.value"
              :value="item.value"
              :label="item.label"
              :disabled="item.disabled"
            />
          </el-select>
        </el-form-item>
      </el-col>
    </el-row>

    <el-row :gutter="16">
      <el-col :span="4">
        <el-form-item prop="IsUseConsumable" label="使用耗材">
          <el-switch
            v-model="formData.IsUseConsumable"
            :disabled="true"
            @change="handleUseConsumableChange"
          />
        </el-form-item>
      </el-col>
      <el-col v-if="isUseConsumable" :span="8">
        <el-form-item
          prop="Consumables"
          label="关联耗材"
          :rules="[{ required: isConsumableRequired, message: '请选择关联耗材' }]"
        >
          <el-select
            v-model="formData.Consumables"
            :disabled="true"
            placeholder="请选择关联耗材"
            multiple
            filterable
            :multiple-limit="1"
          >
            <el-option
              v-for="item in consumableList"
              :key="item.Id"
              :value="item.Id"
              :label="item.Name"
            />
          </el-select>
        </el-form-item>
      </el-col>
      <el-col :span="6">
        <el-form-item prop="IsEnable" label="是否启用">
          <el-switch v-model="formData.IsEnable" :disabled="false" />
        </el-form-item>
      </el-col>
    </el-row>

    <el-row>
      <el-col :span="24">
        <el-form-item prop="Catelog" label="康复分类">
          <el-select
            v-model="formData.Catelog"
            placeholder="请选择康复分类"
            multiple
            :disabled="true"
          >
            <el-option
              v-for="item in recoveryTypeList"
              :key="item.Key"
              :value="item.Key!"
              :label="item.Key"
            />
          </el-select>
        </el-form-item>
      </el-col>
    </el-row>

    <el-row>
      <el-col :span="24">
        <el-form-item prop="Tags" label="分组">
          <el-select
            v-model="formData.Tags"
            placeholder="请选择分组"
            multiple
            allow-create
            filterable
            default-first-option
            :reserve-keyword="false"
            :disabled="true"
          >
            <el-option v-for="item in moItemGroupList" :key="item" :value="item" :label="item" />
          </el-select>
        </el-form-item>
      </el-col>
    </el-row>

    <FormItemContainer>
      <el-form-item
        prop="MinAmount"
        label="金额范围"
        :rules="[
          { required: true, message: '请输入金额范围' },
          { validator: validateAmountRange, trigger: 'change' },
        ]"
      >
        <div class="flex items-center">
          <el-input-number
            v-model="formData.MinAmount"
            :disabled="true"
            style="width: 120px"
            :min="0"
            :precision="2"
            placeholder="最小值（元）"
          />
          <span class="mx-2">-</span>
          <el-input-number
            v-model="formData.MaxAmount"
            :disabled="true"
            style="width: 120px"
            :min="0"
            :precision="2"
            placeholder="最大值（元）"
          />
        </div>
      </el-form-item>
      <el-form-item prop="ShowAmount" label="指导价">
        <el-input-number
          v-model="formData.ShowAmount"
          :min="0"
          :precision="2"
          placeholder="指导价"
        />
      </el-form-item>
      <el-form-item v-if="isShowLogistics" prop="LogisticsDay" label="物流天数">
        <el-input-number
          v-model="formData.LogisticsDay"
          :min="0"
          :precision="0"
          placeholder="物流天数"
          :disabled="isPreview"
        />
      </el-form-item>
    </FormItemContainer>

    <FormItemContainer>
      <el-form-item
        prop="MinDay"
        label="医嘱天数范围"
        :rules="[
          { required: true, message: '请输入金额范围' },
          { validator: validateDayRange, trigger: 'change' },
        ]"
      >
        <div class="flex items-center">
          <el-input-number
            v-model="formData.MinDay"
            style="width: 120px"
            :min="1"
            :precision="0"
            placeholder="最小值（天）"
            :disabled="isPreview"
          />
          <span class="mx-2">-</span>
          <el-input-number
            v-model="formData.MaxDay"
            style="width: 120px"
            :min="formData.MinDay"
            :precision="0"
            placeholder="最大值（天）"
            :disabled="isPreview"
          />
        </div>
      </el-form-item>
      <el-form-item
        prop="DefaultMoDay"
        label="默认天数"
        :rules="[{ required: true, message: '请输入默认天数' }, { validator: validateDefaultDay }]"
      >
        <el-input-number
          v-model="formData.DefaultMoDay"
          :min="1"
          :precision="0"
          placeholder="默认天数（天）"
          :disabled="isPreview"
        />
      </el-form-item>
    </FormItemContainer>

    <el-row>
      <el-col :span="24">
        <el-form-item
          prop="Visibility"
          label="下达权限"
          :rules="[{ required: true, message: '请选择下达权限' }]"
        >
          <el-checkbox-group
            v-model="formData.Visibility"
            :disabled="isPreview"
            @change="handleVisibilityChange"
          >
            <el-checkbox
              v-for="item in visibilityOptions"
              :key="item.value"
              :value="item.value"
              :label="item.label"
              :disabled="item.disabled"
            >
              {{ item.label }}
            </el-checkbox>
          </el-checkbox-group>
        </el-form-item>
      </el-col>
    </el-row>

    <el-row :gutter="16">
      <el-col :span="12">
        <el-form-item
          prop="Scene"
          label="下达场景"
          :rules="[{ required: true, message: '请选择下达场景' }]"
        >
          <el-checkbox-group
            v-model="formData.Scene"
            :disabled="isPreview"
            @change="handleSceneChange"
          >
            <el-checkbox v-for="item in sceneOptions" :key="item.value" :value="item.value">
              {{ item.label }}
            </el-checkbox>
          </el-checkbox-group>
        </el-form-item>
      </el-col>
      <el-col v-if="isShowHaveConsultServe" :span="12">
        <el-form-item prop="HaveConsultServe" label="是否含咨询指导服务" label-width="200">
          <el-switch v-model="formData.HaveConsultServe" />
        </el-form-item>
      </el-col>
    </el-row>
    <el-row>
      <el-col :span="24">
        <el-form-item prop="DeptIds" label="适用科室">
          <el-select
            v-model="formData.DeptIds"
            placeholder="请选择适用科室"
            multiple
            :disabled="isPreview"
          >
            <el-option
              v-for="item in deptList"
              :key="item.Id"
              :value="item.Id!"
              :label="item.Name"
            />
          </el-select>
        </el-form-item>
      </el-col>
    </el-row>
    <el-row>
      <el-col :span="24">
        <el-form-item prop="Explain" label="医嘱说明">
          <el-input
            v-model="formData.Explain"
            type="textarea"
            :rows="4"
            placeholder="请输入医嘱说明"
            :disabled="isPreview"
          />
        </el-form-item>
      </el-col>
    </el-row>

    <el-row>
      <el-col :span="24">
        <el-form-item prop="MoItemVideoUrl" label="操作视频" :label-col="{ span: 4 }">
          <SingleFileUpload
            v-model="formData.MoItemVideoUrl"
            accept="video/*"
            :disabled="isPreview"
          />
        </el-form-item>
      </el-col>
    </el-row>

    <el-row>
      <el-col :span="24">
        <el-form-item prop="Media" label="服务内容说明" :label-col="{ span: 4 }">
          <MultiImageUpload v-model="formData.Media" accept="image/*" :disabled="isPreview" />
        </el-form-item>
      </el-col>
    </el-row>

    <el-row>
      <el-col :span="24">
        <el-form-item prop="Remark" label="治疗须知" :label-col="{ span: 4 }">
          <WangEditor v-model="formData.Remark" :disabled="isPreview" />
        </el-form-item>
      </el-col>
    </el-row>
  </el-form>
</template>

<script setup lang="ts">
import type { CheckboxValueType, FormInstance } from "element-plus";
import { AdviceMoItemUseScope, AdviceMoItemMethod } from "@/enums/AdviceEnum";
import Content_Api from "@/api/content";
import { PageInsertOrUpdateMoItemParams } from "../index.vue";
import { chineseToPinyin } from "@/utils/pinyin-helper";
import { getDeptList } from "@/utils/dict";
const emit = defineEmits(["getIsShowTreatmentPoint"]);

const formRef = ref<FormInstance>();
const isUseConsumable = ref(false);
const isConsumableRequired = ref(false);
const isShowLogistics = ref(true);
const isShowHaveConsultServe = ref(false);
const isShowTreatmentPoint = ref(false);

// 表单数据
let formData = reactive<PageInsertOrUpdateMoItemParams>({
  MoItemUseScope: 1,
  Name: "",
  AliasName: "",
  PYM: "",
  Code: "",
  MoType: undefined,
  Manufacturer: undefined,
  MoItemMethod: 0,
  ChargeMode: undefined,
  IsUseConsumable: false,
  Consumables: [],
  IsEnable: true,
  IsDefaultPush: false,
  Catelog: [],
  Tags: [],
  MinAmount: null,
  MaxAmount: null,
  ShowAmount: null,
  LogisticsDay: 0,
  MinDay: 1,
  MaxDay: 30,
  DefaultMoDay: 15,
  Visibility: ["1"],
  Scene: ["1"],
  HaveConsultServe: false,
  Explain: "",
  MoItemVideoUrl: "",
  Media: [],
  Remark: "",
  IsSpecialFreq: false,
  DeptIds: [],
  OrganizationId: "",
});
const moItemTypeList = inject("moItemTypeList") as Ref<MoItemTypeList[]>;
const deptList = ref<BaseDepartment[]>([]);
// 选项数据
let chargeMode = ref([
  { label: "按部位", value: 1, disabled: true },
  { label: "按次", value: 2, disabled: false },
  { label: "按天", value: 3, disabled: false },
  { label: "按项目", value: 4, disabled: false },
  { label: "按月", value: 5, disabled: true },
]);
const consumableList = ref<BaseConsumables[]>([]);
const recoveryTypeList = inject("recoveryList") as Ref<ReadDict[]>;
const moItemGroupList = inject("moItemGroupList") as Ref<string[]>;
const isPreview = inject("isPreview") as Ref<boolean>;
let visibilityOptions = ref([
  { label: "医生", value: "1", disabled: false },
  { label: "治疗师", value: "2", disabled: false },
  { label: "治疗师（需医生确认）", value: "4", disabled: false },
  { label: "护士", value: "8", disabled: false },
  { label: "护士（需医生确认）", value: "16", disabled: false },
]);
const sceneOptions = ref([
  { label: "问诊/咨询", value: "1" },
  { label: "患者管理", value: "2" },
]);

const validateDefaultDay = (rule: any, value: any, callback: any) => {
  if (value && (value < formData.MinDay || value > formData.MaxDay)) {
    callback(new Error("默认天数必须在最小天数和最大天数之间"));
  } else {
    callback();
  }
};

const validateAmountRange = (rule: any, value: any, callback: any) => {
  const { MinAmount, MaxAmount } = formData;
  if (!MinAmount && MinAmount != 0) {
    callback(new Error("请输入最小值"));
  } else if (!MaxAmount && MaxAmount != 0) {
    callback(new Error("请输入最大值"));
  } else if (Number(MinAmount) > Number(MaxAmount)) {
    callback(new Error("最大值必须大于最小值"));
  } else {
    callback();
  }
};
const validateDayRange = (rule: any, value: any, callback: any) => {
  const { MinDay, MaxDay } = formData;
  if (!MinDay && MinDay != 0) {
    callback(new Error("请输入最小值"));
  } else if (!MaxDay && MaxDay != 0) {
    callback(new Error("请输入最大值"));
  } else if (Number(MinDay) > Number(MaxDay)) {
    callback(new Error("最大值必须大于最小值"));
  } else {
    callback();
  }
};
// 事件处理函数
const handleUseScopeChange = (value: number) => {
  formData.MoItemMethod = 0;
  switch (value) {
    case 1:
      formData.IsSpecialFreq = false;
      isShowLogistics.value = true;
      isShowTreatmentPoint.value = false;
      if (formData.Scene.includes("2")) {
        isShowHaveConsultServe.value = true;
      }
      break;
    case 2:
      formData.IsSpecialFreq = true;
      formData.LogisticsDay = 0;
      isShowLogistics.value = false;
      isShowTreatmentPoint.value = true;
      isShowHaveConsultServe.value = false;
      break;
    case 3:
      formData.IsSpecialFreq = false;
      formData.Scene = ["1"];
      isShowTreatmentPoint.value = true;
      isShowLogistics.value = true;
      break;
    default:
      break;
  }
  emit("getIsShowTreatmentPoint", isShowTreatmentPoint.value);
};

const handleMoItemMethodChange = (value: number) => {
  switch (value) {
    case 0: // 按部位和按月不可选
      chargeMode.value = [
        { label: "按部位", value: 1, disabled: true },
        { label: "按次", value: 2, disabled: false },
        { label: "按天", value: 3, disabled: false },
        { label: "按项目", value: 4, disabled: false },
        { label: "按月", value: 5, disabled: true },
      ];
      formData.IsSpecialFreq = false;
      break;
    case 1: // 按穴位
      chargeMode.value = [
        { label: "按部位", value: 1, disabled: false },
        { label: "按次", value: 2, disabled: false },
        { label: "按天", value: 3, disabled: false },
        { label: "按项目", value: 4, disabled: false },
        { label: "按月", value: 5, disabled: true },
      ];
      formData.IsSpecialFreq = true;
      break;
    case 2: // 按咨询
      chargeMode.value = [
        { label: "按部位", value: 1, disabled: true },
        { label: "按次", value: 2, disabled: false },
        { label: "按天", value: 3, disabled: false },
        { label: "按项目", value: 4, disabled: false },
        { label: "按月", value: 5, disabled: true },
      ];
      formData.IsSpecialFreq = false;
      break;
    case 3: // 按评定
      chargeMode.value = [
        { label: "按部位", value: 1, disabled: true },
        { label: "按次", value: 2, disabled: false },
        { label: "按天", value: 3, disabled: false },
        { label: "按项目", value: 4, disabled: false },
        { label: "按月", value: 5, disabled: true },
      ];
      formData.IsSpecialFreq = true;
      break;
    case 4: // 按辅具
      chargeMode.value = [
        { label: "按部位", value: 1, disabled: true },
        { label: "按次", value: 2, disabled: false },
        { label: "按天", value: 3, disabled: false },
        { label: "按项目", value: 4, disabled: false },
        { label: "按月", value: 5, disabled: true },
      ];
      formData.IsSpecialFreq = false;
      break;
    case 5: // 按耗材
      chargeMode.value = [
        { label: "按部位", value: 1, disabled: true },
        { label: "按次", value: 2, disabled: false },
        { label: "按天", value: 3, disabled: true },
        { label: "按项目", value: 4, disabled: true },
        { label: "按月", value: 5, disabled: true },
      ];
      formData.IsSpecialFreq = true;
      break;
    case 6: // 按宣教
      chargeMode.value = [
        { label: "按部位", value: 1, disabled: true },
        { label: "按次", value: 2, disabled: true },
        { label: "按天", value: 3, disabled: true },
        { label: "按项目", value: 4, disabled: false },
        { label: "按月", value: 5, disabled: true },
      ];
      formData.IsSpecialFreq = false;
      break;
    default:
      break;
  }
};

const handleUseConsumableChange = async (value: string | number | boolean) => {
  if (value) {
    // 如果有耗材数据就不发送请求获取 没有才发送请求
    if (!consumableList.value || !consumableList.value.length) {
      const res = await Content_Api.getConsumablesPageData({
        page: 1,
        pageSize: 1000,
        isEnable: true,
        keywords: "",
      });
      if (res.Type === 200) {
        consumableList.value = res.Data.Data;
      }
    }
    isConsumableRequired.value = true;
  } else {
    formData.Consumables = [];
    isConsumableRequired.value = false;
  }
  isUseConsumable.value = Boolean(value);
};

const handleVisibilityChange = (value: CheckboxValueType[]) => {
  const visibilityMapping = {
    "2": 2,
    "4": 1,
    "8": 4,
    "16": 3,
  };
  const newVisibilityOptions = [...visibilityOptions.value]; // 创建数组副本
  Object.entries(visibilityMapping).forEach(([key, index]) => {
    if (newVisibilityOptions[index]) {
      newVisibilityOptions[index].disabled = value.includes(key);
    }
  });
  visibilityOptions.value = newVisibilityOptions;
};

const handleSceneChange = (value: CheckboxValueType[]) => {
  if (value.includes("2") && formData.MoItemUseScope !== 2) {
    isShowHaveConsultServe.value = true;
    formData.HaveConsultServe = true;
  } else {
    isShowHaveConsultServe.value = false;
    formData.HaveConsultServe = false;
  }
};

const handleFormSubmit = async (): Promise<any> => {
  if (!formRef.value) return;

  try {
    await formRef.value.validate();
    // 表单验证通过，进行提交操作
    return formData;
  } catch (error) {
    console.error("表单验证失败:", error);
    return null;
  }
};
const handleNameBlur = () => {
  console.log("handleNameBlur");
  formData.PYM = chineseToPinyin(formData.Name);
};
const handleInitFormData = (newVal: PageInsertOrUpdateMoItemParams) => {
  formData = newVal;
  handlePageShowItemData(newVal);
  // 获取机构的科室信息
  onGetDeptListData(newVal.OrganizationId);
};
const onGetDeptListData = async (orgId?: string) => {
  if (!orgId) return;
  deptList.value = await getDeptList({
    OrgId: orgId,
  });
};
const handleVisibilityOptions = (safeFormItem: PageInsertOrUpdateMoItemParams) => {
  const newVisibilityOptions = [...visibilityOptions.value];
  if (safeFormItem.Visibility.includes("2")) {
    newVisibilityOptions[2].disabled = true;
  }
  if (safeFormItem.Visibility.includes("4")) {
    newVisibilityOptions[1].disabled = true;
  }
  if (safeFormItem.Visibility.includes("8")) {
    newVisibilityOptions[4].disabled = true;
  }
  if (safeFormItem.Visibility.includes("16")) {
    newVisibilityOptions[3].disabled = true;
  }
  visibilityOptions.value = newVisibilityOptions;
};

const handlePageShowItemData = async (safeFormItem: PageInsertOrUpdateMoItemParams) => {
  // 处理 "下达权限" 那些需要禁用
  handleVisibilityOptions(safeFormItem);

  // 处理是否显示 "是否含咨询指导服务"
  if (safeFormItem.Scene.includes("2")) {
    isShowHaveConsultServe.value = true;
  } else {
    isShowHaveConsultServe.value = false;
  }
  // 处理是否显示物流天数
  if (safeFormItem.MoItemUseScope !== 2) {
    isShowLogistics.value = true;
  } else {
    isShowLogistics.value = false;
  }
  // 设置下达方式那些能够选择
  handleMoItemMethodChange(safeFormItem.MoItemMethod);
  // 是否显示使用耗材
  if (safeFormItem.IsUseConsumable) {
    isUseConsumable.value = true;
    if (!consumableList.value || !consumableList.value.length) {
      const res = await Content_Api.getConsumablesPageData({
        page: 1,
        pageSize: 1000,
        isEnable: true,
        keywords: "",
      });
      if (res.Type === 200) {
        consumableList.value = res.Data.Data;
      }
    }
  }
  formData = safeFormItem;
};

interface Props {
  formItem: PageInsertOrUpdateMoItemParams | null;
}
const props = defineProps<Props>();
watch(
  () => props.formItem,
  (newVal) => {
    if (newVal) {
      handleInitFormData(newVal);
    }
  },
  {
    immediate: true,
  }
);
defineExpose({
  handleFormSubmit,
});
</script>

<style lang="scss" scoped></style>
