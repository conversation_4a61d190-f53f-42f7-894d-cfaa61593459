<template>
  <div class="container">
    <el-form
      ref="formRef"
      :disabled="isOnlyPreview"
      :model="formData"
      label-width="100px"
      :rules="rules"
    >
      <FormItemContainer>
        <el-form-item label="医嘱天数" prop="MoDay">
          <el-input-number v-model="formData.MoDay" disabled style="width: 120px" />
        </el-form-item>
        <el-form-item label="频次" prop="FreqDay">
          <div class="flex items-center">
            <el-input-number v-model="formData.FreqDay" :min="1" :max="7" class="flex-1" />
            <span class="ml-2">天</span>
            <el-input-number v-model="formData.Freq" :min="1" :max="7" class="flex-1" />
            <span class="ml-2">次</span>
          </div>
        </el-form-item>
        <el-form-item label="总次数" prop="TotalCount">
          <el-input-number v-model="formData.TotalCount" :min="1" style="width: 120px" />
        </el-form-item>
      </FormItemContainer>

      <el-form-item label="耗材" prop="Consumables">
        <el-select
          v-model="formData.Consumables"
          multiple
          :multiple-limit="1"
          placeholder="请选择耗材"
          style="width: 150px"
        >
          <el-option
            v-for="item in consumableList"
            :key="item.Id"
            :label="item.Spec"
            :value="item.Id"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="穴位模版">
        <div class="flex items-center mb-2">
          <el-select
            v-model="acuTemplatePointId"
            placeholder="请选择穴位"
            filterable
            style="width: 250px"
          >
            <el-option
              v-for="item in templateList"
              :key="item.Id"
              :label="item.Name"
              :value="item.Id ?? ''"
            />
          </el-select>
          <el-button type="primary" class="ml-10px" @click="handleAddTemplateAcupoint">
            添加所有穴位
          </el-button>
        </div>
      </el-form-item>
      <el-form-item label="穴位" prop="DisTags">
        <el-tag
          v-for="item in formData.PackAcupoints"
          :key="item.AcuPointId"
          closable
          :disable-transitions="false"
          style="margin-right: 10px"
          @close="handleDeleteAcupoint(item)"
        >
          {{ item.Name }}
        </el-tag>
        <el-select-v2
          v-model="acuPointId"
          placeholder="请选择穴位"
          filterable
          style="width: 150px"
          :options="acuPointList"
          :props="{ label: 'Name', value: 'Id' }"
          @change="handleAcupointSelectChange"
        />
      </el-form-item>
    </el-form>
    <el-table :data="formData.PackAcupoints" border style="width: 100%" align="center">
      <el-table-column label="穴位名称" prop="Name" align="center" />
      <el-table-column label="穴位数量" align="center">
        <template #default="{ row }">
          <el-input-number
            v-model="row.AcuPointCount"
            :disabled="isOnlyPreview"
            :min="1"
            :max="99"
            style="width: 150px"
          />
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script setup lang="ts">
import type { FormInstance, FormRules } from "element-plus";
import Content_Api from "@/api/content";
import { MoItemGroupItem } from "@/api/content/types";

const formRef = ref<FormInstance>();
const templateList = ref<BaseAcuPointTemplate[]>([]);
const acuPointList = ref<BaseAcuPoint[]>([]);
const acuTemplatePointId = ref<string>("");
const acuPointId = ref<string>("");
const consumableList = ref<BaseConsumables[]>([]);
const isOnlyPreview = inject("isOnlyPreview") as Ref<boolean>;
let formData = reactive<MoItemGroupItem>({
  MoDay: 1,
  FreqDay: 1,
  Freq: 1,
  TotalCount: 1,
  Consumables: [],
  PackAcupoints: [],
  MoItemId: "",
  MoName: "",
  MoRemark: "",
  Price: 0,
  IsSpecialFreq: false,
  MoItemMethod: 0,
  MoItemUseScope: 0,
  LogisticsDay: 0,
});

const rules: FormRules = {
  MoDay: [
    { required: true, message: "请输入医嘱天数", trigger: "blur" },
    { type: "number", min: 1, message: "天数必须大于0", trigger: "blur" },
  ],
  FreqDay: [
    { required: true, message: "请输入频次", trigger: "blur" },
    { type: "number", min: 1, max: 7, message: "频次的天数必须在1-7之间", trigger: "blur" },
  ],
  TotalCount: [
    { required: true, message: "请输入总次数", trigger: "blur" },
    { type: "number", min: 1, max: 99, message: "总次数必须在1-99之间", trigger: "blur" },
  ],
};

// 监听TotalCount、Freq和FreqDay的变化，计算并更新MoDay
watch(
  () => [formData.TotalCount, formData.Freq, formData.FreqDay],
  () => {
    console.log(formData.TotalCount, formData.Freq, formData.FreqDay);
    formData.MoDay = Math.ceil(formData.TotalCount / formData.Freq) * formData.FreqDay;
  }
);

const handleAddAcupoint = (item: AcuPointInfo) => {
  const isExist = formData.PackAcupoints!.some(
    (acuPoint) => acuPoint.AcuPointId === item.AcuPointId
  );
  if (isExist) {
    return;
  }
  formData.PackAcupoints!.push(item);
};
const handleAcupointSelectChange = (value: string) => {
  const acuPoint: BaseAcuPoint = acuPointList.value.find((item) => item.Id === value)!;
  if (acuPoint) {
    const acuPointInfo: AcuPointInfo = {
      AcuPointId: acuPoint.Id,
      Name: acuPoint.Name ?? "",
      AcuPointCount: acuPoint.AcuPointCount ?? 1,
      IsDouble: acuPoint.IsDouble ?? false,
    };
    handleAddAcupoint(acuPointInfo);
    acuPointId.value = "";
  }
};
// 添加模板穴位
const handleAddTemplateAcupoint = () => {
  // 将选择的穴位模板中的AcuPointInfos加入到PackAcupoints中 如果有重复的泽不添加
  const template = templateList.value.find((item) => item.Id === acuTemplatePointId.value);
  if (template) {
    const acuPointInfos = template.AcuPointInfos;
    // 如果PackAcupoints中有相同的穴位 这不添加AcuPointId 没有的则加入到PackAcupoints中
    acuPointInfos?.forEach((item) => {
      handleAddAcupoint(item);
    });
  }
};
// 删除穴位
const handleDeleteAcupoint = (item: AcuPointInfo) => {
  formData.PackAcupoints!.splice(formData.PackAcupoints!.indexOf(item), 1);
};
const handleGetAcupointTemplate = async () => {
  try {
    const res = await Content_Api.appGetAcuPointTemplates({ moItem: props.moItemId });
    if (res.Type === 200) {
      templateList.value = res.Data;
    }
  } catch (error) {
    console.error(error);
    templateList.value = [];
  }
};
const handleGetAcupoint = async () => {
  try {
    const res = await Content_Api.getAcuPointPageData({ isEnable: true, page: 1, pageSize: 1000 });
    if (res.Type === 200) {
      acuPointList.value = res.Data.Data;
    }
  } catch (error) {
    console.error(error);
    acuPointList.value = [];
  }
};
const handleCheckAcupoint = (): boolean => {
  if (!formData.PackAcupoints || !formData.PackAcupoints.length) {
    ElMessage.warning("请选择穴位");
    return false;
  }
  return true;
};
const handleProcessingData = (): MoItemGroupItem => {
  let partCount = 0;
  formData.PackAcupoints!.forEach((e) => {
    partCount += e.AcuPointCount;
  });
  const finishData: MoItemGroupItem = {
    MoItemId: formData.MoItemId,
    MoName: formData.MoName,
    MoRemark: formData.MoRemark,
    MoDay: formData.MoDay,
    Part: partCount,
    Freq: formData.Freq,
    TotalCount: formData.TotalCount,
    Price: formData.Price,
    FreqDay: formData.FreqDay,
    IsSpecialFreq: formData.IsSpecialFreq,
    MoItemMethod: formData.MoItemMethod,
    MoItemUseScope: formData.MoItemUseScope,
    LogisticsDay: formData.LogisticsDay,
    PackAcupoints: formData.PackAcupoints,
    PackActions: [],
    PackScales: [],
    ChargeMode: formData.ChargeMode,
    ChargeItem: formData.ChargeItem,
    Consumables: formData.Consumables,
  };
  return finishData;
};
const handleSubmit = async (): Promise<MoItemGroupItem | null> => {
  if (!formRef.value) return null;
  try {
    await formRef.value.validate();
    if (formData.Freq > 7 || formData.Freq < 1) {
      ElMessage.warning("频次的次数必须在1-7之间");
      return null;
    }
    const isFinish = handleCheckAcupoint();
    if (!isFinish) {
      return null;
    }
    // 表单验证通过，进行提交操作
    const data = handleProcessingData();
    return data;
  } catch (error) {
    console.error("表单验证失败:", error);
    return null;
  }
};
const handleInitPageData = async () => {
  // 获取医嘱详情，添加的时候把默认的耗材选择上 修改的时候默认选择上之前选择的
  const res = await Content_Api.getMoItemByIds({
    Ids: [props.moItemId],
  });
  if (res.Type === 200) {
    if (res.Data[0].ConsumablesOutputDtos && res.Data[0].ConsumablesOutputDtos.length) {
      await handleGetConsumablesListData(res.Data[0].ConsumablesOutputDtos[0].Type);
      formData.Consumables = res.Data[0].Consumables;
    }
  } else {
    consumableList.value = [];
  }
};

const handleGetConsumablesListData = async (type: string) => {
  const res = await Content_Api.getConsumablesPageData({
    type,
    page: 1,
    pageSize: 1000,
    keywords: "",
    isEnable: true,
  });
  if (res.Type === 200) {
    consumableList.value = res.Data.Data;
  }
};

defineExpose({
  handleSubmit,
});
interface Props {
  moItemId: string;
  info: MoItemGroupItem | null;
}
const props = defineProps<Props>();
watch(
  () => props.info,
  async (newVal) => {
    if (newVal) {
      await handleInitPageData();
      // 使用 Object.assign 更新属性，保持响应式
      Object.assign(formData, newVal);
    }
  },
  {
    immediate: true,
  }
);
onMounted(() => {
  handleGetAcupointTemplate();
  handleGetAcupoint();
});
</script>

<style scoped lang="scss">
.container {
  width: 100%;
  height: 400px;
  padding: 20px;
  overflow-y: auto;

  :deep(.el-input-number) {
    width: 100%;
  }

  .flex {
    display: flex;
  }

  .items-center {
    align-items: center;
  }

  .w-full {
    width: 100%;
  }

  .mr-2 {
    margin-right: 8px;
  }

  .mb-2 {
    margin-bottom: 8px;
  }

  .ml-2 {
    margin-left: 8px;
  }

  .flex-1 {
    flex: 1;
  }
}
</style>
