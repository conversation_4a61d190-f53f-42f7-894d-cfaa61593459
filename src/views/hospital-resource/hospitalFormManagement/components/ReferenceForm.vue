<template>
  <el-container class="w-full overflow-y-auto h-600px">
    <!-- 左侧目录 -->
    <el-aside width="230px" class="p-10px">
      <el-card class="h-full">
        <el-tree
          class="w-full h-full"
          :data="treeData"
          :props="defaultTreeProps"
          node-key="value"
          :current-node-key="queryParams.Type"
          highlight-current
          default-expand-all
          @node-click="onTreeClick"
        />
      </el-card>
    </el-aside>
    <el-main class="p-10px!">
      <BaseTableSearchContainer @size-changed="tableResize">
        <template #search>
          <!-- 顶部筛选条件 -->
          <TBSearchContainer is-show-toggle>
            <template #left>
              <el-form label-position="right" :model="queryParams" :inline="true">
                <el-form-item label="适用病种" prop="DiseaId">
                  <KSelect
                    v-model="queryParams.DiseaId"
                    :data="props.diseases"
                    :props="{ label: 'Key', value: 'Id' }"
                    :show-all="true"
                  />
                </el-form-item>
                <el-form-item label="康复分类" prop="DictType">
                  <KSelect
                    v-model="queryParams.DictType"
                    :data="props.recoveryTypes"
                    :props="{ label: 'Key', value: 'Key' }"
                    :show-all="true"
                  />
                </el-form-item>
                <el-form-item label="关键字" prop="Keyword">
                  <el-input
                    v-model="queryParams.Keyword"
                    clearable
                    placeholder="输入关键字筛选"
                    @keyup.enter="onSearch"
                  />
                </el-form-item>
              </el-form>
            </template>
            <template #right>
              <el-button type="primary" icon="search" @click="onSearch">搜索</el-button>
            </template>
          </TBSearchContainer>
        </template>
        <!-- 量表列表 -->
        <template #table>
          <el-table
            v-loading="tableLoading"
            :data="pageData"
            highlight-current-row
            border
            :header-cell-style="{ textAlign: 'center' }"
            :cell-style="{ textAlign: 'center' }"
            :height="tableFluidHeight"
            @selection-change="(selection) => handleSelectionChange(selection, 'Id')"
          >
            <el-table-column type="selection" width="55" />
            <el-table-column label="操作" width="120">
              <template #default="scope">
                <el-button link type="primary" @click="onGaugeDetail(scope.row)">
                  量表预览
                </el-button>
              </template>
            </el-table-column>
            <el-table-column prop="Types" label="康复分类" />
            <el-table-column prop="Name" label="量表名称" />
            <el-table-column prop="Remark" label="量表说明/介绍" show-overflow-tooltip />
          </el-table>
        </template>
        <!-- 分页 -->
        <template #pagination>
          <Pagination
            v-if="total > 0"
            v-model:total="total"
            v-model:page="queryParams.PageIndex"
            v-model:limit="queryParams.PageSize"
            @pagination="requestGaugeData"
          />
        </template>
      </BaseTableSearchContainer>
    </el-main>
  </el-container>

  <!-- 添加/编辑/查看量表 -->
  <el-dialog
    v-model="showGaugeDialog.isShow"
    :title="showGaugeDialog.title"
    width="800"
    destroy-on-close
    @close="showGaugeDialog.isShow = false"
  >
    <GaugeForm
      :gauge="showGaugeDialog.gauge"
      :recovery-types="props.recoveryTypes"
      :diseases="props.diseases"
      page="hospitalForm"
      :disabled="showGaugeDialog.isDisabled"
      @cancel="showGaugeDialog.isShow = false"
    />
  </el-dialog>

  <!-- 底部按钮 -->
  <div class="flex justify-end">
    <el-button @click="$emit('cancel')">取消</el-button>
    <el-button type="primary" @click="onReference">引用</el-button>
  </div>
</template>

<script setup lang="ts">
import Training_Api from "@/api/training";
import { GaugeDetail, GetEvaluateGaugeInputDTO } from "@/api/training/types";
import { useTableConfig } from "@/hooks/useTableConfig";

const kEnableDebug = false;
defineOptions({
  name: "ReferenceForm",
});

const emit = defineEmits<{
  cancel: [];
  reference: [];
}>();

const props = defineProps<{
  /** 机构id */
  organizationId: string;

  /** 类型 */
  type?: number;

  /** 康复分类 */
  recoveryTypes: ReadDict[];

  /** 疾病列表 */
  diseases: ReadDict[];
}>();

const {
  pageData,
  tableLoading,
  tableFluidHeight,
  total,
  tableResize,
  handleSelectionChange,
  selectedTableIds,
} = useTableConfig<BaseGauge>();

/** 查询条件 */
const queryParams = reactive<GetEvaluateGaugeInputDTO>({
  PageIndex: 1,
  PageSize: 10,
  Type: props.type,
  OrganizationId: props.organizationId,
});

/** 点击搜索 */
async function onSearch() {
  kEnableDebug && console.log("点击搜索", queryParams);
  queryParams.PageIndex = 1;
  const r = await requestGaugeData();
  if (r.Type !== 200) {
    ElMessage.error(r.Content);
  }
}

/** 查看量表弹窗 */
const showGaugeDialog = reactive({
  isShow: false,
  title: "",
  isDisabled: false,
  gauge: {} as GaugeDetail,
});

/** 点击查看量表 */
async function onGaugeDetail(item: BaseGauge) {
  kEnableDebug && console.log("查看量表", item);
  if (!item.Id) {
    ElMessage.error("量表id为空");
    return;
  }

  /** 获取量表详情 */
  tableLoading.value = true;
  const r = await Training_Api.getGaugeById(item.Id);
  tableLoading.value = false;
  if (r.Type !== 200) {
    ElMessage.error(r.Content);
    return;
  }

  showGaugeDialog.gauge = r.Data;
  showGaugeDialog.title = "查看量表";
  showGaugeDialog.isDisabled = true;
  showGaugeDialog.isShow = true;
}

/** 请求量表列表 */
async function requestGaugeData() {
  tableLoading.value = true;
  const r = await Training_Api.getReferenceEvaluate(queryParams);
  tableLoading.value = false;
  if (r.Type === 200) {
    pageData.value = r.Data.Rows.map((item) => {
      const data = {
        ...item,
      };
      try {
        data.Types = JSON.parse(item.Types!).join("、");
      } catch (error) {
        data.Types = item.Types;
      }
      return data;
    });
    total.value = r.Data.Total;
  }

  return r;
}

interface Tree {
  // 展示数据
  label: string;
  // 实际网络请求需要的值
  value?: number;
  children?: Tree[];
}
/** 左侧列表数据 */
const treeData = reactive<Tree[]>([
  {
    label: "全部",
    children: [
      {
        label: "评定量表",
        value: 0,
      },
      {
        label: "随访表单",
        value: 1,
      },
    ],
  },
]);
const defaultTreeProps = reactive({
  children: "children",
  label: "label",
});

/** 树点击事件 */
async function onTreeClick(data: Tree) {
  kEnableDebug && console.debug("树点击事件", data);
  queryParams.Type = data.value;
  queryParams.PageIndex = 1;

  const r = await requestGaugeData();
  if (r.Type !== 200) {
    ElMessage.error(r.Content);
  }
}

/** 点击引用 */
async function onReference() {
  kEnableDebug && console.debug("点击引用");

  if (!selectedTableIds.value?.length) {
    ElMessage.warning("请选择需要引用的量表");
    return;
  }

  tableLoading.value = true;
  const r = await Training_Api.referenceGauge({
    Ids: selectedTableIds.value,
    OrgId: props.organizationId,
  });
  if (r.Type !== 200) {
    tableLoading.value = false;
    ElMessage.error(r.Content);
    return;
  }

  ElNotification.success("引用成功");
  emit("reference");

  const r1 = await requestGaugeData();
  if (r1.Type !== 200) {
    ElMessage.error(r1.Content);
  }
}

onMounted(async () => {
  const r = await requestGaugeData();
  if (r.Type !== 200) {
    ElMessage.error(r.Content);
  }
});
</script>

<style lang="scss" scoped></style>
