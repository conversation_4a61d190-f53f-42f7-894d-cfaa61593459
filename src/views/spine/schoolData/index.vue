<template>
  <div class="app-container">
    <BaseTableSearchContainer @size-changed="tableResize">
      <template #search>
        <TBSearchContainer :is-show-toggle="true">
          <template #left>
            <el-form :model="queryParams" label-position="right" :inline="true">
              <el-form-item label="时间">
                <el-date-picker
                  v-model="timeRange"
                  unlink-panels
                  type="daterange"
                  range-separator="至"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                  format="YYYY-MM-DD"
                  :clearable="false"
                  value-format="YYYY-MM-DD"
                  style="width: 250px"
                  :shortcuts="pageDatePickerShortcuts"
                />
              </el-form-item>
              <el-form-item label="学校">
                <SpineSchoolSelect v-model="queryParams.School" />
              </el-form-item>
              <el-form-item label="年级">
                <el-select
                  v-model="queryParams.Grade"
                  placeholder="请选择"
                  clearable
                  :empty-values="[null, undefined, '']"
                  :value-on-clear="() => null"
                >
                  <el-option label="一年级" value="一年级" />
                  <el-option label="二年级" value="二年级" />
                  <el-option label="三年级" value="三年级" />
                  <el-option label="四年级" value="四年级" />
                  <el-option label="五年级" value="五年级" />
                  <el-option label="六年级" value="六年级" />
                </el-select>
              </el-form-item>
            </el-form>
          </template>
          <template #right>
            <el-button type="primary" icon="search" @click="handleQuery">搜索</el-button>
          </template>
        </TBSearchContainer>
      </template>
      <template #table>
        <el-table
          ref="tableRef"
          v-loading="tableLoading"
          :data="pageData"
          row-key="Id"
          :height="tableFluidHeight"
          :tree-props="{ children: 'Children' }"
          class="data-table__content"
          border
        >
          <el-table-column :label="showLabelName" align="left" width="180">
            <template #default="scope">
              {{ scope.row.Name }}
            </template>
          </el-table-column>
          <el-table-column prop="UserCount" label="筛查人数" align="center" width="100" />
          <el-table-column label="脊柱侧弯人数/比例" align="center" width="120">
            <template #default="scope">
              <div>{{ scope.row.NotNormalCount + "/" + scope.row.NotNormalIkidRate + "%" }}</div>
            </template>
          </el-table-column>
          <el-table-column label="脊柱侧弯分布人数/比例" align="center">
            <template #default="scope">
              <div class="h-full">
                <ECharts
                  style="flex: 1 !important"
                  :options="handleGetScreeningChartOption(scope.row)"
                  height="100px"
                />
              </div>
            </template>
          </el-table-column>
          <el-table-column
            prop="ScreeningCount"
            label="足弓异常人数/比例"
            align="center"
            width="120"
          >
            <template #default="scope">
              <div>
                {{ scope.row.NotNormalFootCount + "/" + scope.row.NotNormalFootRate + "%" }}
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="ScreeningCount" label="左足弓异常分布人数/比例" align="center">
            <template #default="scope">
              <div class="h-full">
                <ECharts
                  style="flex: 1 !important"
                  :options="handleGetLeftFootChartOption(scope.row)"
                  height="100px"
                />
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="ScreeningCount" label="右足弓异常分布人数/比例" align="center">
            <template #default="scope">
              <div class="h-full">
                <ECharts
                  style="flex: 1 !important"
                  :options="handleGetRightFootChartOption(scope.row)"
                  height="100px"
                />
              </div>
            </template>
          </el-table-column>
        </el-table>
      </template>
    </BaseTableSearchContainer>
  </div>
</template>

<script setup lang="ts">
import dayjs from "dayjs";
import { useTableConfig } from "@/hooks/useTableConfig";
import { DetailStatisticsInputDTO } from "@/api/supplier-common/types";
import Supplier_Common_Api from "@/api/supplier-common";
import { PageDetailStatisticItem } from "./types";
import { generateUUID } from "@/utils";
const pageDatePickerShortcuts = [
  {
    text: "2025年上半年",
    value() {
      const end = "2025-06-30 23:59:59";
      const start = "2025-05-26 00:00:00";
      return [start, end];
    },
  },
];

defineOptions({
  name: "SpinalScreeningSchoolData",
});

const queryParams = ref<DetailStatisticsInputDTO>({
  StartDate: "2025-05-26 00:00:00",
  EndDate: "2025-06-30 23:59:59",
  School: null,
  Grade: null,
  Class: null,
});
const timeRange = ref<[string, string]>([
  dayjs().format("2025-05-26 00:00:00"),
  dayjs().format("2025-06-30 23:59:59"),
]);

const showLabelName = ref<string>("学校");

const { tableLoading, pageData, total, tableRef, tableFluidHeight, tableResize } =
  useTableConfig<PageDetailStatisticItem>();

const handleQuery = () => {
  showLabelName.value = handleGetLabel();
  handleGetTableList();
};
const handleGetTableList = async () => {
  tableLoading.value = true;

  try {
    const res = await Supplier_Common_Api.detailStatistics(queryParams.value);
    if (res.Type === 200) {
      let list: PageDetailStatisticItem[] = [];
      res.Data.forEach((s) => {
        Object.keys(s).forEach((key) => {
          if (key.includes("Rate") && s[key as keyof typeof s] !== "NaN") {
            (s as any)[key] = s[key as keyof typeof s]
              ? Number((s[key as keyof typeof s] as number)?.toFixed(2))
              : 0;
          }
          if (s[key as keyof typeof s] === "NaN") {
            (s as any)[key] = 0;
          }
        });
        let params: PageDetailStatisticItem = {
          Id: generateUUID(),
          Name: s.ShowName,
          UserCount: s.UserCount,
          AvgAtrMax: s.AvgAtrMax,
          NotNormalCount: s.NotNormalCount,
          NotNormalIkidRate: s.NotNormalIkidRate,
          LowUserCount: s.LowUserCount,
          LowRate: s.LowRate,
          HighUserCount: s.HighUserCount,
          HighRate: s.HighRate,
          NotNormalFootCount: s.NotNormalFootCount,
          NotNormalFootRate: s.NotNormalFootRate,
          LeftHighFootCount: s.LeftHighFootCount,
          LeftHighFootRate: s.LeftHighFootRate,
          LeftMildFootCount: s.LeftMildFootCount,
          LeftMildFootRate: s.LeftMildFootRate,
          LeftModerateFootCount: s.LeftModerateFootCount,
          LeftModerateFootRate: s.LeftModerateFootRate,
          LeftSevereFootCount: s.LeftSevereFootCount,
          LeftSevereFootRate: s.LeftSevereFootRate,
          RightHighFootCount: s.RightHighFootCount,
          RightHighFootRate: s.RightHighFootRate,
          RightMildFootCount: s.RightMildFootCount,
          RightMildFootRate: s.RightMildFootRate,
          RightModerateFootCount: s.RightModerateFootCount,
          RightModerateFootRate: s.RightModerateFootRate,
          RightSevereFootCount: s.RightSevereFootCount,
          RightSevereFootRate: s.RightSevereFootRate,
          Children: [
            {
              Id: generateUUID(),
              Name: "男",
              UserCount: s.BoyUserCount,
              AvgAtrMax: s.BoyAvgAtrMax,
              NotNormalCount: s.BoyNotNormalCount,
              NotNormalIkidRate: s.BoyNotNormalIkidRate,
              LowUserCount: s.BoyLowUserCount,
              LowRate: s.BoyLowRate,
              HighUserCount: s.BoyHighUserCount,
              HighRate: s.BoyHighRate,
              NotNormalFootCount: s.BoyNotNormalFootCount,
              NotNormalFootRate: s.BoyNotNormalFootRate,
              LeftHighFootCount: s.BoyLeftHighFootCount,
              LeftHighFootRate: s.BoyLeftHighFootRate,
              LeftMildFootCount: s.BoyLeftMildFootCount,
              LeftMildFootRate: s.BoyLeftMildFootRate,
              LeftModerateFootCount: s.BoyLeftModerateFootCount,
              LeftModerateFootRate: s.BoyLeftModerateFootRate,
              LeftSevereFootCount: s.BoyLeftSevereFootCount,
              LeftSevereFootRate: s.BoyLeftSevereFootRate,
              RightHighFootCount: s.BoyRightHighFootCount,
              RightHighFootRate: s.BoyRightHighFootRate,
              RightMildFootCount: s.BoyRightMildFootCount,
              RightMildFootRate: s.BoyRightMildFootRate,
              RightModerateFootCount: s.BoyRightModerateFootCount,
              RightModerateFootRate: s.BoyRightModerateFootRate,
              RightSevereFootCount: s.BoyRightSevereFootCount,
              RightSevereFootRate: s.BoyRightSevereFootRate,
            },
            {
              Id: generateUUID(),
              Name: "女",
              UserCount: s.GirlUserCount,
              AvgAtrMax: s.GirlAvgAtrMax,
              NotNormalCount: s.GirlNotNormalCount,
              NotNormalIkidRate: s.GirlNotNormalIkidRate,
              LowUserCount: s.GirlLowUserCount,
              LowRate: s.GirlLowRate,
              HighUserCount: s.GirlHighUserCount,
              HighRate: s.GirlHighRate,
              NotNormalFootCount: s.GirlNotNormalFootCount,
              NotNormalFootRate: s.GirlNotNormalFootRate,
              LeftHighFootCount: s.GirlLeftHighFootCount,
              LeftHighFootRate: s.GirlLeftHighFootRate,
              LeftMildFootCount: s.GirlLeftMildFootCount,
              LeftMildFootRate: s.GirlLeftMildFootRate,
              LeftModerateFootCount: s.GirlLeftModerateFootCount,
              LeftModerateFootRate: s.GirlLeftModerateFootRate,
              LeftSevereFootCount: s.GirlLeftSevereFootCount,
              LeftSevereFootRate: s.GirlLeftSevereFootRate,
              RightHighFootCount: s.GirlRightHighFootCount,
              RightHighFootRate: s.GirlRightHighFootRate,
              RightMildFootCount: s.GirlRightMildFootCount,
              RightMildFootRate: s.GirlRightMildFootRate,
              RightModerateFootCount: s.GirlRightModerateFootCount,
              RightModerateFootRate: s.GirlRightModerateFootRate,
              RightSevereFootCount: s.GirlRightSevereFootCount,
              RightSevereFootRate: s.GirlRightSevereFootRate,
            },
          ],
        };
        list.push(params);
      });
      pageData.value = list;
    }
  } catch (error) {
    console.error("加载数据失败:", error);
    pageData.value = [];
    total.value = 0;
  } finally {
    tableLoading.value = false;
  }
};

const handleGetLabel = (): string => {
  if (queryParams.value.Class) {
    return "性别";
  } else if (queryParams.value.Grade) {
    return "班级";
  } else if (queryParams.value.School) {
    return "年级";
  } else {
    return "学校";
  }
};
/** 脊柱侧弯分布人数/比例  饼图*/
const handleGetScreeningChartOption = (row: PageDetailStatisticItem) => {
  return {
    tooltip: {
      trigger: "item",
    },
    legend: {
      orient: "vertical", // 垂直排列
      left: "40%",
      top: "center", // 垂直居中
      textStyle: {
        fontSize: 14,
        color: "#333",
      },
    },
    series: [
      {
        type: "pie",
        radius: "50%",
        center: ["20%", "50%"],
        data: [
          { value: row.LowUserCount, name: `低风险${row.LowUserCount}/${row.LowRate}%` },
          { value: row.HighUserCount, name: `高风险${row.HighUserCount}/${row.HighRate}%` },
        ],
        label: {
          show: false, // 隐藏标签（name）
        },
        labelLine: {
          show: false, // 隐藏指示线
        },
      },
    ],
    color: ["#0E9CFF", "#FFAC26"],
  };
};

/** 左足弓异常分布人数/比例  饼图*/
const handleGetLeftFootChartOption = (row: PageDetailStatisticItem) => {
  return {
    tooltip: {
      trigger: "item",
    },
    legend: {
      orient: "vertical", // 垂直排列
      left: "40%",
      top: "center", // 垂直居中
      textStyle: {
        fontSize: 14,
        color: "#333",
      },
    },
    series: [
      {
        type: "pie",
        radius: "50%",
        center: ["20%", "50%"],
        data: [
          {
            value: row.LeftHighFootCount,
            name: `高足弓${row.LeftHighFootCount}/${row.LeftHighFootRate}%`,
          },
          {
            value: row.LeftMildFootCount,
            name: `轻度低足弓${row.LeftMildFootCount}/${row.LeftMildFootRate}%`,
          },
          {
            value: row.LeftModerateFootCount,
            name: `中度低足弓${row.LeftModerateFootCount}/${row.LeftModerateFootRate}%`,
          },
          {
            value: row.LeftSevereFootCount,
            name: `重度低足弓${row.LeftSevereFootCount}/${row.LeftSevereFootRate}%`,
          },
        ],
        label: {
          show: false, // 隐藏标签（name）
        },
        labelLine: {
          show: false, // 隐藏指示线
        },
      },
    ],
    color: ["#f70c00", "#0E9CFF", "#FFAC26", "#FF6B6B"],
  };
};

/** 右足弓异常分布人数/比例  饼图*/
const handleGetRightFootChartOption = (row: PageDetailStatisticItem) => {
  return {
    tooltip: {
      trigger: "item",
    },
    legend: {
      orient: "vertical", // 垂直排列
      left: "40%",
      top: "center", // 垂直居中
      textStyle: {
        fontSize: 14,
        color: "#333",
      },
    },
    series: [
      {
        type: "pie",
        radius: "50%",
        center: ["20%", "50%"],
        data: [
          {
            value: row.RightHighFootCount,
            name: `高足弓${row.RightHighFootCount}/${row.RightHighFootRate}%`,
          },
          {
            value: row.RightMildFootCount,
            name: `轻度低足弓${row.RightMildFootCount}/${row.RightMildFootRate}%`,
          },
          {
            value: row.RightModerateFootCount,
            name: `中度低足弓${row.RightModerateFootCount}/${row.RightModerateFootRate}%`,
          },
          {
            value: row.RightSevereFootCount,
            name: `重度低足弓${row.RightSevereFootCount}/${row.RightSevereFootRate}%`,
          },
        ],
        label: {
          show: false, // 隐藏标签（name）
        },
        labelLine: {
          show: false, // 隐藏指示线
        },
      },
    ],
    color: ["#f70c00", "#0E9CFF", "#FFAC26", "#FF6B6B"],
  };
};

watch(timeRange, (newVal) => {
  queryParams.value.StartDate = dayjs(newVal[0]).format("YYYY-MM-DD 00:00:00");
  queryParams.value.EndDate = dayjs(newVal[1]).format("YYYY-MM-DD 23:59:59");
});

onActivated(() => {
  handleGetTableList();
});
</script>

<style lang="scss" scoped></style>
