export interface PageDetailStatisticItem {
  Id?: string;
  Name?: string;
  UserCount?: number;
  AvgAtrMax?: number;
  NotNormalCount?: number;
  NotNormalIkidRate?: number;
  LowUserCount?: number;
  LowRate?: number;
  HighUserCount?: number;
  HighRate?: number;
  NotNormalFootCount?: number;
  NotNormalFootRate?: number;
  LeftHighFootCount?: number;
  LeftHighFootRate?: number;
  LeftMildFootCount?: number;
  LeftMildFootRate?: number;
  LeftModerateFootCount?: number;
  LeftModerateFootRate?: number;
  LeftSevereFootCount?: number;
  LeftSevereFootRate?: number;
  RightHighFootCount?: number;
  RightHighFootRate?: number;
  RightMildFootCount?: number;
  RightMildFootRate?: number;
  RightModerateFootCount?: number;
  RightModerateFootRate?: number;
  RightSevereFootCount?: number;
  RightSevereFootRate?: number;
  Children?: PageDetailStatisticItem[];
}
