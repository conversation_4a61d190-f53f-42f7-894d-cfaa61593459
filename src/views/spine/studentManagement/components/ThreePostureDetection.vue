<template>
  <div v-loading="tableLoading">
    <el-table
      ref="tableRef"
      v-loading="tableLoading"
      :data="pageData"
      border
      row-key="Id"
      :height="400"
      highlight-current-row
      style="flex: 1; text-align: center"
    >
      <el-table-column prop="Sex" label="性别" align="center">
        <template #default="scope">
          <div>{{ scope.row.OriginalData.user_info.sex === "f" ? "女" : "男" }}</div>
        </template>
      </el-table-column>
      <el-table-column prop="Age" label="年龄" align="center">
        <template #default="scope">
          <div>{{ scope.row.OriginalData.user_info.age + "岁" }}</div>
        </template>
      </el-table-column>
      <el-table-column
        prop="ReportTime"
        label="筛查时间"
        align="center"
        :formatter="tableDateFormatDay"
      />
      <el-table-column label="结论" align="center">
        <template #default="scope">
          <div>{{ scope.row.Conclusion.length > 0 ? scope.row.Conclusion[0] : "" }}</div>
        </template>
      </el-table-column>
      <el-table-column prop="Advice" label="建议" align="center">
        <template #default="scope">
          <div>{{ scope.row.Advice.length > 0 ? scope.row.Advice[0] : "" }}</div>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" fixed="right">
        <template #default="scope">
          <el-button link type="primary" @click="previewPdf(scope.row.PdfUrl)">查看</el-button>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script setup lang="ts">
import Supplier_Vis_Api from "@/api/supplier-vis";
import { VisReportPageItem } from "@/api/supplier-vis/types";
import { useTableConfig } from "@/hooks/useTableConfig";
import dayjs from "dayjs";
const { tableLoading, pageData, tableRef, tableDateFormatDay } =
  useTableConfig<VisReportPageItem>();

const studentInfo = inject<Ref<{ name: string; phone: string; type: string }>>("studentInfo")!;
const handleGetIkidcareData = async () => {
  const res = await Supplier_Vis_Api.queryReport({
    Phone: studentInfo.value.phone,
    PageIndex: 1,
    PageSize: 999,
  });
  tableLoading.value = true;
  if (res.Type === 200) {
    res.Data.Data.forEach((item) => {
      item.ReportTime = dayjs(item.ReportTime!).format("YYYY-MM-DD");
    });
    pageData.value = res.Data.Data;
  }
  tableLoading.value = false;
};

// 预览PDF文件的方法
const previewPdf = (url: string | undefined) => {
  // 验证URL是否有效
  if (!url || url.trim() === "") {
    ElMessage.warning("PDF文件地址无效，无法预览");
    return;
  }

  // 使用浏览器自带的方法在新标签页打开PDF文件
  try {
    window.open(url, "_blank");
  } catch (error) {
    console.error("打开PDF文件失败:", error);
    ElMessage.error("打开PDF文件失败，请检查链接或联系管理员");
  }
};

onBeforeMount(() => {
  handleGetIkidcareData();
});
</script>

<style lang="scss" scoped></style>
