<template>
  <el-tabs v-model="activeTab" type="card" @tab-click="handleTabClick">
    <el-tab-pane label="脊柱筛查" name="spine">
      <SpineScreening />
    </el-tab-pane>
    <el-tab-pane label="足底筛查" name="foot">
      <FootScreening />
    </el-tab-pane>
    <el-tab-pane label="3D体态检测" name="3d">
      <ThreePostureDetection />
    </el-tab-pane>
  </el-tabs>
</template>

<script setup lang="ts">
import { TabsPaneContext } from "element-plus";
import SpineScreening from "./SpineScreening.vue";
import FootScreening from "./FootScreening.vue";
import ThreePostureDetection from "./ThreePostureDetection.vue";

const activeTab = ref<string>("spine");

const studentInfo = inject<Ref<{ name: string; phone: string; type: string }>>("studentInfo")!;
const handleTabClick = (tab: TabsPaneContext) => {
  activeTab.value = tab.paneName as string;
};

onBeforeMount(() => {
  activeTab.value = studentInfo.value.type;
});
</script>

<style lang="scss" scoped></style>
