<template>
  <div v-loading="tableLoading">
    <ECharts :options="footOptions" style="width: 100%; height: 300px" />
    <el-table
      ref="tableRef"
      v-loading="tableLoading"
      :data="pageData"
      border
      row-key="Id"
      :height="400"
      highlight-current-row
      style="flex: 1; text-align: center"
    >
      <el-table-column prop="Name" label="姓名" align="center" />
      <el-table-column prop="Sex" label="性别" align="center" />
      <el-table-column prop="Age" label="年龄" align="center" />
      <el-table-column prop="School" label="学校" align="center" />
      <el-table-column prop="Grade" label="年级" align="center" />
      <el-table-column prop="Class" label="班级" align="center" />
      <el-table-column prop="Phone" label="手机号" align="center" />
      <el-table-column
        prop="ReportTime"
        label="筛查时间"
        align="center"
        :formatter="tableDateFormatDay"
      />
      <el-table-column prop="LeftFootArch" label="左足弓指数" align="center" />
      <el-table-column prop="RightFootArch" label="右足弓指数" align="center" />
      <el-table-column prop="HalluxValgusLeft" label="左拇翻角度" align="center">
        <template #default="scope">
          <div>{{ scope.row.HalluxValgusLeft + "°" }}</div>
        </template>
      </el-table-column>
      <el-table-column prop="HalluxValgusRight" label="右拇翻角度" align="center">
        <template #default="scope">
          <div>{{ scope.row.HalluxValgusRight + "°" }}</div>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" fixed="right">
        <template #default="scope">
          <el-button link type="primary" @click="previewPdf(scope.row.PDFUrl)">查看</el-button>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script setup lang="ts">
import Supplier_FootScreen_Api from "@/api/supplier-footscreen";
import { FootReportPageItem } from "@/api/supplier-footscreen/types";
import { useTableConfig } from "@/hooks/useTableConfig";
import dayjs from "dayjs";
const { tableLoading, pageData, tableRef, tableDateFormatDay } =
  useTableConfig<FootReportPageItem>();

const studentInfo = inject<Ref<{ name: string; phone: string; type: string }>>("studentInfo")!;
// ECharts 配置选项
const footOptions = ref<echarts.EChartsCoreOption>({});
const handleGetIkidcareData = async () => {
  const res = await Supplier_FootScreen_Api.getReport({
    Name: studentInfo.value.name,
    Phone: studentInfo.value.phone,
    PageIndex: 1,
    PageSize: 999,
  });
  tableLoading.value = true;
  if (res.Type === 200) {
    res.Data.Data.forEach((item) => {
      item.ReportTime = dayjs(item.ReportTime!).format("YYYY-MM-DD");
      // 通过Birthday计算年龄
      item.Age = dayjs().diff(dayjs(item.Birthday), "year");
    });
    pageData.value = res.Data.Data;
    handleGetEchartsData();
  }
  tableLoading.value = false;
};

const handleGetEchartsData = () => {
  footOptions.value = {
    title: {
      text: "足弓指数变化趋势",
    },
    legend: {
      data: ["左足弓指数", "右足弓指数"],
    },
    tooltip: {
      trigger: "axis",
    },
    grid: {
      left: "3%",
      right: "4%",
      bottom: "3%",
      containLabel: true,
    },
    xAxis: {
      type: "category",
      data: pageData.value.map((item) => item.ReportTime!),
    },
    yAxis: {
      type: "value",
    },
    series: [
      {
        name: "左足弓指数",
        type: "line",
        data: pageData.value.map((item) => item.LeftFootArch!),
      },
      {
        name: "右足弓指数",
        type: "line",
        data: pageData.value.map((item) => item.RightFootArch!),
      },
    ],
  };
};

// 预览PDF文件的方法
const previewPdf = (url: string | undefined) => {
  // 验证URL是否有效
  if (!url || url.trim() === "") {
    ElMessage.warning("PDF文件地址无效，无法预览");
    return;
  }

  // 使用浏览器自带的方法在新标签页打开PDF文件
  try {
    window.open(url, "_blank");
  } catch (error) {
    console.error("打开PDF文件失败:", error);
    ElMessage.error("打开PDF文件失败，请检查链接或联系管理员");
  }
};

onBeforeMount(() => {
  handleGetIkidcareData();
});
</script>

<style lang="scss" scoped></style>
