<template>
  <div v-loading="tableLoading">
    <ECharts :options="spinalOptions" style="width: 100%; height: 300px" />
    <el-table
      ref="tableRef"
      v-loading="tableLoading"
      :data="pageData"
      border
      row-key="Id"
      :height="400"
      highlight-current-row
      style="flex: 1; text-align: center"
    >
      <el-table-column prop="Name" label="姓名" align="center" />
      <el-table-column prop="Sex" label="性别" align="center" />
      <el-table-column prop="Age" label="年龄" align="center" />
      <!-- <el-table-column label="学校" align="center">
        <template #default="scope">
          <div>{{ scope.row.School + "/" + scope.row.Grade + "/" + scope.row.Class }}</div>
        </template>
      </el-table-column> -->
      <el-table-column prop="Phone" label="手机号" align="center" />
      <el-table-column
        prop="ReportTime"
        label="筛查时间"
        align="center"
        :formatter="tableDateFormatDay"
      />
      <el-table-column prop="ArtMax" label="ATR最大值" align="center" />
      <el-table-column prop="MaxDirect" label="最大值方向" align="center">
        <template #default="scope">
          <div>{{ ["无", "左", "右"][scope.row.MaxDirect] }}</div>
        </template>
      </el-table-column>
      <el-table-column prop="MaxPosition" label="最大值部位" align="center">
        <template #default="scope">
          <div>{{ position[scope.row.MaxPosition] }}</div>
        </template>
      </el-table-column>
      <el-table-column prop="MaxCentrumPosition" label="最大值锥体位置" align="center" />
      <el-table-column prop="LeftArtMax" label="左侧ATR最大值" align="center" />
      <el-table-column prop="LeftMaxPosition" label="左侧最大值部位" align="center">
        <template #default="scope">
          <div>{{ position[scope.row.LeftMaxPosition] }}</div>
        </template>
      </el-table-column>
      <el-table-column prop="LeftMaxCentrumPosition" label="左侧最大值锥体位置" align="center" />
      <el-table-column prop="RightArtMax" label="右侧ATR最大值" align="center" />
      <el-table-column prop="RightMaxPosition" label="右侧最大值部位" align="center">
        <template #default="scope">
          <div>{{ position[scope.row.RightMaxPosition] }}</div>
        </template>
      </el-table-column>
      <el-table-column prop="RightMaxCentrumPosition" label="右侧最大值锥体位置" align="center" />
      <el-table-column label="操作" align="center" fixed="right">
        <template #default="scope">
          <el-button link type="primary" @click="previewPdf(scope.row.Url)">查看</el-button>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script setup lang="ts">
import Supplier_Ikidcare_Api from "@/api/supplier-ikidcare";
import { SpineReportPageItem } from "@/api/supplier-ikidcare/types";
import { useTableConfig } from "@/hooks/useTableConfig";
import dayjs from "dayjs";
const { tableLoading, pageData, tableRef, tableDateFormatDay } =
  useTableConfig<SpineReportPageItem>();

const studentInfo = inject<Ref<{ name: string; phone: string; type: string }>>("studentInfo")!;
const position: Record<string, string> = {
  L: "腰椎",
  T: "胸椎",
};
// ECharts 配置选项
const spinalOptions = ref<echarts.EChartsCoreOption>({});
const handleGetIkidcareData = async () => {
  const res = await Supplier_Ikidcare_Api.getReportPage({
    Name: studentInfo.value.name,
    Phone: studentInfo.value.phone,
  });
  tableLoading.value = true;
  if (res.Type === 200) {
    res.Data.forEach((item) => {
      item.ArtMax = Math.abs(item.ArtMax!);
      item.ReportTime = dayjs(item.ReportTime!).format("YYYY-MM-DD");
    });
    pageData.value = res.Data;
    handleGetEchartsData();
  }
  tableLoading.value = false;
};

const handleGetEchartsData = () => {
  spinalOptions.value = {
    title: {
      text: "脊柱ATR角度变化趋势",
    },
    tooltip: {
      trigger: "axis",
    },
    grid: {
      left: "3%",
      right: "4%",
      bottom: "3%",
      containLabel: true,
    },
    xAxis: {
      type: "category",
      data: pageData.value.map((item) => item.ReportTime!),
    },
    yAxis: {
      type: "value",
    },
    series: [
      {
        data: pageData.value.map((item) => item.ArtMax!),
        type: "line",
        smooth: true,
      },
    ],
  };
};

// 预览PDF文件的方法
const previewPdf = (url: string | undefined) => {
  // 验证URL是否有效
  if (!url || url.trim() === "") {
    ElMessage.warning("PDF文件地址无效，无法预览");
    return;
  }

  // 使用浏览器自带的方法在新标签页打开PDF文件
  try {
    window.open(url, "_blank");
  } catch (error) {
    console.error("打开PDF文件失败:", error);
    ElMessage.error("打开PDF文件失败，请检查链接或联系管理员");
  }
};

onBeforeMount(() => {
  handleGetIkidcareData();
});
</script>

<style lang="scss" scoped></style>
