<template>
  <el-form ref="formRef" :model="info" :rules="rules" :inline="true" label-width="130px">
    <el-form-item label="ATR最小值" required>
      <el-input-number v-model.number="info.SuggestRule!.StartPoint" class="w-150px!" />
    </el-form-item>
    <el-form-item label="ATR最大值" required>
      <el-input-number v-model="info.SuggestRule!.EndPoint" class="w-150px!" />
    </el-form-item>
    <br />
    <el-form-item label="结论" prop="Name">
      <el-input v-model="info.Name" class="w-150px!" />
    </el-form-item>
    <br />
    <el-form-item label="足弓范围" prop="FootArchSuggestRule">
      <div v-for="(item, index) in info.FootArchSuggestRule" :key="index">
        <div style="margin-bottom: 10px">
          <el-input-number v-model.number="item.StartPoint" class="w-150px!" placeholder="起始点" />
          <span style="margin: 0 5px">~</span>
          <el-input-number v-model.number="item.EndPoint" class="w-150px!" placeholder="结束点" />
          <el-button
            type="danger"
            :icon="Delete"
            circle
            style="margin-left: 10px"
            :disabled="(info.FootArchSuggestRule?.length || 0) <= 1"
            @click="removeFootArchRule(index)"
          />
          <el-button
            v-if="index === (info.FootArchSuggestRule?.length || 0) - 1"
            type="primary"
            :icon="Plus"
            circle
            style="margin-left: 5px"
            @click="addFootArchRule()"
          />
        </div>
      </div>
    </el-form-item>
    <br />
    <el-form-item label="结论" prop="FootArchName">
      <el-input v-model="info.FootArchName" class="w-150px!" />
    </el-form-item>
    <el-form-item label="是否足弓正常" prop="FootArchNormal">
      <el-switch v-model="info.FootArchNormal" class="w-150px!" />
    </el-form-item>
    <br />
    <el-form-item label="建议" prop="SuggestContent">
      <WangEditor v-model="info.SuggestContent" height="300px" />
    </el-form-item>
    <el-form-item label="推荐医院" prop="SuggestOrgIds">
      <el-select v-model="info.SuggestOrgIds" multiple>
        <el-option
          v-for="item in orgList"
          :key="item.OrganizationId"
          :label="item.OrganizationName"
          :value="item.OrganizationId!"
        />
      </el-select>
    </el-form-item>
  </el-form>
</template>

<script setup lang="ts">
import { Delete, Plus } from "@element-plus/icons-vue";
import Content_Api from "@/api/content";
import { SuggestLevelItem } from "@/api/content/types";
import { FormInstance, FormRules } from "element-plus";
import Passport_Api from "@/api/passport";
import { OrganizationConsortium } from "@/api/passport/types";

const info = ref<SuggestLevelItem>({
  SuggestRule: {
    StartPoint: 0,
    EndPoint: 0,
  },
  FootArchSuggestRule: [
    {
      StartPoint: undefined,
      EndPoint: undefined,
    },
  ],
  Name: "",
  FootArchName: "",
  FootArchNormal: false,
  SuggestContent: "",
});

const orgList = ref<OrganizationConsortium[]>([]);
const handleSubmit = async (): Promise<SuggestLevelItem | null> => {
  try {
    await formRef.value!.validate();
    info.value.Type = 1; // Type:1 脊椎筛查   2 视光筛查
    const copyData = JSON.parse(JSON.stringify(info.value));
    if (copyData.Id) {
      copyData.StrId = copyData.Id;
      delete copyData.Id;
    }
    return copyData;
  } catch {
    return null;
  }
};

const validateFootArchRanges = (rule: any, value: any, callback: any) => {
  if (!value || value.length === 0) {
    return callback(new Error("请至少设置一个足弓范围"));
  }
  for (let i = 0; i < value.length; i++) {
    const errorMessage = isValidRangeItem(value[i], i);
    if (errorMessage) {
      return callback(new Error(errorMessage));
    }
  }

  if (value.length > 1) {
    const sortedRanges = value.slice().sort((a: any, b: any) => a.StartPoint - b.StartPoint);
    for (let i = 1; i < sortedRanges.length; i++) {
      if (sortedRanges[i].StartPoint < sortedRanges[i - 1].EndPoint) {
        return callback(new Error("足弓范围区间存在重叠"));
      }
    }
  }
  callback();
};
const formRef = ref<FormInstance>();
const rules = reactive<FormRules>({
  Name: [
    {
      type: "string",
      required: true,
      message: "请输入结论",
      trigger: "blur",
    },
  ],
  SuggestContent: [
    {
      type: "string",
      required: true,
      message: "请输入建议",
      trigger: "blur",
    },
  ],
  FootArchName: [
    {
      type: "string",
      required: true,
      message: "请输入结论",
      trigger: "blur",
    },
  ],
  FootArchSuggestRule: [
    {
      validator: validateFootArchRanges,
      trigger: ["change", "blur"],
    },
  ],
});

const isValidRangeItem = (item: any, itemIndex: number) => {
  if (item.StartPoint === null || item.StartPoint === undefined) {
    return `第 ${itemIndex + 1} 个足弓范围的起始点不能为空`;
  }
  if (item.EndPoint === null || item.EndPoint === undefined) {
    return `第 ${itemIndex + 1} 个足弓范围的结束点不能为空`;
  }
  if (typeof item.StartPoint !== "number" || typeof item.EndPoint !== "number") {
    return `第 ${itemIndex + 1} 个足弓范围的起止点必须是数字`;
  }
  if (item.StartPoint > item.EndPoint) {
    return `第 ${itemIndex + 1} 个足弓范围的起始点不能大于结束点`;
  }
  return null;
};
const handleProcessingData = async (id: string) => {
  const res = await Content_Api.querySuggestLevelByIds([id]);
  if (res.Type === 200) {
    info.value = res.Data[0];
  }
};

const removeFootArchRule = (index: number) => {
  if (info.value.FootArchSuggestRule && info.value.FootArchSuggestRule.length > 1) {
    info.value.FootArchSuggestRule.splice(index, 1);
  }
};

const addFootArchRule = () => {
  if (!info.value.FootArchSuggestRule) {
    info.value.FootArchSuggestRule = [];
  }
  info.value.FootArchSuggestRule.push({
    StartPoint: undefined,
    EndPoint: undefined,
  });
};

const onGetOrgList = async () => {
  const res = await Passport_Api.getOrganizationConsortiumList({
    ConsortiumId: "2dad543b-6ab4-42b8-a1a5-b1c834050d44",
    PageIndex: 1,
    PageSize: 999,
  });
  if (res.Type === 200) {
    orgList.value = res.Data.Data;
  }
};

interface Props {
  id: string;
}
const props = defineProps<Props>();

watch(
  () => props.id,
  (newVal) => {
    if (newVal) {
      handleProcessingData(newVal);
    }
  },
  { immediate: true }
);
onBeforeMount(() => {
  // 获取推荐医院
  onGetOrgList();
});

defineExpose({
  handleSubmit,
});
</script>

<style lang="scss" scoped></style>
