/** 数据看板组件类型定义 */

/** 顶部统计数据类型 */
export interface TopData {
  SchoolCount: number;
  UserCount: number;
  IkidCount: number;
  ProblemRate: string;
}

/** 分布数据项类型 */
export interface DistributionItem {
  value: number;
  name: string;
  percentage: string;
  color: string;
  selected: boolean;
}

/** 性别统计数据类型 */
export interface GenderData {
  BoyRate: number;
  GirlRate: number;
}

/** 脊柱数据类型 */
export interface SpinalData {
  SpinalDistributionData: DistributionItem[];
  SpinalGenderData: GenderData;
}

/** 足弓侧别数据类型 */
export interface FootSideData {
  FootDistributionData: DistributionItem[];
  FootGenderData: GenderData;
}

/** 足弓数据类型 */
export interface FootArchData {
  left: FootSideData;
  right: FootSideData;
  FootDistributionData: DistributionItem[];
  FootGenderData: GenderData;
}

/** 页面数据类型 */
export interface PageData {
  topData: TopData;
  ikidData: SpinalData;
  footData: FootArchData;
}

/** 看板左侧数据类型 */
export interface BoardLeftData {
  topData: TopData;
  ikidData: SpinalData;
  footData: FootArchData;
}

/** ECharts 图表数据项类型 */
export interface ChartDataItem {
  name: string;
  value: number;
  percentage: string;
  [key: string]: any;
}

/** ECharts 回调参数类型 */
export interface ChartParams {
  data: ChartDataItem;
  name: string;
  value: number;
  percent: number;
  [key: string]: any;
}

/** 足弓图表数据类型 */
export interface FootChartData {
  /** x轴数据（年龄） */
  x: string[];
  /** 轻度数据 */
  footLightData: number[];
  /** 中度数据 */
  footModerateData: number[];
  /** 重度数据 */
  footSevereData: number[];
  /** 高足弓数据 */
  footHighData: number[];
}

/** 足弓侧别图表数据类型 */
export interface FootSideChartData {
  /** 左足数据 */
  left: FootChartData;
  /** 右足数据 */
  right: FootChartData;
}

/** 看板右侧数据类型 */
export interface BoardRightData {
  /** 足弓图表数据 */
  footData: FootSideChartData;
  /** 脊柱图表数据 */
  ikidData: IkidChartData;
}
export interface IkidChartData {
  /** 年龄组 */
  ageGroups: string[];
  /** 低风险数据 */
  lowRiskData: number[];
  /** 高风险数据 */
  highRiskData: number[];
}
