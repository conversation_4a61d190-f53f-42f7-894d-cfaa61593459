<template>
  <div class="right-root">
    <div class="right-root-top">
      <div class="h-56px" />
      <ECharts :options="rightIkidChartOptions" width="100%" height="75%" />
    </div>
    <div class="right-root-bottom">
      <div class="h-56px" />
      <div class="h-full flex-1">
        <ECharts :options="rightFootChartOptions" width="100%" height="45%" />
        <ECharts :options="leftFootChartOptions" width="100%" height="45%" />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { ECBasicOption } from "echarts/types/dist/shared";
import { BoardRightData } from "../types";

// 年龄段数据
let ageGroups: string[] = [];
// 低风险异常率数据（百分比）
let lowRiskData: number[] = [];
// 高风险异常率数据（百分比）
let highRiskData: number[] = [];

// 左足弓指数月份数据
let leftXData: string[] = [];
// 右足弓指数月份数据
let rightXData: string[] = [];
// 右足足弓指数数据（百分比）
let rightFootLightData: number[] = []; // 轻微
let rightFootModerateData: number[] = []; // 中度
let rightFootSevereData: number[] = []; // 重度
let rightFootHighData: number[] = []; // 高
// 左足足弓指数数据（百分比）
let leftFootLightData: number[] = []; // 轻微
let leftFootModerateData: number[] = []; // 中度
let leftFootSevereData: number[] = []; // 重度
let leftFootHighData: number[] = []; // 高

// 创建足弓指数图表配置函数
function createFootArchIndexChart(
  title: string,
  lightData: number[],
  moderateData: number[],
  severeData: number[],
  highData: number[]
): ECBasicOption {
  return {
    title: {
      text: title,
      left: "39px",
      top: 10,
      textStyle: {
        fontSize: 16,
        color: "#DFFDFE",
        fontWeight: "bold",
      },
    },
    tooltip: {
      trigger: "axis",
      formatter: function (params: any) {
        let result = `${params[0].name}<br/>`;
        params.forEach((param: any) => {
          result += `${param.marker} ${param.seriesName}: ${param.value}%<br/>`;
        });
        return result;
      },
    },
    legend: {
      data: ["足弓低（轻微）", "足弓低（中度）", "足弓低（重度）", "足弓高"],
      top: 15,
      textStyle: {
        color: "#fff",
      },
    },
    grid: {
      left: "3%",
      right: "4%",
      bottom: "3%",
      top: "25%",
      containLabel: true,
    },
    xAxis: {
      type: "category",
      data: leftXData,
      axisLine: {
        lineStyle: {
          color: "#999",
        },
      },
      axisLabel: {
        color: "#fff",
      },
    },
    yAxis: {
      type: "value",
      min: 0,
      max: 30,
      axisLabel: {
        formatter: "{value}%",
        color: "#fff",
      },
      axisLine: {
        lineStyle: {
          color: "#999",
        },
      },
      splitLine: {
        lineStyle: {
          color: "rgba(255, 255, 255, 0.2)",
        },
      },
    },
    series: [
      {
        name: "足弓低（轻微）",
        type: "line",
        data: lightData,
        smooth: true,
        lineStyle: {
          color: "#0E9CFF",
          width: 3,
        },
        itemStyle: {
          color: "#0E9CFF",
        },
        symbol: "circle",
        symbolSize: 6,
      },
      {
        name: "足弓低（中度）",
        type: "line",
        data: moderateData,
        smooth: true,
        lineStyle: {
          color: "#FFAC26",
          width: 3,
        },
        itemStyle: {
          color: "#FFAC26",
        },
        symbol: "circle",
        symbolSize: 6,
      },
      {
        name: "足弓低（重度）",
        type: "line",
        data: severeData,
        smooth: true,
        lineStyle: {
          color: "#FF6B6B",
          width: 3,
        },
        itemStyle: {
          color: "#FF6B6B",
        },
        symbol: "circle",
        symbolSize: 6,
      },
      {
        name: "足弓高",
        type: "line",
        data: highData,
        smooth: true,
        lineStyle: {
          color: "#f70c00",
          width: 3,
        },
        itemStyle: {
          color: "#f70c00",
        },
        symbol: "circle",
        symbolSize: 6,
      },
    ],
  };
}

// ECharts配置选项（保留原有图表用于top区域）
const rightIkidChartOptions = ref<ECBasicOption>({});

// 右足足弓指数图表配置
let rightFootChartOptions: ECBasicOption = {} as ECBasicOption;

// 左足足弓指数图表配置
let leftFootChartOptions: ECBasicOption = {} as ECBasicOption;

const handleRenderCharts = () => {
  leftFootChartOptions = createFootArchIndexChart(
    "足弓指数（左）",
    leftFootLightData,
    leftFootModerateData,
    leftFootSevereData,
    leftFootHighData
  );
  rightFootChartOptions = createFootArchIndexChart(
    "足弓指数（右）",
    rightFootLightData,
    rightFootModerateData,
    rightFootSevereData,
    rightFootHighData
  );
  rightIkidChartOptions.value = {
    tooltip: {
      trigger: "axis",
      formatter: function (params: any) {
        let result = `${params[0].name}<br/>`;
        params.forEach((param: any) => {
          result += `${param.marker} ${param.seriesName}: ${param.value}%<br/>`;
        });
        return result;
      },
    },
    legend: {
      data: ["低风险", "高风险"],
      top: "10px",
      textStyle: {
        color: "#fff",
      },
    },
    grid: {
      left: "3%",
      right: "4%",
      bottom: "3%",
      top: "15%",
      containLabel: true,
    },
    xAxis: {
      type: "category",
      data: ageGroups,
      axisLine: {
        lineStyle: {
          color: "#999",
        },
      },
      axisLabel: {
        color: "#fff",
      },
    },
    yAxis: {
      type: "value",
      axisLabel: {
        formatter: "{value}%",
        color: "#fff",
      },
      axisLine: {
        lineStyle: {
          color: "#999",
        },
      },
      splitLine: {
        lineStyle: {
          color: "rgba(255, 255, 255, 0.2)",
        },
      },
    },
    series: [
      {
        name: "低风险",
        type: "line",
        data: lowRiskData,
        smooth: true,
        lineStyle: {
          color: "#0E9CFF",
          width: 3,
        },
        itemStyle: {
          color: "#0E9CFF",
        },
        symbol: "circle",
        symbolSize: 6,
      },
      {
        name: "高风险",
        type: "line",
        data: highRiskData,
        smooth: true,
        lineStyle: {
          color: "#FFAC26",
          width: 3,
        },
        itemStyle: {
          color: "#FFAC26",
        },
        symbol: "circle",
        symbolSize: 6,
      },
    ],
  };
};

const handleProcessData = (newVal: BoardRightData) => {
  leftXData = newVal.footData.left.x;
  rightXData = newVal.footData.right.x;
  leftFootLightData = newVal.footData.left.footLightData;
  leftFootModerateData = newVal.footData.left.footModerateData;
  leftFootSevereData = newVal.footData.left.footSevereData;
  leftFootHighData = newVal.footData.left.footHighData;
  rightFootLightData = newVal.footData.right.footLightData;
  rightFootModerateData = newVal.footData.right.footModerateData;
  rightFootSevereData = newVal.footData.right.footSevereData;
  rightFootHighData = newVal.footData.right.footHighData;
  ageGroups = newVal.ikidData.ageGroups;
  lowRiskData = newVal.ikidData.lowRiskData;
  highRiskData = newVal.ikidData.highRiskData;
  handleRenderCharts();
};

interface Props {
  boardRightData: BoardRightData;
}
const props = defineProps<Props>();
watch(
  () => props.boardRightData,
  (newVal) => {
    if (newVal && Object.keys(newVal).length) {
      // 处理数据
      handleProcessData(newVal);
    }
  },
  { immediate: true, deep: true }
);
</script>

<style lang="scss" scoped>
.right-root {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  height: 100%;
  margin-left: 57px;

  &-top {
    width: 853px;
    height: 276px;
    background-image: url("../assets/images/chart-line1.png");
    background-repeat: no-repeat;
    background-position: center;
    background-size: cover;
  }

  &-bottom {
    display: flex;
    flex-direction: column;
    width: 853px;
    height: 576px;
    background-image: url("../assets/images/chart-line2.png");
    background-repeat: no-repeat;
    background-position: center;
    background-size: cover;
  }
}
</style>
