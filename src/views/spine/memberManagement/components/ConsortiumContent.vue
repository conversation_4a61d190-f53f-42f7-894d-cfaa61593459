<template>
  <el-form ref="formRef" :model="info" :rules="rules" :inline="true">
    <el-form-item label="医院" prop="OrganizationId">
      <el-select
        v-model="info.OrganizationId"
        :disabled="Object.keys(info).length > 0"
        class="w150"
        placeholder="请选择"
        filterable
        :clearable="true"
      >
        <el-option
          v-for="(item, index) in baseOrganizationList"
          :key="index"
          :label="item.Name"
          :value="item.Id!"
        />
      </el-select>
    </el-form-item>
    <br />
    <el-form-item label="封面图" prop="CoverImageUrl">
      <SingleImageUpload v-model="info.CoverImageUrl!" />
    </el-form-item>
    <br />
    <el-form-item label="项目简介" prop="Introduction">
      <el-input
        v-model="info.Introduction"
        type="textarea"
        placeholder="请输入项目简介"
        style="width: 400px"
        :autosize="{ minRows: 2, maxRows: 4 }"
      />
    </el-form-item>
    <br />
    <el-form-item label="介绍">
      <WangEditor v-model="info.Introduce" height="350px" />
    </el-form-item>
  </el-form>
</template>

<script setup lang="ts">
import Passport_Api from "@/api/passport";
import { OrganizationConsortium } from "@/api/passport/types";
import { FormInstance, FormRules } from "element-plus";
const formRef = ref<FormInstance>();
const info = ref<OrganizationConsortium>({
  OrganizationId: "",
  Introduce: "",
  CoverImageUrl: "",
  Introduction: "",
});
const handleSubmit = async (): Promise<OrganizationConsortium | null> => {
  try {
    await formRef.value!.validate();
    info.value.ConsortiumId = "2dad543b-6ab4-42b8-a1a5-b1c834050d44";
    return info.value;
  } catch {
    return null;
  }
};

const handleProcessingData = (detailInfo: OrganizationConsortium) => {
  info.value = detailInfo;
};

const rules = reactive<FormRules<OrganizationConsortium>>({
  OrganizationId: [{ required: true, message: "请选择医院", trigger: "change" }],
  CoverImageUrl: [{ required: true, message: "请上传封面图", trigger: "change" }],
  Introduction: [{ required: true, message: "请输入项目简介", trigger: "blur" }],
});

const baseOrganizationList = ref<BaseOrganization[]>([]);
const onGetOrganizationList = async () => {
  const res = await Passport_Api.getOrganizationList({
    IsEnabled: true,
    DtoTypeName: "QueryOrgDtoForDropDownList",
    PageIndex: 1,
    PageSize: 9999,
    Scopeable: false,
  });
  if (res.Type === 200) {
    baseOrganizationList.value = res.Data.Rows;
  }
};

interface Props {
  detailInfo: OrganizationConsortium;
}
const props = defineProps<Props>();

watch(
  () => props.detailInfo,
  (newVal) => {
    if (newVal) {
      handleProcessingData(newVal);
    }
  },
  { immediate: true }
);

defineExpose({
  handleSubmit,
});
onBeforeMount(() => {
  // 获取医院列表
  onGetOrganizationList();
});
</script>

<style lang="scss" scoped></style>
