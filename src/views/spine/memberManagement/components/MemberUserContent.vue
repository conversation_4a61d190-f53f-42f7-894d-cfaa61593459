<template>
  <BaseTableSearchContainer @size-changed="tableResize">
    <template #search>
      <TBSearchContainer>
        <template #left>
          <el-form :model="params" label-position="right" :inline="true">
            <el-form-item label="科室">
              <DeptSelect
                v-model="params.DeptIds"
                :multiple-limit="1"
                :org-id="props.orgId"
                multiple
                :max="1"
              />
            </el-form-item>
            <el-form-item label="关键字">
              <el-input
                v-model="params.Keyword"
                placeholder="请输入姓名"
                @keyup.enter="handleQuery"
              />
            </el-form-item>
          </el-form>
        </template>
        <template #right>
          <el-button type="primary" @click="handleQuery">查询</el-button>
        </template>
      </TBSearchContainer>
    </template>
    <template #table>
      <el-table
        ref="tableRef"
        v-loading="tableLoading"
        :data="pageData"
        :total="total"
        border
        row-key="Id"
        height="400"
        highlight-current-row
        style="flex: 1; text-align: center"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" reserve-selection width="55" align="center" />
        <el-table-column prop="Name" label="姓名" align="center" />
        <el-table-column label="入驻科室" :show-overflow-tooltip="true" align="center">
          <template #default="scope">
            {{ getDepartmentsName(scope.row.Department) }}
          </template>
        </el-table-column>
        <el-table-column prop="PhoneNumber" label="手机号" align="center" />
      </el-table>
    </template>
    <template #pagination>
      <Pagination
        v-if="total > 0"
        v-model:total="total"
        v-model:page="params.PageIndex!"
        v-model:limit="params.PageSize!"
        @pagination="handleGetUserData"
      />
    </template>
  </BaseTableSearchContainer>
</template>

<script setup lang="ts">
import Passport_Api from "@/api/passport";
import { UserProfileParams } from "@/api/passport/types";
import { useTableConfig } from "@/hooks/useTableConfig";
const { tableLoading, pageData, total, tableRef, tableResize, selectedTableIds } =
  useTableConfig<BaseUserProfile>();
interface Props {
  orgId: string;
}
const props = defineProps<Props>();
const params = ref<UserProfileParams>({
  DeptIds: null,
  RoleTypes: ["doctor", "therapist"],
  OrgIds: [props.orgId],
  DtoTypeName: "QueryUserOutputDto2",
  IsEnabled: true,
  Pageable: true,
  SingleOne: false,
  Scopeable: false,
  PageIndex: 1,
  PageSize: 20,
});
const handleSubmit = (): string[] => {
  if (!selectedTableIds.value.length) {
    ElMessage.warning("请选择要添加的人员");
    return [];
  }
  return selectedTableIds.value;
};

const clearSelectedTable = () => {
  selectedTableIds.value = [];
  tableRef.value?.clearSelection();
};

const handleSelectionChange = (val: BaseUserProfile[]) => {
  selectedTableIds.value = val.map((item) => item.Id);
};

const handleGetUserData = async () => {
  tableLoading.value = true;
  const res = await Passport_Api.getUserProfile(params.value);
  if (res.Type === 200) {
    pageData.value = res.Data.Row;
    total.value = res.Data.Total;
  }
  tableLoading.value = false;
};

const getDepartmentsName = (row: BaseDepartment) => {
  return row ? row.Name : "";
};

const handleQuery = () => {
  params.value.PageIndex = 1;
  handleGetUserData();
};

watch(
  () => props.orgId,
  (newVal) => {
    if (newVal) {
      handleGetUserData();
    }
  },
  { immediate: true }
);

defineExpose({
  handleSubmit,
  clearSelectedTable,
});
</script>

<style lang="scss" scoped></style>
