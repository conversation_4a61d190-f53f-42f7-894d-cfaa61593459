import { type GlobalStatisticItem } from "@/api/supplier-common/types";

export interface PageGlobalStatisticItem extends GlobalStatisticItem {
  FootReportDataObject?: FootReportDataObject;
  IkidReportDataObject?: IkidReportDataObject;
}
export interface FootReportDataObject {
  HalluxValgusLeft?: number;
  HalluxValgusRight?: number;
  LeftFootArch?: number;
  RightFootArch?: number;
}
export interface IkidReportDataObject {
  ArtMax?: number;
  MaxDirect?: number;
  MaxPosition?: string;
  MaxCentrumPosition?: string;
  LeftArtMax?: number;
  LeftMaxPosition?: string;
  LeftMaxCentrumPosition?: string;
  RightArtMax?: number;
  RightMaxPosition?: string;
  RightMaxCentrumPosition?: string;
}
