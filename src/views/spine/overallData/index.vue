<template>
  <div class="app-container">
    <BaseTableSearchContainer @size-changed="tableResize">
      <template #search>
        <TBSearchContainer :is-show-toggle="true">
          <template #left>
            <el-form :model="queryParams" label-position="right" :inline="true">
              <el-form-item label="时间">
                <el-date-picker
                  v-model="timeRange"
                  unlink-panels
                  type="daterange"
                  range-separator="至"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                  format="YYYY-MM-DD"
                  :clearable="false"
                  value-format="YYYY-MM-DD"
                  style="width: 250px"
                  :shortcuts="pageDatePickerShortcuts"
                />
              </el-form-item>
              <el-form-item label="学校">
                <SpineSchoolSelect v-model="queryParams.School" />
              </el-form-item>
              <el-form-item label="年级">
                <el-select
                  v-model="queryParams.Grade"
                  placeholder="请选择"
                  clearable
                  :empty-values="[null, undefined, '']"
                  :value-on-clear="() => null"
                >
                  <el-option label="一年级" value="一年级" />
                  <el-option label="二年级" value="二年级" />
                  <el-option label="三年级" value="三年级" />
                  <el-option label="四年级" value="四年级" />
                  <el-option label="五年级" value="五年级" />
                  <el-option label="六年级" value="六年级" />
                </el-select>
              </el-form-item>
              <el-form-item label="关键字搜索" prop="Keyword">
                <el-input
                  v-model="queryParams.Keyword"
                  placeholder="姓名/电话号码"
                  @keyup.enter="handleQuery"
                />
              </el-form-item>
            </el-form>
          </template>
          <template #right>
            <el-button type="primary" icon="search" @click="handleQuery">搜索</el-button>
          </template>
        </TBSearchContainer>
      </template>
      <template #table>
        <el-table
          ref="tableRef"
          v-loading="tableLoading"
          :data="pageData"
          :total="total"
          border
          row-key="Id"
          :height="tableFluidHeight"
          highlight-current-row
          style="flex: 1; text-align: center"
        >
          <el-table-column prop="Name" label="姓名" align="left">
            <template #default="scope">
              <div>{{ scope.row.Name }}</div>
              <div>{{ scope.row.School + "/" + scope.row.Grade + "/" + scope.row.Class }}</div>
              <div>
                脊柱结果:
                <el-tag
                  v-if="scope.row.IkidAnalysisData?.Analysis !== undefined"
                  :type="getSpineTagType(scope.row.IkidAnalysisData?.Analysis)"
                  size="small"
                >
                  {{ getSpineText(scope.row.IkidAnalysisData?.Analysis) }}
                </el-tag>
                <span v-else class="text-gray-400">未检测</span>
              </div>
              <!-- <div>
                足底结果:
                <el-tag
                  v-if="scope.row.FootAnalysisData?.Left !== undefined"
                  :type="getFootTagType(scope.row.FootAnalysisData?.Left)"
                  size="small"
                  style="margin-right: 4px"
                >
                  左{{ getFootText(scope.row.FootAnalysisData?.Left) }}
                </el-tag>
                <el-tag
                  v-if="scope.row.FootAnalysisData?.Right !== undefined"
                  :type="getFootTagType(scope.row.FootAnalysisData?.Right)"
                  size="small"
                >
                  右{{ getFootText(scope.row.FootAnalysisData?.Right) }}
                </el-tag>
                <span
                  v-if="!scope.row.FootAnalysisData?.Left && !scope.row.FootAnalysisData?.Right"
                  class="text-gray-400"
                >
                  未检测
                </span>
              </div> -->
            </template>
          </el-table-column>
          <el-table-column label="脊柱" align="center">
            <template #default="scope">
              <div>
                ATR最大值： 左：{{
                  handleGetIkidArch(scope.row.IkidReportData, "LeftArtMax") + "°"
                }}， 右：{{ handleGetIkidArch(scope.row.IkidReportData, "RightArtMax") + "°" }}
              </div>
              <div>
                最大值部位： 左：{{
                  position[handleGetIkidArch(scope.row.IkidReportData, "LeftMaxPosition")]
                }}， 右：{{
                  position[handleGetIkidArch(scope.row.IkidReportData, "RightMaxPosition")]
                }}
              </div>
              <div>
                最大值锥体位置： 左：{{
                  handleGetIkidArch(scope.row.IkidReportData, "LeftMaxCentrumPosition")
                }}， 右：{{
                  handleGetIkidArch(scope.row.IkidReportData, "RightMaxCentrumPosition")
                }}
              </div>
              <div>
                最大值方向：{{
                  ["无", "左", "右"][
                    Number(handleGetIkidArch(scope.row.IkidReportData, "MaxDirect"))
                  ]
                }}
              </div>
              <div>
                最大值部位：{{
                  position[handleGetIkidArch(scope.row.IkidReportData, "MaxPosition")]
                }}
              </div>
            </template>
          </el-table-column>
          <el-table-column label="足弓" align="center">
            <template #default="scope">
              <div>
                足弓指数： 左：{{ handleGetFootArch(scope.row.FootReportData, "LeftFootArch") }}，
                右：{{ handleGetFootArch(scope.row.FootReportData, "RightFootArch") }}
              </div>
              <div>
                拇翻角度： 左：{{
                  handleGetFootArch(scope.row.FootReportData, "HalluxValgusLeft") + "°"
                }}， 右：{{
                  handleGetFootArch(scope.row.FootReportData, "HalluxValgusRight") + "°"
                }}
              </div>
            </template>
          </el-table-column>
        </el-table>
      </template>
      <template #pagination>
        <Pagination
          v-if="total > 0"
          v-model:total="total"
          v-model:page="queryParams.PageIndex"
          v-model:limit="queryParams.PageSize"
          @pagination="handleGetTableList"
        />
      </template>
    </BaseTableSearchContainer>
  </div>
</template>

<script setup lang="ts">
import dayjs from "dayjs";
import { useTableConfig } from "@/hooks/useTableConfig";
import { GlobalStatisticsInputDTO } from "@/api/supplier-common/types";
import Supplier_Common_Api from "@/api/supplier-common";
import { SpineAnalysisResult, FootAnalysisResult } from "@/enums/Spine";
import { FootReportDataObject, IkidReportDataObject, PageGlobalStatisticItem } from "./types";
const pageDatePickerShortcuts = [
  {
    text: "2025年上半年",
    value() {
      const end = "2025-06-30 23:59:59";
      const start = "2025-05-26 00:00:00";
      return [start, end];
    },
  },
];

defineOptions({
  name: "SpinalScreeningOverallData",
});

const queryParams = ref<GlobalStatisticsInputDTO>({
  PageIndex: 1,
  PageSize: 20,
  StartDate: "2025-05-26 00:00:00",
  EndDate: "2025-06-30 23:59:59",
  School: null,
  Grade: null,
  Class: null,
  Keyword: "",
});
const timeRange = ref<[string, string]>([
  dayjs().format("2025-05-26 00:00:00"),
  dayjs().format("2025-06-30 23:59:59"),
]);

const position: Record<string, string> = {
  L: "腰椎",
  T: "胸椎",
};

const { tableLoading, pageData, total, tableRef, exportLoading, tableFluidHeight, tableResize } =
  useTableConfig<PageGlobalStatisticItem>();

const handleQuery = () => {
  queryParams.value.PageIndex = 1;
  handleGetTableList();
};

const handleGetTableList = async () => {
  tableLoading.value = true;
  const res = await Supplier_Common_Api.globalStatistics(queryParams.value);
  if (res.Type === 200) {
    pageData.value = res.Data.Data;
    total.value = res.Data.Total;
  }
  tableLoading.value = false;
};

watch(timeRange, (newVal) => {
  queryParams.value.StartDate = dayjs(newVal[0]).format("YYYY-MM-DD 00:00:00");
  queryParams.value.EndDate = dayjs(newVal[1]).format("YYYY-MM-DD 23:59:59");
});

// 获取脊柱结果标签类型
const getSpineTagType = (analysis?: number): "success" | "warning" | "danger" | undefined => {
  if (analysis === undefined || analysis === null) return undefined;
  switch (analysis) {
    case SpineAnalysisResult.Normal:
      return "success"; // 正常-绿色
    case SpineAnalysisResult.Mild:
      return "warning"; // 轻度-黄色
    case SpineAnalysisResult.Severe:
      return "danger"; // 重度-红色
    default:
      return undefined;
  }
};

// 获取足底结果标签类型
const getFootTagType = (analysis?: number): "success" | "warning" | "danger" | undefined => {
  if (analysis === undefined || analysis === null) return undefined;
  switch (analysis) {
    case FootAnalysisResult.Normal:
      return "success"; // 正常-绿色
    case FootAnalysisResult.High:
    case FootAnalysisResult.LowMild:
      return "warning"; // 高足弓和轻度低足弓-黄色
    case FootAnalysisResult.LowModerate:
    case FootAnalysisResult.LowSevere:
      return "danger"; // 中度和重度低足弓-红色
    default:
      return undefined;
  }
};

// 获取脊柱结果文本
const getSpineText = (analysis?: number): string => {
  if (analysis === undefined || analysis === null) return "未检测";
  switch (analysis) {
    case SpineAnalysisResult.Normal:
      return "正常";
    case SpineAnalysisResult.Mild:
      return "脊柱侧弯低风险";
    case SpineAnalysisResult.Severe:
      return "脊柱侧弯高风险";
    default:
      return "未知";
  }
};

// 获取足底结果文本
const getFootText = (analysis?: number): string => {
  if (analysis === undefined || analysis === null) return "未检测";
  switch (analysis) {
    case FootAnalysisResult.Normal:
      return "正常";
    case FootAnalysisResult.High:
      return "高足弓";
    case FootAnalysisResult.LowMild:
      return "轻度低足弓";
    case FootAnalysisResult.LowModerate:
      return "中度低足弓";
    case FootAnalysisResult.LowSevere:
      return "重度低足弓";
    default:
      return "未知";
  }
};

const handleGetFootArch = (info: string, type: keyof FootReportDataObject): string => {
  try {
    const params: FootReportDataObject = JSON.parse(info);
    return params[type as keyof FootReportDataObject]?.toString() ?? "未获取到数据";
  } catch (error) {
    return "未获取到数据";
  }
};

const handleGetIkidArch = (info: string, type: keyof IkidReportDataObject): string => {
  try {
    const params: IkidReportDataObject = JSON.parse(info);
    return params[type as keyof IkidReportDataObject]?.toString() ?? "未获取到数据";
  } catch (error) {
    return "未获取到数据";
  }
};

onActivated(() => {
  handleGetTableList();
});
</script>

<style lang="scss" scoped></style>
