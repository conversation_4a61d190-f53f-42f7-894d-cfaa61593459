<template>
  <div v-loading="formLoading" class="p-20px overflow-y-auto h-600px">
    <el-form
      :ref="kFormRef"
      :model="formData"
      :rules="rules"
      label-width="80px"
      :disabled="props.disabled"
    >
      <el-row class="flex justify-between">
        <el-col :span="16">
          <el-button type="primary" @click="showPatientSelect = true">选择患者</el-button>
          <el-text v-if="formData.StartTime && props.disabled" class="ml-15px">
            上次随访时间:{{ formData.StartTime }}
          </el-text>
        </el-col>
        <el-col :span="8">
          <el-form-item label="患者编号" prop="PatNo">
            <el-input v-model="formData.PatNo" clearable placeholder="请输入患者编号" />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="8">
          <el-form-item label="姓名" prop="PatName">
            <el-input
              v-model="formData.PatName"
              clearable
              :disabled="isSelectedPatient"
              placeholder="请输入姓名"
            />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="性别" prop="Sex">
            <KSelect
              v-model="formData.Sex"
              :data="[
                { label: '男', value: '男' },
                { label: '女', value: '女' },
                { label: '未知', value: '未知' },
              ]"
              :disabled="isSelectedPatient"
            />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="年龄" prop="Age">
            <el-input
              v-model.number="formData.Age"
              type="number"
              clearable
              :disabled="isSelectedPatient"
              placeholder="请输入年龄"
            />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="8">
          <el-form-item label="随访方式" prop="Type">
            <KSelect
              v-model="formData.Type"
              :data="[
                { label: '电话随访', value: 1 },
                { label: '在线随访', value: 2, disabled: !isSelectedPatient },
              ]"
            />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="随访时间" prop="StartTime">
            <el-date-picker
              v-model="formData.StartTime"
              type="date"
              placeholder="请选择日期"
              :disabled-date="
                (data: Date) => {
                  if (formData.Type === 1) {
                    return data.getTime() > new Date().getTime();
                  }
                  return false;
                }
              "
              :clearable="false"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
            />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="执行人" prop="ExecName">
            <el-input
              v-model="formData.ExecName"
              type="text"
              clearable
              placeholder="请输入执行人"
            />
          </el-form-item>
        </el-col>
      </el-row>
      <el-col :span="8">
        <el-form-item label="随访名称" prop="Name">
          <el-input v-model="formData.Name" type="text" clearable placeholder="请输入随访名称" />
        </el-form-item>
      </el-col>
      <el-col :span="16">
        <el-form-item label="备注" prop="Remark">
          <el-input
            v-model="formData.Remark"
            type="textarea"
            :autosize="{ minRows: 3, maxRows: 9 }"
          />
        </el-form-item>
      </el-col>
      <el-col :span="8">
        <el-form-item v-if="isAdd" label="随访问卷" prop="SelectedGaugeIds">
          <el-select
            v-model="formData.SelectedGaugeIds"
            clearable
            multiple
            placeholder="请搜索选择"
            filterable
            :loading="gaugeListLoading"
          >
            <el-option
              v-for="item in gaugeList"
              :key="item.Id!"
              :label="item.Name"
              :value="item.Id!"
            />
          </el-select>
        </el-form-item>
      </el-col>
    </el-form>
    <!-- 随访问卷列表 -->
    <el-table
      v-if="!isAdd && formData.Details"
      v-loading="formLoading"
      :data="formData.Details"
      border
      :header-cell-style="{ textAlign: 'center' }"
      :cell-style="{ textAlign: 'center' }"
      highlight-current-row
    >
      <el-table-column label="类型">
        <template #default="scope">
          {{ ["", "量表", "宣教", "问卷"][scope.row.Type ?? 0] }}
        </template>
      </el-table-column>
      <el-table-column prop="RelatedName" label="姓名" />
      <el-table-column label="状态">
        <template #default="scope">
          <span v-if="scope.row.Type !== 2">
            {{ ["未填写", "已填写", "已过期"][scope.row.State] }}
          </span>
          <span v-else>{{ ["未读", "已读", "已过期"][scope.row.State] }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作">
        <template #default="scope">
          <el-button
            v-if="scope.row.State === 0 && scope.row.Type !== 2"
            link
            type="primary"
            @click="handleExecuteOperation(scope.row, false)"
          >
            执行
          </el-button>
          <el-button
            v-if="scope.row.BusinessId && scope.row.Type !== 2"
            link
            type="primary"
            @click="handleExecuteOperation(scope.row, true)"
          >
            查看
          </el-button>
        </template>
      </el-table-column>
    </el-table>
  </div>
  <!-- 底部按钮 -->
  <div class="flex justify-end">
    <el-button @click="emit('cancel')">取消</el-button>
    <el-button v-if="!props.disabled" type="primary" @click="onSubmitForm">确定</el-button>
  </div>

  <!-- 选择患者 -->
  <el-dialog
    v-model="showPatientSelect"
    title="选择患者"
    width="850"
    destroy-on-close
    @close="showPatientSelect = false"
  >
    <PatientSelect
      :organization-id="props.data.OrgId!"
      @cancel="showPatientSelect = false"
      @select="handleSelectPatient"
    />
  </el-dialog>

  <!-- 查看/执行随访问卷 -->
  <el-dialog
    v-model="showExecutionForm.isShow"
    title="查看"
    width="850"
    destroy-on-close
    @close="showExecutionForm.isShow = false"
  >
    <ExecutionForm
      :data="showExecutionForm.data"
      :remark="formData.Remark"
      :executor-name="formData.ExecName"
      :disabled="showExecutionForm.disabled"
      @cancel="showExecutionForm.isShow = false"
      @submit="handleConfirmExecute"
    />
  </el-dialog>
</template>

<script setup lang="ts">
import Training_Api from "@/api/training";
import { FormRules, FormInstance, dayjs } from "element-plus";
import Consult_Api from "@/api/consult";
import { useUserStore } from "@/store";
import { FollowUpDetail, FollowUpPlan, PlatformAddFollowUpPlanParams } from "@/api/consult/types";
import { PatientRedash } from "@/api/report/types";

import ExecutionForm from "./ExecutionForm.vue";
import PatientSelect from "./PatientSelect.vue";

interface FollowUpPlanForm extends FollowUpPlan {
  // 选择的随访问卷Id列表
  SelectedGaugeIds: string[];
}

const kEnableDebug = false;
defineOptions({
  name: "FollowUpForm",
});

const kFormRef = "ruleFormRef";
const props = defineProps<{
  data: FollowUpPlan;
  disabled: boolean;
}>();

const emit = defineEmits<{
  cancel: [];
  submit: [];
}>();

// 是否显示选择患者弹窗
const showPatientSelect = ref(false);
// 是否显示查看/执行随访问卷弹窗
const showExecutionForm = reactive({ isShow: false, disabled: false, data: {} as FollowUpDetail });

// 是否是新增
const isAdd = computed(() => {
  return !props.data.Id;
});

const formLoading = ref(false);
// 表单实例
const formRef = useTemplateRef<FormInstance>(kFormRef);
// 表单数据
const formData = reactive<FollowUpPlanForm>({
  SelectedGaugeIds: [],
});

// 表单验证规则
const rules = reactive<FormRules<FollowUpPlanForm>>({
  PatName: [
    {
      required: true,
      message: "请输入姓名",
      trigger: "blur",
    },
  ],
  Sex: [
    {
      required: true,
      message: "请选择性别",
      trigger: "change",
    },
  ],
  Age: [
    {
      type: "number",
      required: true,
      message: "请输入年龄",
      trigger: "blur",
    },
  ],
  Type: [
    {
      required: true,
      message: "请选择随访方式",
      trigger: "change",
    },
  ],
  StartTime: [
    {
      required: true,
      message: "请选择随访时间",
      trigger: "blur",
    },
  ],
  Name: [
    {
      required: true,
      message: "请输入随访名称",
      trigger: "blur",
    },
  ],
  SelectedGaugeIds: [
    {
      required: true,
      message: "请选择随访问卷",
      trigger: "change",
    },
  ],
});

// 选择的患者信息
const selectedPatient = ref({});
// 是否已选择患者信息
const isSelectedPatient = computed(() => {
  return Object.keys(selectedPatient.value).length > 0;
});

// 随访问卷列表
const gaugeList = ref<BaseGauge[]>([]);
// 随访问卷列表加载状态
const gaugeListLoading = ref(false);

onMounted(async () => {
  formLoading.value = true;

  if (props.data.Id) {
    // 编辑、查看
    const r = await requestFollowUpPlanDetail();
    formLoading.value = false;

    if (r.Type !== 200) {
      ElMessage.error(r.Content);
      return;
    }
  } else {
    // 新增
    const data = JSON.parse(JSON.stringify(props.data));
    kEnableDebug && console.debug("props", data);
    Object.assign(formData, data);
    formData.Type = 1;
    formData.Name = dayjs().format("YYYY-MM-DD") + "电话随访";
    formData.StartTime = dayjs().format("YYYY-MM-DD");

    const r = await requestGaugeList();
    formLoading.value = false;

    if (r.Type !== 200) {
      ElMessage.error(r.Content);
      return;
    }
  }
});

// 选择患者
function handleSelectPatient(row: PatientRedash) {
  kEnableDebug && console.debug("选择患者", row);

  selectedPatient.value = row;
  formData.PatName = row.Name;
  formData.Sex = row.Sex ? row.Sex : "未知";
  formData.Age = parseInt(row.Age ? row.Age : "0");
  formData.PatId = row.UserId;
  showPatientSelect.value = false;
}

// 执行、查看执行操作
async function handleExecuteOperation(row: FollowUpDetail, disabled: boolean = false) {
  kEnableDebug && console.debug("执行、查看执行操作", row, disabled);

  showExecutionForm.disabled = disabled;
  showExecutionForm.data = row;
  showExecutionForm.isShow = true;
}

// 随访问卷确认执行
async function handleConfirmExecute() {
  showExecutionForm.isShow = false;
  ElNotification.success("执行成功");

  // 执行后刷新
  formLoading.value = true;
  const r = await requestFollowUpPlanDetail();
  formLoading.value = false;
  if (r.Type !== 200) {
    ElMessage.error(r.Content);
    return;
  }
}

// 提交表单
function onSubmitForm() {
  if (!formRef.value) return;

  formRef.value.validate((valid, fields) => {
    if (valid) {
      requestAddFollowUpPlan();
    } else {
      kEnableDebug && console.debug("提交失败", fields, formData);
    }
  });
}

// 请求随访明细
async function requestFollowUpPlanDetail() {
  const r = await Consult_Api.getFollowUpPlanDetail(props.data.ShowId!);
  if (r.Type === 200) {
    Object.assign(formData, r.Data);
    formData.StartTime = dayjs(formData.StartTime).format("YYYY-MM-DD");
    formData.SelectedGaugeIds =
      r.Data.Details?.filter((item) => item.RelatedId).map((item) => item.RelatedId!) ?? [];
  }

  return r;
}

// 请求随访问卷列表
async function requestGaugeList() {
  gaugeListLoading.value = true;
  const r = await Training_Api.getEvaluateGaugePage({
    IsEnble: true,
    Type: 1,
    OrganizationId: props.data.OrgId,
    PageIndex: 1,
    PageSize: 999,
  });
  gaugeListLoading.value = false;
  if (r.Type === 200) {
    gaugeList.value = r.Data.Rows;
  }

  return r;
}

/** 新增数据 */
async function requestAddFollowUpPlan() {
  kEnableDebug && console.debug("requestAddFollowUpPlan", formData);

  formLoading.value = true;
  const userId = useUserStore().userInfo.Id;
  const params: PlatformAddFollowUpPlanParams = {
    PatName: formData.PatName!,
    Sex: formData.Sex!,
    Age: formData.Age!,
    PatNo: formData.PatNo,
    Name: formData.Name!,
    ExecName: formData.ExecName,
    StartTime: formData.StartTime!,
    OrgId: props.data.OrgId!,
    OrgName: props.data.OrgName!,
    CreatorId: userId,
    CreatorName: useUserStore().userInfo.Name,
    Type: formData.Type!,
    Sort: 1,
    Remark: formData.Remark,
    FollowUpPlanDetailInputDtos: formData.SelectedGaugeIds.map((id) => ({
      RelatedId: id,
      RelatedName: gaugeList.value.find((gauge) => gauge.Id === id)?.Name,
      Type: 3,
    })),
    PatId: formData.PatId,
  };
  const r = await Consult_Api.platformAddFollowUpPlan(params);
  formLoading.value = false;
  if (r.Type === 200) {
    emit("submit");
  } else {
    ElMessage.error(r.Content);
  }
}
</script>

<style lang="scss" scoped></style>
