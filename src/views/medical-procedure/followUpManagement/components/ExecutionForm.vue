<template>
  <div class="p-20px overflow-y-auto h-600px">
    <el-form :ref="kFormRef" :model="gaugeProblems" label-width="0px" :disabled="props.disabled">
      <template v-for="(problem, index) in gaugeProblems" :key="index">
        <!-- 单选 -->
        <el-form-item
          v-if="problem.ProblemType === 1"
          :key="index"
          :prop="`${index}.SelectedId`"
          :rules="[{ required: problem.IsRequired, message: '请选择选项', trigger: 'change' }]"
        >
          <div class="flex flex-col justify-start items-start">
            <el-text class="self-start!">
              {{ index + 1 + "、" + problem.Title }}
              <el-text v-if="problem.IsRequired" style="color: red">*</el-text>
            </el-text>
            <el-radio-group v-model="gaugeProblems[index].SelectedId">
              <el-radio
                v-for="option in problem.GaugeProblemDetails"
                :key="option.Id"
                :label="option.ProblemOption"
                :value="option.Id"
              >
                {{ option.ProblemOption }}
              </el-radio>
            </el-radio-group>
          </div>
        </el-form-item>
        <!-- 多选 -->
        <el-form-item
          v-if="problem.ProblemType === 2"
          :key="index"
          :prop="`${index}.SelectedIds`"
          :rules="[{ required: problem.IsRequired, message: '请选择选项', trigger: 'change' }]"
        >
          <div class="flex flex-col justify-start items-start">
            <el-text class="self-start!">
              {{ index + 1 + "、" + problem.Title }}
              <el-text v-if="problem.IsRequired" style="color: red">*</el-text>
            </el-text>
            <el-checkbox-group v-model="gaugeProblems[index].SelectedIds">
              <el-checkbox
                v-for="option in problem.GaugeProblemDetails"
                :key="option.Id"
                :label="option.ProblemOption"
                :value="option.Id"
              />
            </el-checkbox-group>
          </div>
        </el-form-item>
        <!-- 问答 -->
        <el-form-item
          v-if="problem.ProblemType === 3"
          :key="index"
          :prop="`${index}.Answer`"
          :rules="[
            {
              type: 'string',
              required: problem.IsRequired,
              message: '请输入内容',
              trigger: 'blur',
            },
          ]"
        >
          <el-text>
            {{ index + 1 + "、" + problem.Title }}
            <el-text v-if="problem.IsRequired" style="color: red">*</el-text>
          </el-text>
          <el-input
            v-model="gaugeProblems[index].Answer"
            type="textarea"
            :autosize="{ minRows: 2, maxRows: 9 }"
          />
        </el-form-item>
        <!-- 填分值 -->
        <el-form-item
          v-if="problem.ProblemType === 4"
          :key="index"
          :prop="`${index}.AnswerNumber`"
          :rules="[
            {
              type: 'number',
              required: problem.IsRequired,
              message: '请输入分值',
              trigger: 'blur',
            },
          ]"
        >
          <el-text>
            {{ index + 1 + "、" + problem.Title }}
            <el-text v-if="problem.IsRequired" style="color: red">*</el-text>
          </el-text>
          <el-input v-model.number="gaugeProblems[index].AnswerNumber" type="number" />
        </el-form-item>
      </template>
    </el-form>
  </div>

  <!-- 底部按钮 -->
  <div class="flex justify-end">
    <el-button @click="emit('cancel')">取消</el-button>
    <el-button v-if="!props.disabled" type="primary" @click="onSubmitForm">确定</el-button>
  </div>
</template>

<script setup lang="ts">
import Training_Api from "@/api/training";
import { FormInstance } from "element-plus";
import { useUserStore } from "@/store";
import { DctPatGauge, GaugeDetail, GaugeProblem } from "@/api/training/types";
import { FollowUpDetail } from "@/api/consult/types";

interface GaugeProblemShow extends GaugeProblem {
  // 选中的单选id
  SelectedId?: string;

  // 选中的多选id
  SelectedIds?: string[];

  // 问答题答案
  Answer?: string;

  // 填分值答案
  AnswerNumber?: number;
}

const kEnableDebug = false;
const kFormRef = "ruleFormRef";
defineOptions({
  name: "ExecutionForm",
});

const props = defineProps<{
  data: FollowUpDetail;
  remark?: string;
  executorName?: string;
  disabled: boolean;
}>();

const emit = defineEmits<{
  cancel: [];
  submit: [];
}>();

const formRef = useTemplateRef<FormInstance>(kFormRef);

// 随访问卷问题列表加载状态
const gaugeLoading = ref(false);
// 随访问卷问题列表
const gaugeProblems = ref<GaugeProblemShow[]>([]);

// 临时存储业务数据
let tempPatGauge: GaugeDetail | undefined;

onMounted(() => {
  kEnableDebug && console.debug("props", props.data);

  if (props.data.BusinessId) {
    // 查看
    requestBusinessFollowUpDetail();
  } else if (props.data.RelatedId) {
    // 执行
    requestBasicFollowUpDetail();
  }
});

// 点击确定
function onSubmitForm() {
  if (!formRef.value) return;

  formRef.value.validate((valid, fields) => {
    if (valid) {
      requestSubmitData();
    } else {
      kEnableDebug && console.debug("提交失败", fields);
      try {
        const error = Object.values(fields!)[0][0];
        const index = Number(error.field?.split(".")[0]);
        const problem = gaugeProblems.value[index];
        if (problem.ProblemType === 1 || problem.ProblemType === 2) {
          ElMessage.warning(`请选择第${index + 1}题`);
        } else {
          ElMessage.warning(`请填写第${index + 1}题`);
        }
      } catch (error) {
        kEnableDebug && console.warn("提示信息解析失败", JSON.stringify(error));
      }
    }
  });
}

// 请求基础数据详情
async function requestBasicFollowUpDetail() {
  gaugeLoading.value = true;
  const r = await Training_Api.getEvaluateGaugeDetail(props.data.RelatedId!);
  if (r.Type === 200) {
    tempPatGauge = r.Data;
    gaugeProblems.value = r.Data.GaugeProblems ?? [];
  }
  gaugeLoading.value = false;

  return r;
}

// 请求业务数据详情
async function requestBusinessFollowUpDetail() {
  gaugeLoading.value = true;
  const r = await Training_Api.dctGetPatGaugeById({
    patGaugeId: props.data.BusinessId!,
  });
  if (r.Type === 200 && r.Data.length > 0) {
    gaugeProblems.value =
      r.Data[0].PatGaugeProblems?.map((e) => {
        let data: GaugeProblemShow = {
          ...e,
          GaugeProblemDetails: e.PatGaugeProblemDetails,
        };
        if (e.PatGaugeProblemDetails?.length) {
          switch (e.ProblemType) {
            case 1:
              data.SelectedId = e.PatGaugeProblemDetails.find((v) => v.Answer === "1")?.Id;
              break;
            case 2:
              data.SelectedIds = e.PatGaugeProblemDetails.filter(
                (v) => v.Answer === "1" && v.Id
              ).map((v) => v.Id!);
              break;
            case 3:
              data.Answer = e.PatGaugeProblemDetails[0].Answer;
              break;
            case 4:
              data.AnswerNumber = Number(e.PatGaugeProblemDetails[0].Answer);
              break;
            default:
              break;
          }
        }

        return data;
      }) ?? [];
  }

  return r;
}

// 请求提交
async function requestSubmitData() {
  if (!tempPatGauge) return;

  gaugeLoading.value = true;
  const data: DctPatGauge = {
    Code: tempPatGauge?.Code,
    CreatorId: useUserStore().userInfo.Id,
    Name: tempPatGauge.Name,
    PatGaugeDiseaseRelations: tempPatGauge.GaugeDiseaseRelations?.map((e) => {
      return {
        DiseaseId: e.DiseaseId,
      };
    }),
    PatGaugeResults: tempPatGauge.GaugeResults?.map((e) => {
      return {
        EndPoint: e.EndPoint,
        StartPoint: e.StartPoint,
        Content: e.Content,
      };
    }),
    RelatedId: props.data.ShowId,
    Remark: props.remark,
    Type: tempPatGauge.Type,
    Source: 4,
    PatGaugeProblems: gaugeProblems.value.map((e) => {
      return {
        IsRequired: e.IsRequired,
        ProblemType: e.ProblemType,
        Sort: e.Sort,
        Title: e.Title,
        PatGaugeProblemDetails: e.GaugeProblemDetails?.map((v) => {
          let answer: string | undefined;
          if (e.ProblemType === 1) {
            answer = v.Id === e.SelectedId ? "1" : "0";
          } else if (e.ProblemType === 2) {
            answer = e.SelectedIds?.includes(v.Id!) ? "1" : "0";
          } else if (e.ProblemType === 3) {
            answer = e.Answer;
          } else if (e.ProblemType === 4) {
            answer = `${e.AnswerNumber}`;
          }

          return {
            Answer: answer,
            ProblemOption: v.ProblemOption,
            Sort: v.Sort,
            Points: v.Points,
          };
        }),
      };
    }),
    ExecName: props.executorName,
    Id: tempPatGauge.Id,
  };
  const r = await Training_Api.insertPatEvaluateGauge(data);
  gaugeLoading.value = false;
  if (r.Type === 200) {
    emit("submit");
  } else {
    ElMessage.error(r.Content);
  }
}
</script>

<style lang="scss" scoped>
:deep(.el-form-item) {
  margin-top: 12px;
}
</style>
