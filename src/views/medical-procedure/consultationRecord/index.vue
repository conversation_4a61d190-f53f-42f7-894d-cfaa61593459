<template>
  <div class="app-container">
    <BaseTableSearchContainer @size-changed="tableResize">
      <template #search>
        <TBSearchContainer :is-show-toggle="true">
          <template #left>
            <el-form :model="queryParams" label-position="right" :inline="true">
              <el-form-item label="开具时间">
                <el-date-picker
                  v-model="timeRange"
                  unlink-panels
                  type="daterange"
                  range-separator="至"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                  format="YYYY-MM-DD"
                  :clearable="false"
                  value-format="YYYY-MM-DD"
                  style="width: 250px"
                  :shortcuts="datePickerShortcuts"
                />
              </el-form-item>
              <el-form-item label="方案状态">
                <el-select
                  v-model="queryParams.states"
                  placeholder="请选择"
                  clearable
                  :empty-values="[null, undefined, '']"
                  :value-on-clear="() => null"
                  class="w-180px!"
                  :max-collapse-tags="2"
                  collapse-tags
                  collapse-tags-tooltip
                  multiple
                >
                  <el-option
                    v-for="item in stateType"
                    :key="item.Id"
                    :label="item.Name"
                    :value="item.Id"
                  />
                </el-select>
              </el-form-item>
              <el-form-item label="关键字" prop="keywords">
                <el-input
                  v-model="queryParams.keywords"
                  placeholder="患者姓名"
                  @keyup.enter="handleQuery"
                />
              </el-form-item>
            </el-form>
          </template>
          <template #right>
            <el-button type="primary" icon="search" @click="handleQuery">搜索</el-button>
            <el-button
              type="primary"
              :disabled="!total"
              :loading="exportLoading"
              @click="handleExportExcel"
            >
              导出
            </el-button>
          </template>
        </TBSearchContainer>
      </template>
      <template #table>
        <el-table
          ref="tableRef"
          v-loading="tableLoading"
          :data="pageData"
          :total="total"
          border
          row-key="Id"
          :height="tableFluidHeight"
          highlight-current-row
          style="text-align: center; flex: 1"
        >
          <el-table-column prop="PatName" label="患者姓名" align="center" />
          <el-table-column prop="Sex" label="性别" align="center" />
          <el-table-column prop="Age" label="年龄" align="center" />
          <el-table-column prop="ShowConsultId" label="关联就诊号" width="180" align="center" />
          <el-table-column prop="SupervisorOrgName" label="协诊医院" align="center" />
          <el-table-column prop="SupervisorUserName" label="协诊人" align="center" />
          <el-table-column prop="ApplyOrgName" label="发起医院" align="center" />
          <el-table-column prop="ApplyUserName" label="发起人" align="center" />
          <el-table-column prop="ShowDocNames" label="邀请专家" align="center" />
          <el-table-column
            prop="ApplyTime"
            label="发起时间"
            :formatter="tableDateFormat"
            align="center"
          />
          <el-table-column prop="State" label="状态" align="center">
            <template #default="scope">
              {{ stateType.find((item) => item.Id === scope.row.State)?.Name }}
            </template>
          </el-table-column>
          <el-table-column fixed="right" label="操作" width="180" align="center">
            <template #default="scope">
              <el-button link type="primary" size="small" @click="handlePreview(scope.row)">
                详情
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </template>
      <template #pagination>
        <Pagination
          v-if="total > 0"
          v-model:total="total"
          v-model:page="queryParams.pageIndex"
          v-model:limit="queryParams.pageSize"
          @pagination="handleGetTableList"
        />
      </template>
    </BaseTableSearchContainer>
    <el-dialog v-model="showDialog" :title="dialogTitle" width="900" destroy-on-close>
      <ConsultationRecordContent :detail-info="detailInfo" />
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="showDialog = false">取消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import dayjs from "dayjs";
import { useTableConfig } from "@/hooks/useTableConfig";
import { useDateRangePicker } from "@/hooks/useDateRangePicker";
import { ConsultationStateEnum } from "@/enums/OrderEnum";
import Consult_Api from "@/api/consult";
import { AssistanceRecordItem, SupervisorAssistanceRecordInputDTO } from "@/api/consult/types";
import { ExportEnum } from "@/enums/Other";
import { exportExcel } from "@/utils/serviceUtils";
const { datePickerShortcuts } = useDateRangePicker();

import ConsultationRecordContent from "./components/ConsultationRecordContent.vue";

defineOptions({
  name: "ConsultationRecord",
});

const queryParams = ref<SupervisorAssistanceRecordInputDTO>({
  pageIndex: 1,
  pageSize: 20,
  start: dayjs().format("YYYY-MM-01 00:00:00"),
  end: dayjs().format("YYYY-MM-DD 23:59:59"),
  states: null,
  keywords: "",
});
const timeRange = ref<[string, string]>([
  dayjs().format("YYYY-MM-01"),
  dayjs().format("YYYY-MM-DD"),
]);

const detailInfo = ref<AssistanceRecordItem>({});

const isPreview = ref<boolean>(false);
provide("isPreview", isPreview);

const stateType = [
  {
    Id: ConsultationStateEnum.Pending,
    Name: "待接受",
  },
  {
    Id: ConsultationStateEnum.Canceled,
    Name: "已取消",
  },
  {
    Id: ConsultationStateEnum.Accepted,
    Name: "已接受",
  },
  {
    Id: ConsultationStateEnum.Completed,
    Name: "已结束",
  },
  {
    Id: ConsultationStateEnum.Rejected,
    Name: "已拒绝",
  },
  {
    Id: ConsultationStateEnum.Expired,
    Name: "已过期",
  },
];

const showDialog = ref<boolean>(false);
const dialogTitle = ref<string>("查看");

const {
  tableLoading,
  pageData,
  total,
  tableRef,
  exportLoading,
  tableDateFormat,
  tableFluidHeight,
  tableResize,
} = useTableConfig<AssistanceRecordItem>();

const handleQuery = () => {
  queryParams.value.pageIndex = 1;
  handleGetTableList();
};

const handlePreview = async (row: AssistanceRecordItem) => {
  detailInfo.value = row;
  showDialog.value = true;
};

const handleExportExcel = async () => {
  const params = {
    ServiceExportCode: "SupervisorAssistanceRecordReport", //后端
    ExportWay: ExportEnum.ServiceInvoke, // O:Redash, 1：后端
    ExecutingParams: queryParams.value,
    FileName: `协诊记录查询-${Date.now()}.xlsx`,
  };
  exportLoading.value = true;
  try {
    await exportExcel(params);
  } finally {
    exportLoading.value = false;
  }
};
const handleGetTableList = async () => {
  tableLoading.value = true;
  const res = await Consult_Api.getSupervisorAssistanceRecord(queryParams.value);
  if (res.Type === 200) {
    pageData.value = res.Data.Data;
    total.value = res.Data.Total;
  }
  tableLoading.value = false;
};

watch(timeRange, (newVal) => {
  queryParams.value.start = dayjs(newVal[0]).format("YYYY-MM-DD 00:00:00");
  queryParams.value.end = dayjs(newVal[1]).format("YYYY-MM-DD 23:59:59");
});

onActivated(() => {
  handleGetTableList();
});
</script>

<style lang="scss" scoped></style>
