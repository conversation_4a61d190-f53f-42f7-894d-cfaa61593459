<template>
  <el-tabs v-model="activeName" type="card">
    <el-tab-pane label="患者病例" name="patientCase">
      <PatientCaseContent />
    </el-tab-pane>
    <el-tab-pane label="协诊记录" name="consultRecord">
      <BaseChat :info="roomMsgInfo" />
    </el-tab-pane>
  </el-tabs>
</template>

<script setup lang="ts">
import { AssistanceRecordItem } from "@/api/consult/types";
import PatientCaseContent from "./PatientCaseContent.vue";
import IM_Api from "@/api/im";
import Passport_Api from "@/api/passport";

const activeName = ref<string>("patientCase");
const roomMsgInfo = ref<unknown>(undefined);

interface Props {
  detailInfo: AssistanceRecordItem;
}
const props = defineProps<Props>();
const onGetRoomMsgInfo = () => {
  if (!props.detailInfo.RoomId) {
    return;
  }
  Promise.all([
    IM_Api.getHistoryMsg({
      UserId: props.detailInfo.ApplyUserId!,
      Project: props.detailInfo.ApplyOrgId!,
      RoomId: props.detailInfo.RoomId,
    }),
    Passport_Api.getUserProfile({
      Ids: [props.detailInfo.ApplyUserId!, props.detailInfo.SupervisorUserId!],
    }),
    IM_Api.getRoom({ roomId: props.detailInfo.RoomId }),
  ]).then((res) => {
    console.log("获取数据成功", res);
    const data = {
      Data: {
        RoomInfo: res[2].Data,
        Msg: res[0].Data,
        Users: res[1].Data,
      },
    };
    roomMsgInfo.value = data;
  });
};

provide("detailInfo", props.detailInfo);
onBeforeMount(() => {
  onGetRoomMsgInfo();
});
</script>

<style lang="scss" scoped></style>
