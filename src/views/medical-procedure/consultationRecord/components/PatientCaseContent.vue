<template>
  <div class="patient-info-container">
    <!-- 主信息卡片：合并了基本信息、病情会诊和会诊信息 -->
    <el-card class="info-card">
      <!-- 患者基本信息区 -->
      <div class="section-block">
        <div class="section-title">
          <i class="el-icon-user" />
          <span>患者基本信息</span>
        </div>
        <el-row :gutter="10">
          <el-col :xs="24" :sm="8">
            <div class="info-item">
              <span class="label">姓名：</span>
              {{ info.PatName }}
            </div>
          </el-col>
          <el-col :xs="24" :sm="8">
            <div class="info-item">
              <span class="label">性别：</span>
              {{ info.Sex }}
            </div>
          </el-col>
          <el-col :xs="24" :sm="8">
            <div class="info-item">
              <span class="label">年龄：</span>
              {{ info.Age }}
            </div>
          </el-col>
        </el-row>
      </div>

      <div class="section-divider" />

      <!-- 病情会诊区 -->
      <div class="section-block">
        <div class="section-title">
          <i class="el-icon-document" />
          <span>病情与会诊</span>
        </div>
        <div class="compact-section">
          <div class="info-row">
            <span class="label">病情简介：</span>
            <span>{{ info.Synopsis || "暂无" }}</span>
          </div>
          <div class="info-row">
            <span class="label">会诊目的：</span>
            <span>{{ info.Interflow || "暂无" }}</span>
          </div>
        </div>
      </div>

      <div class="section-divider" />

      <!-- 会诊信息区 -->
      <div class="section-block">
        <div class="section-title">
          <i class="el-icon-office-building" />
          <span>会诊信息</span>
        </div>
        <el-row :gutter="10">
          <el-col :xs="24" :sm="12">
            <div class="info-item">
              <span class="label">协诊医院：</span>
              {{ info.SupervisorOrgName }}
            </div>
          </el-col>
          <el-col :xs="24" :sm="12">
            <div class="info-item">
              <span class="label">协诊人：</span>
              {{ info.SupervisorUserName }}
            </div>
          </el-col>
          <el-col :xs="24" :sm="12">
            <div class="info-item">
              <span class="label">发起医院：</span>
              {{ info.ApplyOrgName }}
            </div>
          </el-col>
          <el-col :xs="24" :sm="12">
            <div class="info-item">
              <span class="label">发起人：</span>
              {{ info.ApplyUserName }}
            </div>
          </el-col>
          <el-col :xs="24" :sm="12">
            <div class="info-item">
              <span class="label">发起时间：</span>
              {{ info.ApplyTime }}
            </div>
          </el-col>
        </el-row>
      </div>
    </el-card>

    <!-- 意见建议卡片 -->
    <el-card v-if="suggestList.length > 0" class="info-card">
      <div class="section-title card-title">
        <i class="el-icon-chat-line-square" />
        <span>意见或建议</span>
      </div>
      <div v-for="(item, index) in suggestList" :key="item.Id" class="suggest-item">
        <div class="suggest-header">
          <el-avatar size="small" icon="el-icon-user-solid" />
          <span class="suggest-name">{{ item.Name }}</span>
          <el-tag size="small" type="success">意见或建议</el-tag>
        </div>
        <div class="suggest-content">{{ item.Suggest || "暂无意见内容" }}</div>
        <div v-if="index !== suggestList.length - 1" class="section-divider thin" />
      </div>
    </el-card>

    <!-- 无意见或建议时显示 -->
    <el-card v-else class="info-card">
      <div class="section-title card-title">
        <i class="el-icon-chat-line-square" />
        <span>意见或建议</span>
      </div>
      <div class="empty-suggest">
        <i class="el-icon-chat-dot-square" />
        <span>暂无意见或建议</span>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import dayjs from "dayjs";
import { AssistanceRecordItem, SupervisorDocTeamItem } from "@/api/consult/types";
import Consult_Api from "@/api/consult";
const suggestList = ref<SupervisorDocTeamItem[]>([]);
const info = ref<AssistanceRecordItem>({});

const handleProcessingData = (row: AssistanceRecordItem) => {
  row.ApplyTime = dayjs(row.ApplyTime).format("YYYY-MM-DD");
  info.value = row;
  // 获取建议数据
  handleGetSuggest(row.Id!);
};

const handleGetSuggest = async (id: string) => {
  const res = await Consult_Api.getSupervisorAssistanceRecordByConsultId({ id });
  if (res.Type === 200) {
    suggestList.value = res.Data.SupervisorDocTeams || [];
  }
};

const detailInfo = inject<AssistanceRecordItem>("detailInfo");

watch(
  () => detailInfo,
  (newVal) => {
    if (newVal) {
      handleProcessingData(newVal);
    }
  },
  { immediate: true }
);
</script>

<style lang="scss" scoped>
.patient-info-container {
  padding: 5px;
  .info-card {
    margin-bottom: 10px;
    border-radius: 4px;
    box-shadow: 0 1px 6px 0 rgba(0, 0, 0, 0.1);
  }

  .section-block {
    padding: 5px 0;
  }

  .section-title {
    display: flex;
    align-items: center;
    margin-bottom: 5px;
    font-size: 14px;
    font-weight: 600;
    color: #303133;
    i {
      margin-right: 5px;
      font-size: 14px;
    }
  }

  .card-title {
    padding: 0 0 6px 0;
  }

  .section-divider {
    height: 1px;
    margin: 6px 0;
    background-color: #ebeef5;
    &.thin {
      margin: 4px 0;
      background-color: #f2f6fc;
    }
  }

  .info-item {
    margin-bottom: 6px;
    line-height: 20px;
    font-size: 13px;
  }

  .label {
    color: #606266;
    font-weight: 500;
  }

  .compact-section {
    .info-row {
      margin-bottom: 6px;
      line-height: 20px;
      font-size: 13px;
    }
  }

  .suggest-item {
    padding: 2px 0;

    .suggest-header {
      display: flex;
      align-items: center;
      margin-bottom: 4px;

      .suggest-name {
        margin: 0 5px;
        font-weight: 500;
        font-size: 13px;
      }
    }

    .suggest-content {
      padding: 0 0 0 28px;
      line-height: 18px;
      font-size: 13px;
      color: #333;
    }
  }

  .empty-suggest {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 15px 0;
    color: #909399;

    i {
      font-size: 28px;
      margin-bottom: 5px;
    }
    font-size: 13px;
  }
}
</style>
