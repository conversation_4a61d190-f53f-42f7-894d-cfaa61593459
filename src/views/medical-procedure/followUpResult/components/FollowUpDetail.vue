<template>
  <div v-loading="formLoading" class="p-20px overflow-y-auto h-600px">
    <el-form :ref="kFormRef" :model="formData" label-width="80px" :disabled="true">
      <el-row class="flex justify-between">
        <el-col :span="8">
          <el-form-item label-width="100" label="上次随访时间">
            <el-input v-model="formData.StartTime" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="患者编号" prop="PatNo">
            <el-input v-model="formData.PatNo" clearable placeholder="患者编号" />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="8">
          <el-form-item label="姓名" prop="PatName">
            <el-input v-model="formData.PatName" clearable placeholder="姓名" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="性别" prop="Sex">
            <KSelect
              v-model="formData.Sex"
              :data="[
                { label: '男', value: '男' },
                { label: '女', value: '女' },
                { label: '未知', value: '未知' },
              ]"
            />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="年龄" prop="Age">
            <el-input v-model.number="formData.Age" type="number" clearable placeholder="年龄" />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="8">
          <el-form-item label="随访方式" prop="Type">
            <KSelect
              v-model="formData.Type"
              :data="[
                { label: '电话随访', value: 1 },
                { label: '在线随访', value: 2 },
              ]"
            />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="随访时间" prop="StartTime">
            <el-date-picker
              v-model="formData.StartTime"
              type="date"
              placeholder="随访时间"
              :disabled-date="
                (data: Date) => {
                  if (formData.Type === 1) {
                    return data.getTime() > new Date().getTime();
                  }
                  return false;
                }
              "
              :clearable="false"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
            />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="执行人" prop="ExecName">
            <el-input v-model="formData.ExecName" type="text" clearable placeholder="执行人" />
          </el-form-item>
        </el-col>
      </el-row>
      <el-col :span="8">
        <el-form-item label="随访名称" prop="Name">
          <el-input v-model="formData.Name" type="text" clearable placeholder="随访名称" />
        </el-form-item>
      </el-col>
      <el-col :span="16">
        <el-form-item label="备注" prop="Remark">
          <el-input
            v-model="formData.Remark"
            type="textarea"
            :autosize="{ minRows: 3, maxRows: 9 }"
          />
        </el-form-item>
      </el-col>
      <el-col :span="8">
        <el-form-item label="随访问卷" prop="RelatedName">
          <el-input v-model="formData.RelatedName" clearable placeholder="随访问卷" />
        </el-form-item>
      </el-col>
      <template v-for="(problem, index) in formData.GaugeProblems" :key="index">
        <!-- 单选 -->
        <el-form-item
          v-if="problem.ProblemType === 1"
          :key="index"
          label-width="0"
          :prop="`${index}.SelectedId`"
          :rules="[{ required: problem.IsRequired, message: '请选择选项', trigger: 'change' }]"
        >
          <div class="flex flex-col justify-start items-start">
            <el-text class="self-start!">
              {{ index + 1 + "、" + problem.Title }}
              <el-text v-if="problem.IsRequired" style="color: red">*</el-text>
            </el-text>
            <el-radio-group v-model="problem.SelectedId">
              <el-radio
                v-for="option in problem.GaugeProblemDetails"
                :key="option.Id"
                :label="option.ProblemOption"
                :value="option.Id"
              >
                {{ option.ProblemOption }}
              </el-radio>
            </el-radio-group>
          </div>
        </el-form-item>
        <!-- 多选 -->
        <el-form-item
          v-if="problem.ProblemType === 2"
          :key="index"
          label-width="0"
          :prop="`${index}.SelectedIds`"
          :rules="[{ required: problem.IsRequired, message: '请选择选项', trigger: 'change' }]"
        >
          <div class="flex flex-col justify-start items-start">
            <el-text class="self-start!">
              {{ index + 1 + "、" + problem.Title }}
              <el-text v-if="problem.IsRequired" style="color: red">*</el-text>
            </el-text>
            <el-checkbox-group v-model="problem.SelectedIds">
              <el-checkbox
                v-for="option in problem.GaugeProblemDetails"
                :key="option.Id"
                :label="option.ProblemOption"
                :value="option.Id"
              />
            </el-checkbox-group>
          </div>
        </el-form-item>
        <!-- 问答 -->
        <el-form-item
          v-if="problem.ProblemType === 3"
          :key="index"
          label-width="0"
          :prop="`${index}.Answer`"
          :rules="[
            {
              type: 'string',
              required: problem.IsRequired,
              message: '请输入内容',
              trigger: 'blur',
            },
          ]"
        >
          <el-text>
            {{ index + 1 + "、" + problem.Title }}
            <el-text v-if="problem.IsRequired" style="color: red">*</el-text>
          </el-text>
          <el-input
            v-model="problem.Answer"
            type="textarea"
            :autosize="{ minRows: 2, maxRows: 9 }"
          />
        </el-form-item>
        <!-- 填分值 -->
        <el-form-item
          v-if="problem.ProblemType === 4"
          :key="index"
          label-width="0"
          :prop="`${index}.AnswerNumber`"
          :rules="[
            {
              type: 'number',
              required: problem.IsRequired,
              message: '请输入分值',
              trigger: 'blur',
            },
          ]"
        >
          <el-text>
            {{ index + 1 + "、" + problem.Title }}
            <el-text v-if="problem.IsRequired" style="color: red">*</el-text>
          </el-text>
          <el-input v-model.number="problem.AnswerNumber" type="number" />
        </el-form-item>
      </template>
    </el-form>
  </div>
  <!-- 底部按钮 -->
  <div class="flex justify-end">
    <el-button type="primary" @click="emit('cancel')">确定</el-button>
  </div>
</template>

<script setup lang="ts">
import Training_Api from "@/api/training";
import { dayjs } from "element-plus";
import { GaugeProblem } from "@/api/training/types";
import { FollowUpPlan } from "@/api/consult/types";

interface GaugeProblemShow extends GaugeProblem {
  // 选中的单选id
  SelectedId?: string;

  // 选中的多选id
  SelectedIds?: string[];

  // 问答题答案
  Answer?: string;

  // 填分值答案
  AnswerNumber?: number;
}

interface FollowUpPlanShow extends FollowUpPlan {
  GaugeProblems?: GaugeProblemShow[];
}

const kEnableDebug = false;
defineOptions({
  name: "FollowUpDetail",
});

const kFormRef = "ruleFormRef";
const props = defineProps<{
  data: FollowUpPlan;
}>();

const emit = defineEmits<{
  cancel: [];
}>();

const formLoading = ref(false);
// 表单数据
const formData = reactive<FollowUpPlanShow>({});

onMounted(async () => {
  formLoading.value = true;
  const r = await requestBusinessFollowUpDetail();
  Object.assign(formData, props.data);
  formData.StartTime = dayjs(formData.StartTime).format("YYYY-MM-DD");
  formLoading.value = false;

  if (r.Type !== 200) {
    ElMessage.error(r.Content);
  }
});

// 请求填写的随访问卷数据详情
async function requestBusinessFollowUpDetail() {
  const r = await Training_Api.dctGetPatGaugeById({
    patGaugeId: props.data.BusinessId!,
  });
  if (r.Type === 200 && r.Data.length > 0) {
    const gaugeProblems = r.Data[0].PatGaugeProblems?.map((e) => {
      let data: GaugeProblemShow = {
        ...e,
        GaugeProblemDetails: e.PatGaugeProblemDetails,
      };
      if (e.PatGaugeProblemDetails?.length) {
        switch (e.ProblemType) {
          case 1:
            data.SelectedId = e.PatGaugeProblemDetails.find((v) => v.Answer === "1")?.Id;
            break;
          case 2:
            data.SelectedIds = e.PatGaugeProblemDetails.filter((v) => v.Answer === "1" && v.Id).map(
              (v) => v.Id!
            );
            break;
          case 3:
            data.Answer = e.PatGaugeProblemDetails[0].Answer;
            break;
          case 4:
            data.AnswerNumber = Number(e.PatGaugeProblemDetails[0].Answer);
            break;
          default:
            break;
        }
      }

      return data;
    });
    formData.GaugeProblems = gaugeProblems;
  }

  return r;
}
</script>

<style lang="scss" scoped></style>
