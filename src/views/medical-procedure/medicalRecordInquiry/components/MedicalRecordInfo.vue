<template>
  <div class="overflow-y-auto h-500px">
    <el-col>
      <div class="title">就诊时间</div>
      <el-text>{{ consult.VisitDate }}</el-text>
      <div class="title">科别</div>
      <el-text v-if="medical?.Department">{{ medical.Department }}</el-text>
      <div class="title">主诉</div>
      <el-text>{{ medical?.Complain ? medical.Complain : "无" }}</el-text>
      <div class="title">既往史</div>
      <el-text>{{ medical?.HistoryIllness ? medical.HistoryIllness : "无" }}</el-text>
      <div class="title">现病史</div>
      <el-text>{{ medical?.PresentIllness ? medical.PresentIllness : "无" }}</el-text>
      <div class="title">过敏史</div>
      <el-text>{{ medical?.IsAllergy ? medical.AllergyDrug : "无" }}</el-text>
      <div class="title">辅助检查结果</div>
      <el-text>{{ medical?.AuxiliaryDiagnosis ? medical.AuxiliaryDiagnosis : "无" }}</el-text>
      <el-row>
        <el-image
          v-for="(url, index) in imageUrls"
          class="w-100px h-100px mb-10px mx-10px"
          fit="contain"
          :src="url"
          :preview-src-list="imageUrls"
          :initial-index="index"
        />
      </el-row>
      <div class="title">诊断</div>
      <el-text>{{ diagnoses }}</el-text>
      <div class="title">处置</div>
      <el-col v-for="(item, index) in disposal" v-if="disposal">
        <el-text>{{ disposal.length > 1 ? index + 1 + "、" : "" }}{{ item }}</el-text>
      </el-col>
      <el-text v-else>无</el-text>
    </el-col>
  </div>
</template>

<script setup lang="ts">
import { ConsultRecordInfo } from "@/api/consult/types";
import dayjs from "dayjs";

const kEnableDebug = false;
defineOptions({
  name: "MedicalRecordInfo",
});

const props = defineProps<{
  data: ConsultRecordInfo;
}>();

// 主诊断
const diagnoses = computed(() => {
  return props.data.Diagnoses?.find((item) => item.IsMain)?.DiagnoseName ?? "无";
});

const disposal = computed(() => {
  return typeof props.data.Disposal === "string"
    ? props.data.Disposal.split("\n").filter((item) => item)
    : props.data.Disposal;
});

const consult = computed(() => {
  return {
    ...props.data.Consult,
    VisitDate: props.data.Consult?.VisitDate
      ? dayjs(props.data.Consult.VisitDate).format("YYYY-MM-DD")
      : "",
  };
});

const medical = computed(() => props.data.Medical);

const imageUrls = computed(() => {
  return medical.value?.ReportUrls?.filter((url) => url!);
});
</script>

<style lang="scss" scoped>
.title {
  margin: 20px 0 10px;
  font-weight: 600;
}
</style>
