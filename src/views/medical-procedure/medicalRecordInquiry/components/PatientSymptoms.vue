<template>
  <div class="overflow-y-auto h-500px">
    <el-col>
      <div class="title">就诊信息</div>
      <el-row class="mb-5px">
        <el-text class="mr-30px!">就诊号：{{ consult.Id }}</el-text>
        <el-text class="mr-30px!">就诊科室：{{ consult.DepartmentName }}</el-text>
        <el-text class="mr-30px!">就诊医生：{{ consult.DocUserName }}</el-text>
        <el-text v-if="consult.VisitDate" class="mr-30px!">
          就诊日期：{{ consult.VisitDate }}
        </el-text>
      </el-row>
      <el-text>诊断：{{ diagnoses }}</el-text>
      <div class="title">症状描述</div>
      <el-text>{{ consult.Describing }}</el-text>
      <div class="title">线下问诊时间</div>
      <el-text>{{ consult.OfflineDate }}</el-text>
      <div class="title">图片资料</div>
      <el-row>
        <el-image
          v-for="(url, index) in imageUrls"
          class="w-100px h-100px mb-10px mx-10px"
          fit="contain"
          :src="url"
          :preview-src-list="imageUrls"
          :initial-index="index"
        />
      </el-row>
    </el-col>
  </div>
</template>

<script setup lang="ts">
import { ConsultRecordInfo } from "@/api/consult/types";
import dayjs from "dayjs";

const kEnableDebug = false;
defineOptions({
  name: "PatientSymptoms",
});

const props = defineProps<{
  data: ConsultRecordInfo;
}>();

// 主诊断
const diagnoses = computed(() => {
  return props.data.Diagnoses?.find((item) => item.IsMain)?.DiagnoseName ?? "";
});

const consult = computed(() => {
  return {
    ...props.data.Consult,
    VisitDate: props.data.Consult?.VisitDate
      ? dayjs(props.data.Consult.VisitDate).format("YYYY-MM-DD")
      : "",
    OfflineDate: props.data.Consult?.OfflineDate
      ? dayjs(props.data.Consult.OfflineDate).format("YYYY-MM-DD")
      : "",
  };
});

const imageUrls = computed(() => {
  return props.data.Urls?.filter((item) => item.Url).map((item) => item.Url!);
});
</script>

<style lang="scss" scoped>
.title {
  margin: 20px 0 10px;
  font-weight: 600;
}
</style>
