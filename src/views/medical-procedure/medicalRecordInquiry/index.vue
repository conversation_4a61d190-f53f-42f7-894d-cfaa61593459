<template>
  <div class="app-container">
    <BaseTableSearchContainer @size-changed="tableResize">
      <!-- 顶部筛选条件 -->
      <template #search>
        <TBSearchContainer :is-show-toggle="true">
          <template #left>
            <el-form :model="queryParams" label-position="right" :inline="true">
              <el-form-item label="问诊医院">
                <HospitalSelect
                  v-model="queryParams.OrganizationIds"
                  :scopeable="true"
                  multiple
                  collapse-tags
                  clearable
                  filterable
                  @change="
                    () => {
                      queryParams.UserIds = undefined;
                      queryParams.DepartmentIds = undefined;
                    }
                  "
                />
              </el-form-item>
              <el-form-item label="服务类型">
                <KSelect
                  v-model="queryParams.ConsultWays"
                  filterable
                  multiple
                  collapse-tags
                  :data="[
                    { label: '在线问诊', value: 1 },
                    { label: '在线咨询', value: 2 },
                    { label: '护理咨询', value: 3 },
                  ]"
                />
              </el-form-item>
              <el-form-item label="来源">
                <KSelect
                  v-model="queryParams.Source"
                  :data="[
                    { label: '患者发起', value: 1 },
                    { label: '主动开方', value: 2 },
                    { label: '快速开方', value: 3 },
                  ]"
                  :show-all="true"
                />
              </el-form-item>
              <el-form-item label="科室">
                <DeptSelect
                  v-model="queryParams.DepartmentIds"
                  :org-id="queryParams.OrganizationIds?.[0]"
                  :disabled="queryParams.OrganizationIds?.length !== 1"
                  filterable
                  clearable
                  multiple
                  collapse-tags
                />
              </el-form-item>
              <el-form-item label="接诊人">
                <UserSelect
                  v-model="queryParams.UserIds"
                  :org-ids="queryParams.OrganizationIds"
                  :dept-ids="queryParams.DepartmentIds"
                  :role-types="getRoleTypes()"
                  :scopeable="true"
                  multiple
                  collapse-tags
                  filterable
                  clearable
                />
              </el-form-item>
              <el-form-item label="问诊日期">
                <el-date-picker
                  v-model="dateRange"
                  type="daterange"
                  :shortcuts="datePickerShortcuts"
                  range-separator="至"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                  :clearable="false"
                  :disabled-date="handleDisabledDate"
                  value-format="YYYY-MM-DD HH:mm:ss"
                  unlink-panels
                  class="w-300px!"
                />
              </el-form-item>
              <el-form-item label="方案状态">
                <KSelect
                  v-model="queryParams.PreState"
                  :data="[
                    { label: '待审核', value: 0 },
                    { label: '未付费', value: 1 },
                    { label: '已付费', value: 2 },
                    { label: '已失效', value: 3 },
                    { label: '未通过', value: 4 },
                    { label: '已撤销', value: 5 },
                  ]"
                  :show-all="true"
                />
              </el-form-item>
              <el-form-item label="就诊状态">
                <KSelect
                  v-model="queryParams.States"
                  collapse-tags
                  multiple
                  filterable
                  :data="[
                    { label: '待支付', value: 0 },
                    { label: '待接诊', value: 1 },
                    { label: '咨询中', value: 2 },
                    { label: '已完成', value: 3 },
                    { label: '已取消', value: 4 },
                  ]"
                />
              </el-form-item>
              <el-form-item label="是否测试数据">
                <KSelect
                  v-model="queryParams.IsTest"
                  :data="[
                    { label: '否', value: false },
                    { label: '是', value: true },
                  ]"
                  :show-all="true"
                />
              </el-form-item>
              <el-form-item label="关键字">
                <el-input
                  v-model="queryParams.Keyword"
                  prefix-icon="el-icon-Search"
                  placeholder="就诊号/患者姓名"
                  clearable
                  @keyup.enter="handleQuery"
                />
              </el-form-item>
            </el-form>
          </template>
          <template #right>
            <el-button type="primary" icon="search" @click="handleQuery">搜索</el-button>
            <el-button
              type="primary"
              :disabled="!selectedTableIds.length"
              :loading="changeTestDataLoading"
              @click="handleSwitchTestData"
            >
              切换是否测试数据
            </el-button>
            <el-button
              type="primary"
              :disabled="!pageData?.length"
              :loading="exportLoading"
              @click="onExport"
            >
              导出
            </el-button>
          </template>
        </TBSearchContainer>
      </template>
      <!-- 列表 -->
      <template #table>
        <el-table
          :ref="kTableRef"
          v-loading="tableLoading"
          :data="pageData"
          :total="total"
          row-key="Id"
          :height="tableFluidHeight"
          :header-cell-style="{ textAlign: 'center' }"
          :cell-style="{ textAlign: 'center' }"
          border
          highlight-current-row
          @selection-change="(selection) => handleSelectionChange(selection, 'Id')"
        >
          <el-table-column type="selection" fixed="left" reserve-selection width="55" />
          <el-table-column prop="Id" label="就诊号" width="150" show-overflow-tooltip />
          <el-table-column label="服务类型" width="90">
            <template #default="scope">
              {{ ["", "在线问诊", "在线咨询", "护理咨询"][scope.row.ConsultWay] }}
            </template>
          </el-table-column>
          <el-table-column label="来源" width="90">
            <template #default="scope">
              {{ ["", "患者发起", "主动开方", "快速开方"][scope.row.Source] }}
            </template>
          </el-table-column>
          <el-table-column prop="IsTest" label="是否测试数据" width="70">
            <template #default="scope">
              {{ scope.row.IsTest ? "是" : "否" }}
            </template>
          </el-table-column>
          <el-table-column
            prop="CreateDate"
            label="问诊日期"
            :formatter="tableDateFormat"
            width="170"
          />
          <el-table-column prop="OrganizationName" label="医院" width="150" show-overflow-tooltip />
          <el-table-column
            prop="DepartmentName"
            label="问诊科室"
            width="120"
            show-overflow-tooltip
          />
          <el-table-column
            prop="DocUserName"
            label="医生/治疗师"
            width="80"
            show-overflow-tooltip
          />
          <el-table-column prop="TotalAmount" label="问诊费" width="80" show-overflow-tooltip />
          <el-table-column prop="DiagnoseName" label="诊断" width="150" show-overflow-tooltip />
          <el-table-column prop="DocAssistantName" label="医助" width="100" show-overflow-tooltip />
          <el-table-column label="患者信息" min-width="170">
            <template #default="scope">
              <el-text>{{ scope.row.UserName }} {{ scope.row.Sex }} {{ scope.row.Age }}</el-text>
              <br />
              <el-text v-if="scope.row.PhoneNumber">手机号：{{ scope.row.PhoneNumber }}</el-text>
            </template>
          </el-table-column>
          <el-table-column label="方案状态" width="80">
            <template #default="scope">
              {{ ["待审核", "未付费", "已付费", "已失效", "未通过", "已撤销"][scope.row.PreState] }}
            </template>
          </el-table-column>
          <el-table-column label="就诊状态" width="80">
            <template #default="scope">
              {{ ["待支付", "待接诊", "咨询中", "已完成", "已取消"][scope.row.State] }}
            </template>
          </el-table-column>
          <el-table-column fixed="right" label="操作" width="80">
            <template #default="scope">
              <el-button link type="primary" @click="onPreviewDetail(scope.row)">详情</el-button>
            </template>
          </el-table-column>
        </el-table>
      </template>
      <!-- 分页 -->
      <template #pagination>
        <Pagination
          v-if="total > 0"
          v-model:total="total"
          v-model:page="queryParams.pageIndex"
          v-model:limit="queryParams.pageSize"
          @pagination="requestTableList"
        />
      </template>
    </BaseTableSearchContainer>
  </div>

  <!-- 查看详情 -->
  <el-dialog
    v-model="showDataDialog.isShow"
    title="查看详情"
    width="800"
    destroy-on-close
    @close="showDataDialog.isShow = false"
  >
    <MedicalRecordTabs :data="showDataDialog.data" @cancel="showDataDialog.isShow = false" />
  </el-dialog>
</template>

<script setup lang="ts">
import dayjs from "dayjs";
import { useTableConfig } from "@/hooks/useTableConfig";
import { convertUndefinedToNull, exportExcel } from "@/utils/serviceUtils";
import { ExportEnum } from "@/enums/Other";
import { ExportTaskRedashDTO } from "@/api/report/types";
import { ConsultRecord, GetConsultRecordsInputDTO } from "@/api/consult/types";
import Consult_Api from "@/api/consult";
import { useDateRangePicker } from "@/hooks/useDateRangePicker";
import MedicalRecordTabs from "./components/MedicalRecordTabs.vue";

const { datePickerShortcuts } = useDateRangePicker();

interface QueryParams extends GetConsultRecordsInputDTO {}

// 调试开关
const kEnableDebug = false;
defineOptions({
  name: "MedicalRecordInquiry",
});

const {
  kTableRef,
  tableRef,
  pageData,
  tableLoading,
  tableFluidHeight,
  total,
  tableResize,
  handleSelectionChange,
  selectedTableIds,
  exportLoading,
  tableDateFormat,
} = useTableConfig<ConsultRecord>();

// 查询条件
const queryParams = reactive<QueryParams>({
  StartDate: dayjs().startOf("month").format("YYYY-MM-DD HH:mm:ss"),
  EndDate: dayjs().endOf("day").format("YYYY-MM-DD HH:mm:ss"),
  IsTest: false,
  ConsultWays: undefined,
  DepartmentIds: undefined,
  OrganizationIds: undefined,
  UserIds: undefined,
  States: undefined,
  PreState: undefined,
  Source: undefined,
  Keyword: undefined,
  pageIndex: 1,
  pageSize: 20,
});

// 定义 dateRange
const dateRange = computed({
  get() {
    // 从 queryParams 中获取日期范围
    return [queryParams.StartDate, queryParams.EndDate];
  },
  set(newValue) {
    // 当用户选择日期范围时，更新 queryParams
    if (newValue && newValue.length === 2) {
      queryParams.StartDate = newValue[0].split(" ")[0] + " 00:00:00";
      queryParams.EndDate = newValue[1].split(" ")[0] + " 23:59:59";
    }
  },
});

// 切换测试数据请求，等待状态
const changeTestDataLoading = ref(false);

// 查看详情
const showDataDialog = reactive({
  isShow: false,
  data: {} as ConsultRecord,
});

// 禁用日期
function handleDisabledDate(date: Date) {
  return date.getTime() > Date.now();
}

// 获取角色类型
function getRoleTypes() {
  if (queryParams.ConsultWays?.length) {
    const roles = ["", "doctor", "therapist", "nurse"];
    return queryParams.ConsultWays.map((index) => roles[index]);
  }

  return ["doctor", "therapist", "nurse"];
}

// 点击搜索
function handleQuery() {
  queryParams.pageIndex = 1;
  requestTableList();
}

// 点击切换是否测试数据
async function handleSwitchTestData() {
  kEnableDebug && console.debug("切换是否测试数据", selectedTableIds.value);

  changeTestDataLoading.value = true;
  const r0 = await Consult_Api.switchTestStatus(selectedTableIds.value);
  changeTestDataLoading.value = false;
  if (r0.Type !== 200) {
    ElMessage.error(r0.Content);
    return;
  }

  ElNotification.success("切换成功");
  requestTableList();

  // 清空选项
  selectedTableIds.value = [];
  tableRef.value?.clearSelection();
}

// 点击导出
async function onExport() {
  kEnableDebug && console.debug("点击导出");

  const params: ExportTaskRedashDTO = {
    ExecutingParams: convertUndefinedToNull(queryParams),
    ExportWay: ExportEnum.ServiceInvoke,
    FileName: `就诊记录查询-${Date.now()}.xlsx`,
    ServiceExportCode: "ConsultRecordReport",
  };
  exportLoading.value = true;
  try {
    await exportExcel(params);
  } catch (error) {
    ElNotification.error("导出失败");
  } finally {
    exportLoading.value = false;
  }
}

// 点击查看
function onPreviewDetail(row: ConsultRecord) {
  kEnableDebug && console.debug("查看", row);

  showDataDialog.data = row;
  showDataDialog.isShow = true;
}

// 请求列表数据
async function requestTableList() {
  tableLoading.value = true;
  const r = await Consult_Api.getConsultRecords(queryParams);
  tableLoading.value = false;
  if (r.Type !== 200) {
    ElMessage.error(r.Content);
    return;
  }

  // 请求成功
  pageData.value = r.Data.Rows;
  total.value = r.Data.Total;
}

onActivated(() => {
  requestTableList();
});
</script>

<style lang="scss" scoped></style>
