<template>
  <div class="a4-page">
    <div id="pdfDom" class="container">
      <div class="container-title">{{ patEvaluateInfo.OrgName }}</div>
      <div class="container-evaTitle">{{ patEvaluateInfo.Name }}</div>
      <div class="container-info">
        <div class="flex justify-between">
          <FormItem :label="'姓名：' + (patEvaluateInfo.PatName || '')" style="font-size: 18px" />
          <FormItem :label="'性别：' + (patEvaluateInfo.Sex || '')" style="font-size: 18px" />
          <FormItem :label="'年龄：' + (patEvaluateInfo.Age || 0)" style="font-size: 18px" />
          <FormItem :label="'科室：' + (patEvaluateInfo.DeptName || '')" style="font-size: 18px" />
        </div>
        <FormItem
          style="font-size: 18px"
          :label="'评估时间：' + (patEvaluateInfo.CreatedTime || '')"
        />
      </div>
      <div class="container-table">
        <table>
          <thead>
            <tr>
              <th class="w-2/5">题目</th>
              <th class="w-2/5">选项/回答</th>
              <th class="w-1/5">得分</th>
            </tr>
          </thead>
          <tbody>
            <tr v-for="item in patEvaluateInfo.PatGaugeProblems" :key="item.Title">
              <td>{{ item.Title }}</td>
              <td>
                <div v-if="item.ProblemType === 1" class="container-table-radio">
                  <span
                    v-for="(o, index) in item.PatGaugeProblemDetails"
                    :key="o.Id"
                    :class="
                      index === (item.PatGaugeProblemDetails || []).length - 1 ? '' : 'mb-2.5'
                    "
                  >
                    <el-icon v-if="o.Answer === '1'" class="el-icon-check text-green-600">
                      <Select />
                    </el-icon>
                    <el-icon v-else class="el-icon-check visibility-hidden">
                      <Select />
                    </el-icon>
                    <span class="ml-3">{{ o.Points || 0 }}分 {{ o.ProblemOption }}</span>
                  </span>
                </div>
                <div v-if="item.ProblemType === 2" class="container-table-radio">
                  <span
                    v-for="(o, index) in item.PatGaugeProblemDetails"
                    :key="o.Id"
                    :class="
                      index === (item.PatGaugeProblemDetails || []).length - 1 ? '' : 'mb-2.5'
                    "
                  >
                    <el-icon v-if="o.Answer === '1'" class="el-icon-check text-green-600">
                      <Select />
                    </el-icon>
                    <el-icon v-else class="el-icon-check visibility-hidden">
                      <Select />
                    </el-icon>
                    <span class="ml-3">{{ o.Points || 0 }}分 {{ o.ProblemOption }}</span>
                  </span>
                </div>
                <div v-if="item.ProblemType === 3">
                  <div class="text-left">
                    {{ (item.PatGaugeProblemDetails || [])[0].Answer }}
                  </div>
                </div>
                <div v-if="item.ProblemType === 4">
                  <div class="text-left">
                    {{ (item.PatGaugeProblemDetails || [])[0].Answer }}
                  </div>
                </div>
              </td>
              <td>{{ onGetSumPoint(item) }}</td>
            </tr>
            <tr>
              <td colspan="3">
                <div>量表总分：{{ patEvaluateInfo.SumPoint || 0 }}分</div>
                <div>结论：{{ patEvaluateInfo.SuccessResultContent || "暂无" }}</div>
              </td>
            </tr>
            <tr>
              <td colspan="3">
                <template v-for="(item, index) in patEvaluateInfo.PatGaugeResults" :key="index">
                  <div>
                    {{ item.StartPoint + "-" + item.EndPoint + "分：" + item.Content }}
                  </div>
                </template>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
    <div class="text-right">
      <el-button :loading="loading" size="small" type="primary" @click="handleDownPDF">
        下载 pdf
      </el-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import dayjs from "dayjs";
import html2canvas from "html2canvas";
import jsPDF from "jspdf";
import Training_Api from "@/api/training";
import { GaugeProblem, PatEvaluateItem } from "@/api/training/types";

const patEvaluateInfo = ref<PatEvaluateItem>({});

const loading = ref<boolean>(false);
const handleGetEvaluateData = async (id: string) => {
  const res = await Training_Api.getPatEvaluateById({
    patEvaluateGaugeId: id,
  });
  if (res.Type === 200) {
    res.Data.CreatedTime = dayjs(res.Data.CreatedTime).format("YYYY-MM-DD HH:mm:ss");
    patEvaluateInfo.value = res.Data;
  }
};

// 计算单选题分数
const calculateSingleChoice = (details: any[]): number | null => {
  const selectList = details.filter((s) => s.Answer === "1");
  return selectList.length ? selectList[0].Points! : null;
};

// 计算多选题分数
const calculateMultipleChoice = (details: any[]): number => {
  return details.reduce((o, current) => {
    return o + (current.Answer === "1" ? current.Points! : 0);
  }, 0);
};

// 计算数值题分数
const calculateNumericAnswer = (details: any[]): number | null => {
  return Number(details?.[0]?.Answer) || null;
};

const onGetSumPoint = (item: GaugeProblem): number | null => {
  const details = item.PatGaugeProblemDetails || [];

  if (item.ProblemType === 1) {
    // 单选题
    return calculateSingleChoice(details);
  }
  if (item.ProblemType === 2) {
    // 多选题
    return calculateMultipleChoice(details);
  }
  if (item.ProblemType === 3) {
    // 问答题
    return null;
  }
  if (item.ProblemType === 4) {
    // 数值题
    return calculateNumericAnswer(details);
  }

  return null;
};

const handleDownPDF = () => {
  loading.value = true;
  try {
    const targetDom = document.querySelector("#pdfDom"); // 这个dom元素是要导出pdf的内容区域
    console.log(window.devicePixelRatio);

    html2canvas(targetDom as HTMLElement, {
      allowTaint: true,
      useCORS: true, // 如果说所生成的页面中带有跨域的图片，这个useCors需要设置为True 否则画布被污染不会显示
      backgroundColor: "#fff",
    }).then((canvas) => {
      // canvas 为内容区域，默认横向 padding: 20mm;

      const { width: cWidth, height: cHeight } = canvas;
      // A4大小，210mm x 297mm，保留左右20mm边距 上下20mm的边距，显示区域170x257
      const A4ContentWidth = 210 - 40,
        A4ContentHeight = 297 - 40;
      // 按A4显示比例换算一页图像的像素高度
      const onePageContentHeight = Math.floor(A4ContentHeight * (cWidth / A4ContentWidth));
      const pdf = new jsPDF("p", "mm", "a4"); // A4纸，纵向
      const ctx = canvas.getContext("2d");

      // 已生成pdf的html页面高度
      let renderedHeight = 0;

      while (renderedHeight < cHeight) {
        const currentImageHeight = Math.min(onePageContentHeight, cHeight - renderedHeight); //可能内容不足一页
        const imageData = ctx?.getImageData(0, renderedHeight, cWidth, currentImageHeight);

        pdf.addImage(
          imageData!,
          "PNG",
          20,
          20,
          A4ContentWidth,
          Math.floor(currentImageHeight * (A4ContentWidth / cWidth))
        ); // 添加图像到页面，保留左右20mm边距 上下20mm
        renderedHeight += currentImageHeight;
        if (renderedHeight < canvas.height) pdf.addPage(); //如果后面还有内容，添加一个空页
      }
      const today = new Date().toLocaleDateString().replace(/\//g, "-");
      // 本地下载
      pdf.save(
        `${patEvaluateInfo.value.PatName}的${patEvaluateInfo.value.Name}评估方案 ${today}.pdf`,
        { returnPromise: true }
      );
    });
  } catch (error) {
    ElMessage.warning("下载 pdf 失败，请联系管理员!");
  } finally {
    loading.value = false;
  }
};

interface Props {
  patEvaluateGaugeId: string;
}
const props = defineProps<Props>();

watch(
  () => props.patEvaluateGaugeId,
  (newVal) => {
    if (newVal) {
      handleGetEvaluateData(newVal);
    }
  },
  { immediate: true }
);
</script>

<style lang="scss" scoped>
.a4-page {
  width: 210mm;
  padding: 20mm;
  margin: 0 auto;
}

.container {
  width: 170mm;
  color: black;

  &-title {
    margin-bottom: 24px;
    font-size: 40px;
    font-weight: 800;
    text-align: center;
  }

  &-evaTitle {
    margin-bottom: 24px;
    font-size: 30px;
    font-weight: 700;
    text-align: center;
  }

  &-info {
    margin-bottom: 24px;

    :deep(.form-item__label) {
      color: black;
    }
  }

  &-table {
    &-radio {
      display: flex;
      flex-direction: column;
      align-items: flex-start;
      justify-content: center;
    }
  }
}

table {
  width: 100%;
  border-collapse: collapse;
}

th,
td {
  padding: 8px;
  font-size: 14px;
  border: 1px solid #999;
}

.text-right {
  position: absolute;
  top: 60px;
  right: 10px;
}

:deep(.el-checkbox__label) {
  color: #606266 !important;
}

.visibility-hidden {
  visibility: hidden;
}
</style>
