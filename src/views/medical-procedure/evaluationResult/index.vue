<template>
  <div class="app-container">
    <BaseTableSearchContainer @size-changed="tableResize">
      <template #search>
        <TBSearchContainer :is-show-toggle="true">
          <template #left>
            <el-form :model="queryParams" label-position="right" :inline="true">
              <el-form-item label="时间">
                <el-date-picker
                  v-model="timeRange"
                  unlink-panels
                  type="daterange"
                  range-separator="至"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                  format="YYYY-MM-DD"
                  :clearable="false"
                  value-format="YYYY-MM-DD"
                  style="width: 250px"
                  :shortcuts="datePickerShortcuts"
                />
              </el-form-item>
              <el-form-item label="机构">
                <HospitalSelect v-model="queryParams.OrgId" />
              </el-form-item>
              <el-form-item label="关键字" prop="Keyword">
                <el-input
                  v-model="queryParams.Keyword"
                  placeholder="姓名/电话号码"
                  @keyup.enter="handleQuery"
                />
              </el-form-item>
            </el-form>
          </template>
          <template #right>
            <el-button type="primary" icon="search" @click="handleQuery">搜索</el-button>
          </template>
        </TBSearchContainer>
      </template>
      <template #table>
        <el-table
          ref="tableRef"
          v-loading="tableLoading"
          :data="pageData"
          :total="total"
          border
          row-key="Id"
          :height="tableFluidHeight"
          highlight-current-row
          style="text-align: center; flex: 1"
        >
          <el-table-column prop="Name" width="200" label="量表名称" align="center" />
          <el-table-column label="患者信息" prop="PatientInfo" width="200" align="center" />
          <el-table-column label="医院" prop="OrgName" width="200" align="center" />
          <el-table-column label="科室" width="80" prop="DeptName" align="center" />
          <el-table-column label="得分" prop="SumPoint" width="80" align="center" />
          <el-table-column label="结论" prop="SuccessResultContent" align="center" />
          <el-table-column label="评估时间" width="200" prop="CreatedTime" align="center" />
          <el-table-column fixed="right" label="操作" align="center" width="100">
            <template #default="scope">
              <el-button link type="primary" @click="handlePreview(scope.row)">查看详情</el-button>
            </template>
          </el-table-column>
        </el-table>
      </template>
      <template #pagination>
        <Pagination
          v-if="total > 0"
          v-model:total="total"
          v-model:page="queryParams.PageIndex"
          v-model:limit="queryParams.PageSize"
          @pagination="handleGetTableList"
        />
      </template>
    </BaseTableSearchContainer>
    <el-dialog
      v-model="showDialog"
      :title="dialogTitle"
      width="1200"
      destroy-on-close
      :close-on-press-escape="isPreview"
      :close-on-click-modal="isPreview"
    >
      <EvaluateContent :pat-evaluate-gauge-id="patEvaluateGaugeId" />
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="showDialog = false">取消</el-button>
          <el-button v-if="!isPreview" type="primary" :loading="dialogConfirmLoading">
            确定
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import dayjs from "dayjs";
import { useTableConfig } from "@/hooks/useTableConfig";
import { FilledPatGaugeInputDTO, FilledPatGaugeItem } from "@/api/training/types";
import Training_Api from "@/api/training";
import { useDateRangePicker } from "@/hooks/useDateRangePicker";
import EvaluateContent from "./components/EvaluateContent.vue";

const { datePickerShortcuts } = useDateRangePicker();

defineOptions({
  name: "EvaluationResult",
});

const queryParams = ref<FilledPatGaugeInputDTO>({
  StartDate: dayjs().format("YYYY-MM-01 00:00:00"),
  EndDate: dayjs().format("YYYY-MM-DD 23:59:59"),
  OrgId: null,
  Keyword: "",
  PageIndex: 1,
  PageSize: 20,
});
const timeRange = ref<[string, string]>([
  dayjs().format("YYYY-MM-01"),
  dayjs().format("YYYY-MM-DD"),
]);

const isPreview = ref<boolean>(false);
provide("isPreview", isPreview);

const showDialog = ref<boolean>(false);
const dialogTitle = ref<string>("查看");
const patEvaluateGaugeId = ref<string>("");

const {
  tableLoading,
  pageData,
  total,
  tableRef,
  tableFluidHeight,
  tableResize,
  dialogConfirmLoading,
} = useTableConfig<FilledPatGaugeItem>();

const handleQuery = () => {
  queryParams.value.PageIndex = 1;
  handleGetTableList();
};

const handlePreview = async (row: FilledPatGaugeItem) => {
  isPreview.value = true;
  patEvaluateGaugeId.value = row.Id!;
  showDialog.value = true;
};
const handleGetTableList = async () => {
  tableLoading.value = true;
  const res = await Training_Api.getFilledPatGauge(queryParams.value);
  if (res.Type === 200) {
    pageData.value = res.Data.Data;
    total.value = res.Data.Total;
  }
  tableLoading.value = false;
};

watch(timeRange, (newVal) => {
  queryParams.value.StartDate = dayjs(newVal[0]).format("YYYY-MM-DD 00:00:00");
  queryParams.value.EndDate = dayjs(newVal[1]).format("YYYY-MM-DD 23:59:59");
});

onActivated(() => {
  handleGetTableList();
});
</script>

<style lang="scss" scoped></style>
