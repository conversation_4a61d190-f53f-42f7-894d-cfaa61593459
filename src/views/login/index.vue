<template>
  <div class="login">
    <!-- 登录页头部 -->
    <div class="login-header">
      <div class="flex-y-center">
        <el-switch
          v-model="isDark"
          inline-prompt
          active-icon="Moon"
          inactive-icon="Sunny"
          @change="toggleTheme"
        />
        <lang-select class="ml-2 cursor-pointer" />
      </div>
    </div>

    <!-- 登录页内容 -->
    <div class="login-form">
      <el-form ref="loginFormRef" :model="loginFormData" :rules="loginRules">
        <div class="form-title">
          <h2>平台运营端</h2>
        </div>

        <!-- 用户名 -->
        <el-form-item prop="username">
          <div class="input-wrapper">
            <el-icon class="mx-2">
              <User />
            </el-icon>
            <el-input
              ref="username"
              v-model="loginFormData.username"
              :placeholder="$t('login.username')"
              name="username"
              size="large"
              class="h-[48px]"
            />
          </div>
        </el-form-item>

        <!-- 密码 -->
        <el-tooltip :visible="isCapslock" :content="$t('login.capsLock')" placement="right">
          <el-form-item prop="password">
            <div class="input-wrapper">
              <el-icon class="mx-2">
                <Lock />
              </el-icon>
              <el-input
                v-model="loginFormData.password"
                :placeholder="$t('login.password')"
                type="password"
                name="password"
                size="large"
                class="h-[48px] pr-2"
                show-password
                @keyup="checkCapslock"
                @keyup.enter="handleLoginSubmit"
              />
            </div>
          </el-form-item>
        </el-tooltip>

        <div class="flex-x-between w-full py-1">
          <el-checkbox v-model="isRememberMe">
            {{ $t("login.rememberMe") }}
          </el-checkbox>

          <el-link type="primary" href="/forget-password">
            {{ $t("login.forgetPassword") }}
          </el-link>
        </div>

        <!-- 滑块验证 -->
        <VerifySlider v-if="needVerifySlider" class="my-4" @verified="handleSliderVerified" />

        <!-- 登录按钮 -->
        <el-button
          :loading="loading"
          :disabled="isLoginButtonDisabled"
          type="primary"
          size="large"
          class="w-full"
          @click.prevent="handleLoginSubmit"
        >
          {{ $t("login.login") }}
        </el-button>
      </el-form>
    </div>
  </div>
</template>

<script setup lang="ts">
import { type RouteRecordRaw } from "vue-router";
import { useI18n } from "vue-i18n";

import { type LoginFormData } from "@/api/passport/types";
import router from "@/router";

import type { FormInstance } from "element-plus";

import { ThemeEnum } from "@/enums/ThemeEnum";

import {
  usePermissionStoreHook,
  useUserStore,
  useSettingsStore,
  useProtectionStoreHook,
  useAppStore,
} from "@/store";
import { kDebug } from "@/utils";
import VerifySlider from "@/components/verify-slider/index.vue";
import { useBreakpoints, breakpointsTailwind } from "@vueuse/core";

const userStore = useUserStore();
const settingsStore = useSettingsStore();
const isRememberMe = ref<boolean>(true);

const { t } = useI18n();
const loginFormRef = ref<FormInstance>();

const isDark = ref(settingsStore.theme === ThemeEnum.DARK); // 是否暗黑模式
const loading = ref(false); // 按钮 loading 状态
const isCapslock = ref(false); // 是否大写锁定
const isSliderVerified = ref(false); // 滑块是否验证通过

const breakpoints = useBreakpoints(breakpointsTailwind);
const isPC = breakpoints.greaterOrEqual("lg");
const appStore = useAppStore();
/**
 * 是否需要滑块验证
 */
const needVerifySlider = computed(() => {
  return isPC.value && appStore.dynamicConfig.protectionEnable;
});

const loginFormData = ref<LoginFormData>({
  username: kDebug ? "kfx" : "",
  password: kDebug ? "hw123456" : "",
  grant_type: "password",
});

const loginRules = computed(() => {
  return {
    username: [
      {
        required: true,
        trigger: "blur",
        message: t("login.message.username.required"),
      },
    ],
    password: [
      {
        required: true,
        trigger: "blur",
        message: t("login.message.password.required"),
      },
      {
        min: 6,
        message: t("login.message.password.min"),
        trigger: "blur",
      },
    ],
  };
});

const isLoginButtonDisabled = computed(() => {
  if (loading.value) {
    return true; // 如果正在加载，始终禁用
  }
  if (needVerifySlider.value) {
    return !isSliderVerified.value;
  }
  // 用户名和密码不能为空
  return !loginFormData.value.username || !loginFormData.value.password;
});

/**
 * 查找第一个有效的可跳转路由
 *
 * @param routes 路由列表
 * @returns 第一个有效的路由路径，如果找不到则返回根路径
 */
function findFirstValidRoutePath(routes: RouteRecordRaw[], basePath = "/"): string {
  for (const route of routes) {
    if (route.meta?.hidden) {
      continue;
    }

    const currentPath = route.path.startsWith("/")
      ? route.path
      : `${basePath}/${route.path}`.replace(/\/+/g, "/");

    if (route.children && route.children.length > 0) {
      const childPath = findFirstValidRoutePath(route.children, currentPath);
      if (childPath && childPath !== "/") {
        return childPath;
      }
    } else {
      return currentPath;
    }
  }
  return "/";
}

// 登录
async function handleLoginSubmit() {
  loginFormRef.value?.validate((valid: boolean) => {
    if (valid) {
      loading.value = true;
      userStore
        .getUserToken(loginFormData.value.username, loginFormData.value.password)
        .then(async () => {
          // 获取用户信息
          await userStore.getUserInfo();

          // 检测密码强度
          useProtectionStoreHook().setPassword(loginFormData.value.password);

          // 获取权限路由
          const permissionStore = usePermissionStoreHook();
          const accessRoutes = await permissionStore.generateRoutes();
          accessRoutes.forEach((route) => router.addRoute(route));

          // 跳转到第一个有效路由
          const firstValidRoutePath = findFirstValidRoutePath(accessRoutes);
          router.push(firstValidRoutePath);
        })
        .finally(() => {
          loading.value = false;
        });
    }
  });
}

// 滑块验证成功处理
function handleSliderVerified(status: boolean) {
  isSliderVerified.value = status;
}

// 主题切换
const toggleTheme = () => {
  const newTheme = settingsStore.theme === ThemeEnum.DARK ? ThemeEnum.LIGHT : ThemeEnum.DARK;
  settingsStore.changeTheme(newTheme);
};

// 检查输入大小写
function checkCapslock(event: KeyboardEvent) {
  // 防止浏览器密码自动填充时报错
  if (event instanceof KeyboardEvent) {
    isCapslock.value = event.getModifierState("CapsLock");
  }
}
</script>

<style lang="scss" scoped>
.login {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  padding: 20px;
  overflow-y: auto;
  background: url("@/assets/images/login-bg.jpg") no-repeat center right;

  .login-header {
    position: absolute;
    top: 0;
    display: flex;
    justify-content: right;
    width: 100%;
    padding: 15px;

    .logo {
      width: 26px;
      height: 26px;
    }

    .title {
      margin: auto 5px;
      font-size: 24px;
      font-weight: bold;
      color: #3b82f6;
    }
  }

  .login-form {
    display: flex;
    flex-direction: column;
    justify-content: center;
    width: 460px;
    padding: 40px;
    overflow: hidden;
    background-color: #fff;
    border-radius: 5px;
    box-shadow: var(--el-box-shadow-light);

    @media (width <= 460px) {
      width: 100%;
      padding: 20px;
    }

    .form-title {
      position: relative;
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 0 0 20px;
      text-align: center;
    }

    .input-wrapper {
      display: flex;
      align-items: center;
      width: 100%;
    }

    .captcha-img {
      height: 48px;
      cursor: pointer;
      border-top-right-radius: 6px;
      border-bottom-right-radius: 6px;
    }

    .third-party-login {
      display: flex;
      justify-content: center;
      width: 100%;
      color: var(--el-text-color-secondary);

      *:not(:first-child) {
        margin-left: 20px;
      }

      .icon {
        cursor: pointer;
      }
    }
  }

  .login-footer {
    position: fixed;
    bottom: 0;
    width: 100%;
    padding: 10px 0;
    text-align: center;
  }
}

:deep(.el-form-item) {
  background: var(--el-input-bg-color);
  border: 1px solid var(--el-border-color);
  border-radius: 5px;
}

:deep(.el-input) {
  .el-input__wrapper {
    padding: 0;
    background-color: transparent;
    box-shadow: none;

    &.is-focus,
    &:hover {
      box-shadow: none !important;
    }

    input:-webkit-autofill {
      /* 通过延时渲染背景色变相去除背景颜色 */
      transition: background-color 1000s ease-in-out 0s;
    }
  }
}

html.dark {
  .login {
    background: url("@/assets/images/login-bg-dark.jpg") no-repeat center right;

    .login-form {
      background: transparent;
      box-shadow: var(--el-box-shadow);
    }
  }
}
</style>
