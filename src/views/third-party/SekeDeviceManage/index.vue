<template>
  <div class="app-container">
    <BaseTableSearchContainer @size-changed="tableResize">
      <template #search>
        <TBSearchContainer>
          <template #left>
            <el-form :model="queryParams" label-position="right" :inline="true">
              <el-form-item label="关键字" prop="keywords">
                <el-input
                  v-model="queryParams.keywords"
                  placeholder="sn码"
                  class="120px!"
                  @keyup.enter="handleQuery"
                />
              </el-form-item>
            </el-form>
          </template>
          <template #right>
            <el-button type="primary" icon="search" @click="handleQuery">搜索</el-button>
            <el-button type="primary" @click="handleBatchImport">批量导入</el-button>
            <el-button type="primary" @click="handleAddClick">添加</el-button>
          </template>
        </TBSearchContainer>
      </template>
      <template #table>
        <el-table
          ref="tableRef"
          v-loading="tableLoading"
          :data="pageData"
          :total="total"
          border
          row-key="Id"
          :height="tableFluidHeight"
          highlight-current-row
          style="flex: 1; text-align: center"
        >
          <el-table-column label="设备SN码" align="center">
            <template #default="scope">
              {{ handleGetSnCode(scope.row) }}
            </template>
          </el-table-column>
        </el-table>
      </template>
      <template #pagination>
        <Pagination
          v-if="total > 0"
          v-model:total="total"
          v-model:page="queryParams.pageIndex"
          v-model:limit="queryParams.pageSize"
          @pagination="handleGetTableList"
        />
      </template>
    </BaseTableSearchContainer>

    <!-- 添加设备SN码对话框 -->
    <el-dialog
      v-model="dialogVisible"
      title="添加设备SN码"
      width="500px"
      destroy-on-close
      :close-on-click-modal="false"
      :close-on-press-escape="false"
    >
      <el-form>
        <el-form-item label="设备SN码">
          <el-input
            v-model="snCodeText"
            type="textarea"
            :rows="10"
            placeholder="请输入设备SN码，每行一个"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleConfirm">确定</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 批量导入对话框 -->
    <el-dialog
      v-model="batchImportDialogVisible"
      title="批量导入"
      width="500px"
      destroy-on-close
      :close-on-click-modal="false"
      :close-on-press-escape="false"
    >
      <!-- 这里可以添加批量导入的具体内容 -->
      <div>
        <el-button type="primary" @click="handleDownTemplate">下载模板</el-button>
        <span style="color: red">请上传xlsx 或者 xls 后缀文件 只支持单个文件上传</span>
        <div style="height: 10px" />
        <FileUpload v-model="batchImportFile" accept=".xlsx,.xls" />
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="batchImportDialogVisible = false">取消</el-button>
          <el-button
            type="primary"
            :loading="dialogConfirmLoading"
            @click="handleBatchImportConfirm"
          >
            确定
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import Supplier_Xeek_Api from "@/api/supplier-xeek";
import { SnCodeItem } from "@/api/supplier-xeek/types";
import { useTableConfig } from "@/hooks/useTableConfig";
import { useUserStore } from "@/store";
const userStore = useUserStore();

defineOptions({
  name: "SekeDeviceManage",
});

const queryParams = ref<any>({
  pageIndex: 1,
  pageSize: 20,
  keywords: "",
});

const batchImportFile = ref<string[]>([]);

// 对话框相关状态
const dialogVisible = ref<boolean>(false);
const snCodeText = ref<string>("");
const batchImportDialogVisible = ref<boolean>(false);

const {
  tableLoading,
  pageData,
  total,
  tableRef,
  tableFluidHeight,
  tableResize,
  dialogConfirmLoading,
} = useTableConfig<SnCodeItem>();

const handleQuery = () => {
  queryParams.value.PageIndex = 1;
  handleGetTableList();
};
const handleGetSnCode = (row: SnCodeItem) => {
  return row.RequestData?.sn_list || "";
};
const handleGetTableList = async () => {
  tableLoading.value = true;
  const res = await Supplier_Xeek_Api.getSendSnCodeRecord(queryParams.value);
  if (res.Type === 200) {
    pageData.value = res.Data.Data;
    total.value = res.Data.Total;
  }
  tableLoading.value = false;
};
const handleAddClick = () => {
  dialogVisible.value = true;
  snCodeText.value = "";
};
const handleBatchImport = () => {
  batchImportDialogVisible.value = true;
};
const handleBatchImportConfirm = () => {
  if (!batchImportFile.value.length) {
    ElMessage.warning("请选择文件或者等待文件上传完成");
    return;
  }
  dialogConfirmLoading.value = true;
  Supplier_Xeek_Api.fileSendSnCode({
    Url: batchImportFile.value[0],
  })
    .then((res) => {
      if (res.Type === 200) {
        ElMessage.success(res.Content);
        batchImportFile.value = [];
        handleGetTableList();
        batchImportDialogVisible.value = false;
      }
    })
    .finally(() => {
      dialogConfirmLoading.value = false;
    });
};

// 确定按钮处理
const handleConfirm = () => {
  if (!snCodeText.value.trim()) {
    ElMessage.warning("请输入设备SN码");
    return;
  }

  // 按换行分割SN码
  const snCodes = snCodeText.value
    .split("\n")
    .map((code) => code.trim())
    .filter((code) => code.length > 0);

  console.log("分割后的SN码列表:", snCodes);

  dialogConfirmLoading.value = true;
  Supplier_Xeek_Api.sendSnCode({
    CreatorId: userStore.userInfo.Id,
    CreatorName: userStore.userInfo.Name,
    SnCodes: snCodes,
  })
    .then((res) => {
      if (res.Type === 200) {
        ElMessage.success(res.Content);
        handleGetTableList();
        dialogVisible.value = false;
      }
    })
    .finally(() => {
      dialogConfirmLoading.value = false;
    });
};

const handleDownTemplate = () => {
  const fileUrl = "/snmb.xlsx";
  const link = document.createElement("a");
  link.href = fileUrl;
  link.download = "SN.xlsx"; // 您可以自定义下载文件的名称
  link.click();
};

onActivated(() => {
  handleGetTableList();
});
</script>

<style lang="scss" scoped></style>
