<template>
  <div class="p-20px overflow-y-auto flex flex-col items-stretch">
    <div class="flex justify-between ml-48px">
      <el-text class="flex-1 text-center">{{ title }}</el-text>
      <el-button
        v-loading="exportLoading"
        type="primary"
        :disabled="pageData.length === 0"
        @click="onExport"
      >
        导出
      </el-button>
    </div>
    <el-table
      :ref="kTableRef"
      v-loading="tableLoading"
      class="my-20px"
      :data="pageData"
      :total="total"
      row-key="OrderNo"
      :header-cell-style="{ textAlign: 'center' }"
      :cell-style="{ textAlign: 'center' }"
      height="500"
      border
      highlight-current-row
      :row-style="tableRowStyle"
    >
      <el-table-column prop="Mode" label="类型" width="80" />
      <el-table-column prop="OrderType" label="订单类型" width="80" />
      <el-table-column prop="ConsultId" label="就诊号" min-width="150" show-overflow-tooltip />
      <el-table-column prop="OrderNo" label="订单号" min-width="150" show-overflow-tooltip />
      <el-table-column prop="CreatedTime" label="时间" min-width="120" show-overflow-tooltip />
      <el-table-column prop="TotalAmount" label="订单金额" width="80" />
      <el-table-column prop="SettleAmount" label="结算金额" width="80" />
      <el-table-column prop="SettleRatio" label="结算比例" width="80" />
      <el-table-column prop="SettleType" label="说明" min-width="120" show-overflow-tooltip />
    </el-table>
    <Pagination
      v-if="total > 0"
      v-model:total="total"
      v-model:page="queryParams.pageIndex"
      v-model:limit="queryParams.pageSize"
      @pagination="refreshTableData"
    />
  </div>
  <!-- 底部按钮 -->
  <div class="flex justify-end">
    <el-button type="primary" @click="emit('cancel')">确定</el-button>
  </div>
</template>

<script setup lang="ts">
import { useTableConfig } from "@/hooks/useTableConfig";
import {
  DoctorSettlementDetailRedash,
  DoctorSettlementDetailsInputDTO,
  DoctorSettlementRedash,
  DoctorSettlementStatisticsRedash,
  ExportTaskRedashDTO,
} from "@/api/report/types";
import { convertToRedashParams, exportExcel, getExportCols } from "@/utils/serviceUtils";
import Report_Api from "@/api/report";
import { ExportEnum } from "@/enums/Other";

const { kTableRef, tableRef, pageData, tableLoading, total, exportLoading } =
  useTableConfig<DoctorSettlementDetailRedash>();

const kEnableDebug = false;
const props = defineProps<{
  data: DoctorSettlementRedash;
  startTime: string;
  endTime: string;
}>();

const emit = defineEmits<{
  cancel: [];
}>();

// 查询结果ID
let queryResultId = -1;

// 查询条件
const queryParams = reactive<RedashParameters<DoctorSettlementDetailsInputDTO>>({
  UserId: props.data.DoctorId!,
  StartTimeDt: props.startTime,
  EndTimeDt: props.endTime,
  pageIndex: 1,
  pageSize: 20,
});

// 统计标题
const title = ref<string>("");

onMounted(async () => {
  tableLoading.value = true;
  title.value = `${props.data.OrgName}/${props.data.DeptName}/${props.data.Name}-${props.startTime}-${props.endTime}`;
  const rs = await Promise.all([requestTableList(), requestStatistics()]);
  tableLoading.value = false;
  const fail = rs.find((r) => r.Type !== 200);
  if (fail) {
    ElMessage.error(fail.Content);
    return;
  }
});

const tableRowStyle = (data: { row: DoctorSettlementDetailRedash }) => {
  if (data.row.Mode === "退款") {
    return { color: "red" };
  }
  return {};
};

// 点击导出
async function onExport() {
  kEnableDebug && console.debug("点击导出");

  const exportParams = convertToRedashParams(queryParams, "Report_DoctorSettlementDetail");
  const params: ExportTaskRedashDTO = {
    Cols: getExportCols(tableRef.value!.columns as any, "@"),
    ExecutingParams: exportParams.parameters,
    ExportWay: ExportEnum.PlainMySql,
    FileName: `${props.data.OrgName}/${props.data.DeptName}/${props.data.Name}-${props.startTime}-${props.endTime}.xlsx`,
    JobWaitingMs: 30000,
    QueryResultId: queryResultId,
    Split: "@",
    MaxAge: 0,
    PageIndex: queryParams.pageIndex,
    PageSize: queryParams.pageSize,
    QueryName: "Report_DoctorSettlementDetail",
  };
  exportLoading.value = true;
  try {
    await exportExcel(params);
  } catch (error) {
    ElNotification.error("导出失败");
  } finally {
    exportLoading.value = false;
  }
}

// 刷新列表数据
async function refreshTableData() {
  tableLoading.value = true;
  const r = await requestTableList();
  tableLoading.value = false;
  if (r.Type !== 200) {
    ElMessage.error(r.Content);
    return;
  }
}

// 请求列表数据
async function requestTableList() {
  const params = convertToRedashParams(queryParams, "Report_DoctorSettlementDetail");
  const r = await Report_Api.getRedashList<DoctorSettlementDetailRedash>(params);
  if (r.Type === 200) {
    queryResultId = r.Data.QueryResultId;
    pageData.value = r.Data.Data;
    total.value = r.Data.TotalCount;
  }

  return r;
}

// 请求统计数据
async function requestStatistics() {
  const params = convertToRedashParams(queryParams, "Report_DoctorSettlementDetailTotalStatistics");
  const r = await Report_Api.getRedashList<DoctorSettlementStatisticsRedash>(params);
  if (r.Type === 200 && r.Data.Data.length > 0) {
    title.value = `${props.data.OrgName}/${props.data.DeptName}/${props.data.Name}-${props.startTime}-${props.endTime}费用明细（总计￥${r.Data.Data[0].TotalAmount})`;
  }

  return r;
}
</script>

<style lang="scss" scoped></style>
