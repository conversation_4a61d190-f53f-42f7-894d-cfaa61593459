<template>
  <div class="operational-goals">
    <!-- 目标卡片展示区 -->
    <div v-loading="loading" class="carousel-container">
      <el-carousel
        ref="carousel"
        :autoplay="false"
        :initial-index="currentPage"
        :indicator-position="'none'"
        height="100%"
        trigger="click"
        arrow="always"
        @change="handleCarouselChange"
      >
        <el-carousel-item v-for="(group, groupIndex) in groupedGoals" :key="groupIndex">
          <div class="goals-grid">
            <!-- 添加按钮 -->
            <div
              v-if="groupIndex === 0"
              v-hasPermission="['superAdmin']"
              class="card-wrapper"
              @click="handleAddGoal"
            >
              <div class="add-card">
                <el-icon><Plus /></el-icon>
                <span>制定目标</span>
              </div>
            </div>
            <!-- 目标卡片展示区 -->
            <div v-for="(goal, index) in group" :key="index" class="card-wrapper">
              <div class="goal-card">
                <div class="card-header">
                  <span class="card-header_title">
                    {{ formatDate(goal.Month, "YYYY年MM月") }}
                    <span class="card-header_progress">
                      （时间进度：{{ getDateProgress(goal.Month) }}%）
                    </span>
                  </span>

                  <el-button
                    v-hasPermission="['superAdmin']"
                    class="edit-button"
                    type="primary"
                    :icon="Edit"
                    circle
                    size="small"
                    @click.stop="handleEditGoal(goal)"
                  />
                </div>
                <div class="goal-item" @click="handleGoalDetails(goal.Month, 'amount')">
                  <div class="goal-label">目标金额（元）：¥{{ goal.TargetAmount }}</div>
                  <LineProgress
                    text-inside
                    :stroke-width="20"
                    :format="(e: number) => handleFormatContent(goal, 'Amount')"
                    :percentage="
                      goal.TargetAmountCompletionRate > 100 ? 100 : goal.TargetAmountCompletionRate
                    "
                    :color="getProgressColor(goal.TargetAmountCompletionRate, goal.Month)"
                    text-align="left"
                    :middle-progress="{
                      percentage: getDateProgress(goal.Month),
                      color: '#FFC7BA',
                    }"
                  />
                </div>
                <div class="goal-item" @click="handleGoalDetails(goal.Month, 'selfReliance')">
                  <div class="goal-label">自运行目标（单）：{{ goal.TargetSelfRelianceCount }}</div>
                  <LineProgress
                    text-inside
                    :stroke-width="20"
                    :format="(e: number) => handleFormatContent(goal, 'SelfRelianceCount')"
                    :percentage="
                      Number(goal.SelfRelianceCompletionRate) > 100
                        ? 100
                        : Number(goal.SelfRelianceCompletionRate)
                    "
                    :color="getProgressColor(Number(goal.SelfRelianceCompletionRate), goal.Month)"
                    text-align="left"
                    :middle-progress="{
                      percentage: getDateProgress(goal.Month),
                      color: '#FFC7BA',
                    }"
                  />
                </div>
              </div>
            </div>
          </div>
        </el-carousel-item>
      </el-carousel>
    </div>
    <!-- 刷新按钮 -->
    <el-button class="refresh-button" type="primary" circle @click="refreshGoalsList">
      <template #icon>
        <el-icon size="20"><Refresh /></el-icon>
      </template>
    </el-button>
  </div>

  <!-- 制定目标弹框 -->
  <el-dialog
    v-model="dialogVisible.operationalGoals"
    title="制定目标"
    width="800px"
    destroy-on-close
    :close-on-press-escape="false"
    :close-on-click-modal="false"
  >
    <AddGoals ref="addGoalsRefs" :month="goalMonth" :is-edit="isEdit" />
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="dialogVisible.operationalGoals = false">取消</el-button>
        <el-button type="primary" :loading="dialogConfirmLoading" @click="handleAddGoalSubmit">
          确定
        </el-button>
      </div>
    </template>
  </el-dialog>

  <!-- 目标详情 -->
  <el-drawer
    v-if="dialogVisible.goalsDetails"
    v-model="dialogVisible.goalsDetails"
    append-to=".operational-goals"
    direction="rtl"
    :show-close="false"
    size="100%"
  >
    <template #header="{ close, titleId, titleClass }">
      <!-- 自定义头部 -->
      <div class="flex items-center" @click="close">
        <div class="px-10px flex">
          <el-icon><ArrowLeft /></el-icon>
        </div>
        <span :id="titleId" class="flex-1" :class="titleClass">{{ drawerTitle }}</span>
      </div>
    </template>
    <!-- 展示内容 -->
    <GoalDetails :goal-month="goalMonth" :goal-type="goalType" />
  </el-drawer>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from "vue";
import { Plus, Edit, Refresh } from "@element-plus/icons-vue";
import dayjs from "dayjs";
import Report_Api from "@/api/report";
import { BusinessGoalCompletionSummary } from "@/api/report/types";
import AddGoals from "./components/AddGoals.vue";
import Passport_Api from "@/api/passport";
import { formatDate, getDecimalNum } from "@/utils";
import GoalDetails from "./components/GoalDetails.vue";
import LineProgress from "./components/LineProgress.vue";

interface PageBusinessGoalCompletionSummary
  extends Omit<
    BusinessGoalCompletionSummary,
    | "Amount"
    | "SelfRelianceCount"
    | "TargetAmount"
    | "TargetAmountCompletionRate"
    | "TargetSelfRelianceCount"
    | "SelfRelianceCompletionRate"
  > {
  Amount: number;
  SelfRelianceCount: number;
  TargetAmount: number;
  TargetAmountCompletionRate: number;
  TargetSelfRelianceCount: number;
  SelfRelianceCompletionRate: number;
}

interface QueryParams {
  queryName: string;
  parameters: {
    BeginTimeDt: string;
    EndTimeDt: string;
  };
  maxAge: number;
  JobWaitingMs: number;
  pageIndex: number;
  pageSize: number;
}

defineOptions({
  name: "OperationalGoals",
  inheritAttrs: false,
});

const currentPage = ref<number>(0);
const goalsList = ref<PageBusinessGoalCompletionSummary[]>([]);
const dialogVisible = ref<Record<string, boolean>>({
  operationalGoals: false,
  goalsDetails: false,
});
const goalMonth = ref<string>("");
const loading = ref<boolean>(false);
const goalType = ref<string>("");
const carousel = ref();
const addGoalsRefs = ref<InstanceType<typeof AddGoals> | null>(null);
const dialogConfirmLoading = ref<boolean>(false);
const isEdit = ref<boolean>(false);

const queryParams = ref<QueryParams>({
  queryName: "BusinessGoalCompletionSummary_CH",
  parameters: {
    BeginTimeDt: "2020-01",
    EndTimeDt: dayjs(new Date()).format("YYYY-MM-DD"),
  },
  maxAge: 0,
  JobWaitingMs: 30000,
  pageIndex: 1,
  pageSize: 9999,
});

const groupedGoals = computed(() => {
  const result = [];
  const itemsPerPage = 12;
  let i = 0;
  while (i < goalsList.value.length) {
    console.log(i, i + itemsPerPage);

    // 根据条件调整步长
    const step = i === 0 ? itemsPerPage - 1 : itemsPerPage;
    result.push(goalsList.value.slice(i, i + step));
    i += step;
  }
  return result;
});
const drawerTitle = computed(() => {
  return goalType.value === "amount"
    ? `${goalMonth.value}付费金额目标详情`
    : `${goalMonth.value}自运行目标详情`;
});
const handleFormatContent = (
  goal: PageBusinessGoalCompletionSummary,
  type: "Amount" | "SelfRelianceCount"
) => {
  return type === "Amount"
    ? `¥${goal.Amount} （${goal.TargetAmountCompletionRate}%）`
    : `${goal.SelfRelianceCount}（${goal.SelfRelianceCompletionRate}%）`;
};

/** 获取指定日期进度 */
const getDateProgress = (date: string): number => {
  try {
    // 获取当前时间
    const time = dayjs(date);

    // 获取当月开始时间（月初00:00:00）
    const monthStart = time.startOf("month");

    // 获取当月结束时间（月末23:59:59）
    const monthEnd = time.endOf("month");

    // 计算当前时间距离月初的时间差（毫秒）
    const currentDiff = dayjs().valueOf() - monthStart.valueOf();

    // 计算当月总时间差（毫秒）
    const totalDiff = monthEnd.valueOf() - monthStart.valueOf();

    // 使用getDecimalNum进行精确的百分比计算，保留2位小数
    const progress = getDecimalNum(currentDiff).div(getDecimalNum(totalDiff)).times(100).toNumber();

    // 确保返回值在0-100范围内
    return Math.max(0, Math.min(100, Number(progress.toFixed(2))));
  } catch (error) {
    // 如果计算过程中出现错误，返回0
    console.error("计算当前日期进度时出错:", error);
    return 0;
  }
};

const refreshGoalsList = () => {
  queryParams.value.pageIndex = 1;
  getGoalsList();
};

const getGoalsList = async () => {
  try {
    loading.value = true;
    const res = await Report_Api.getRedashList<BusinessGoalCompletionSummary>(queryParams.value);
    if (res.Type === 200) {
      const newData = res.Data.Data.map((item: BusinessGoalCompletionSummary) => {
        return {
          ...item,
          Amount: Number(item.Amount),
          SelfRelianceCompletionRate: Number(item.SelfRelianceCompletionRate),
          SelfRelianceCount: Number(item.SelfRelianceCount),
          TargetAmount: Number(item.TargetAmount),
          TargetAmountCompletionRate: Number(item.TargetAmountCompletionRate),
          TargetSelfRelianceCount: Number(item.TargetSelfRelianceCount),
        };
      });
      goalsList.value = newData;
    } else {
      goalsList.value = [];
    }
  } catch (error) {
    goalsList.value = [];
  } finally {
    loading.value = false;
  }
};

const handleGoalDetails = (month: string, type: string) => {
  goalMonth.value = month;
  goalType.value = type;
  dialogVisible.value.goalsDetails = true;
};

const handleAddGoal = () => {
  isEdit.value = false;
  goalMonth.value = dayjs(new Date()).subtract(1, "month").format("YYYY-MM");
  dialogVisible.value.operationalGoals = true;
};

const getProgressColor = (percentage: number, month: string) => {
  return percentage >= getDateProgress(month) ? "#25B8A3" : "#FF5F5C";
};

const handleCarouselChange = (index: number) => {
  currentPage.value = index;
};

const handleAddGoalSubmit = () => {
  const params = addGoalsRefs.value?.handleSubmit();
  if (!params) {
    return;
  }
  console.log("params", params);
  const fun = isEdit.value ? Passport_Api.updateBusinessGoal : Passport_Api.insertBusinessGoal;
  dialogConfirmLoading.value = true;
  fun(params)
    .then((res) => {
      if (res.Type === 200) {
        ElMessage.success(res.Content);
        dialogVisible.value.operationalGoals = false;
        refreshGoalsList();
      } else {
        ElMessage.error(res.Content);
      }
    })
    .catch((err) => {
      ElMessage.error(err);
    })
    .finally(() => {
      dialogConfirmLoading.value = false;
    });
};

const handleEditGoal = (goal: PageBusinessGoalCompletionSummary) => {
  goalMonth.value = goal.Month;
  isEdit.value = true;
  dialogVisible.value.operationalGoals = true;
};

onMounted(() => {
  getGoalsList();
});
</script>

<style scoped lang="scss">
.operational-goals {
  position: relative;
  display: flex;
  height: 100%;
  padding: 20px;
  background: var(--el-bg-color-overlay);
  transform: translate(0, 0);

  .refresh-button {
    position: absolute;
    bottom: 30px;
    left: 40px;
    padding: 20px !important;
  }
}

.carousel-container {
  position: relative;
  flex: 1;
  overflow: hidden;
  background: var(--el-bg-color-overlay);
  border-radius: 4px;

  :deep(.el-carousel),
  :deep(.el-carousel__container) {
    height: 100%;
  }

  :deep(.el-carousel__item) {
    overflow-y: auto;
  }
}

.goals-grid {
  display: flex;
  flex-wrap: wrap;
  padding-right: 8px;
  overflow-y: auto;

  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-thumb {
    background-color: #dcdfe6;
    border-radius: 3px;
  }

  &::-webkit-scrollbar-track {
    background-color: #f5f7fa;
  }
}

.card-wrapper {
  box-sizing: border-box;
  width: 25%;
  padding: 16px;

  .goal-card {
    position: relative;
    height: 100%;
    padding: 15px;
    background: #fff;
    border: 1px solid #ebeef5;
    border-radius: 4px;
    box-shadow: 0 2px 12px 0 rgb(0 0 0 / 5%);

    .card-header {
      position: relative;
      display: flex;
      align-items: center;
      justify-content: flex-start;
      margin-bottom: 20px;

      &_title {
        margin-right: 24px;
        font-size: 20px;
        font-weight: 600;
        color: #303133;
      }

      &_progress {
        font-size: 14px;
        color: #666;
      }

      .edit-button {
        position: absolute;
        top: 0;
        right: 0;
        opacity: 0.7;
        transition: all 0.3s;

        &:hover {
          opacity: 1;
          transform: scale(1.1);
        }
      }
    }

    .goal-item {
      margin-bottom: 15px;
      cursor: pointer;

      .goal-label {
        margin-bottom: 8px;
        font-size: 16px;
        font-weight: 500;
        color: #606266;
      }
    }
  }

  .add-card {
    display: flex !important;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    cursor: pointer;
    background: #fff;
    border: 1px solid #ebeef5;
    border-radius: 4px;
    box-shadow: 0 2px 12px 0 rgb(0 0 0 / 5%);
    transition: all 0.3s;

    &:hover {
      color: #409eff;
      border-color: #409eff;
      box-shadow: 0 2px 12px 0 rgb(0 0 0 / 10%);
    }

    .el-icon {
      margin-bottom: 8px;
      font-size: 24px;
    }
  }
}

:deep(.el-drawer__header) {
  margin-bottom: 0 !important;
}
</style>
