<template>
  <div class="container1">
    <el-tabs
      v-model="activeName"
      class="flex-1 overflow-auto pt-1px position-relative"
      type="card"
      @tab-click="handleTabClick"
    >
      <el-tab-pane label="按地区" name="region">
        <GroupDetails
          :table-data="tableData"
          :goal-month="props.goalMonth"
          :goal-type="props.goalType"
          :group-by-mode="0"
          :data-range="dataRange"
          :day-range="[queryParams.parameters.BeginTimeDt, queryParams.parameters.EndTimeDt]"
        />
      </el-tab-pane>
      <el-tab-pane label="按医助" name="doctor">
        <GroupDetails
          :table-data="tableData"
          :goal-month="props.goalMonth"
          :goal-type="props.goalType"
          :group-by-mode="1"
          :data-range="dataRange"
          :day-range="[queryParams.parameters.BeginTimeDt, queryParams.parameters.EndTimeDt]"
        />
      </el-tab-pane>
      <el-tab-pane label="按管理负责人" name="personCharge">
        <GroupDetails
          :table-data="tableData"
          :goal-month="props.goalMonth"
          :goal-type="props.goalType"
          :group-by-mode="2"
          :data-range="dataRange"
          :day-range="[queryParams.parameters.BeginTimeDt, queryParams.parameters.EndTimeDt]"
        />
      </el-tab-pane>
    </el-tabs>
    <div class="mb-20px mr-20px position-absolute top-0 right-0">
      <el-date-picker
        v-model="dataRange"
        unlink-panels
        type="daterange"
        align="right"
        :shortcuts="datePickerShortcuts"
        range-separator="至"
        start-placeholder="开始日期"
        end-placeholder="结束日期"
        format="YYYY 年 MM 月 DD 日"
        value-format="YYYY-MM-DD"
      />
      <el-button class="ml-10px" type="primary" @click="handleSearch">搜索</el-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, watch } from "vue";
import type { TabsPaneContext } from "element-plus";
import { ElMessage } from "element-plus";
import dayjs from "dayjs";
import Report_Api from "@/api/report";
import { BusinessGoalCompletionDetail } from "@/api/report/types";

import GroupDetails from "./GroupDetails.vue";

import { useDateRangePicker } from "@/hooks/useDateRangePicker";
const { datePickerShortcuts } = useDateRangePicker();

interface QueryParams {
  queryName: string;
  parameters: {
    BeginTimeDt: string;
    EndTimeDt: string;
    LimitBeginTimeDt: string;
    LimitEndTimeDt: string;
    GroupByMode: number;
  };
  maxAge: number;
  JobWaitingMs: number;
  pageIndex: number;
  pageSize: number;
}

interface Props {
  goalMonth: string;
  goalType?: string;
}

const props = withDefaults(defineProps<Props>(), {
  goalType: "",
});

const activeName = ref("region");
const dataRange = ref<string[]>([]);
const isLoading = ref(false);
const tableData = ref<BusinessGoalCompletionDetail[]>([]);

const queryParams = reactive<QueryParams>({
  queryName: "BusinessGoalCompletionDetail_CH",
  parameters: {
    BeginTimeDt: "",
    EndTimeDt: "",
    LimitBeginTimeDt: "",
    LimitEndTimeDt: "",
    GroupByMode: 0,
  },
  maxAge: 0,
  JobWaitingMs: 30000,
  pageIndex: 1,
  pageSize: 9999,
});

const handleTabClick = (tab: TabsPaneContext) => {
  activeName.value = tab.props.name as string;
  const queryMap: Record<string, number> = {
    region: 0,
    doctor: 1,
    personCharge: 2,
  };
  queryParams.parameters.GroupByMode = queryMap[tab.props.name as string];
  getGoalDetails();
};

const handleSearch = () => {
  if (dataRange.value.length === 2) {
    queryParams.parameters.LimitBeginTimeDt = dayjs(dataRange.value[0]).format(
      "YYYY-MM-DD 00:00:00"
    );
    queryParams.parameters.LimitEndTimeDt = dayjs(dataRange.value[1]).format("YYYY-MM-DD 23:59:59");
    getGoalDetails();
  }
};

const getGoalDetails = async () => {
  try {
    isLoading.value = true;
    const res = await Report_Api.getRedashList<BusinessGoalCompletionDetail>(queryParams);
    if (res.Type === 200) {
      tableData.value = res.Data.Data;
    } else {
      tableData.value = [];
      ElMessage.error("获取数据失败");
    }
  } catch (error) {
    console.error(error);
    tableData.value = [];
    ElMessage.error("获取数据失败");
  } finally {
    isLoading.value = false;
  }
};

watch(
  () => props.goalMonth,
  (newVal) => {
    if (newVal) {
      queryParams.parameters.BeginTimeDt = dayjs(newVal).format("YYYY-MM-01 00:00:00");
      const lastDay = dayjs(newVal).endOf("month").format("YYYY-MM-DD");
      queryParams.parameters.EndTimeDt = dayjs(lastDay).format("YYYY-MM-DD 23:59:59");
      queryParams.parameters.LimitBeginTimeDt = dayjs(newVal).format("YYYY-MM-01 00:00:00");
      queryParams.parameters.LimitEndTimeDt = dayjs(lastDay).format("YYYY-MM-DD 23:59:59");
      dataRange.value = [dayjs(newVal).format("YYYY-MM-01"), dayjs(lastDay).format("YYYY-MM-DD")];
      getGoalDetails();
    }
  },
  { immediate: true }
);
</script>

<style scoped lang="scss">
.container1 {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: stretch;
  justify-content: flex-start;
  width: 100%;
  height: calc(100%);
  padding: 0 20px 20px;
}

.footer {
  position: absolute;
  right: 0;
  bottom: 0;
  width: 100%;
  padding: 10px;
  text-align: right;
  background: #fff;
  border-top: 1px solid #ebeef5;

  .el-button + .el-button {
    margin-left: 10px;
  }
}

:deep(.el-tab-pane) {
  height: 100%;
}
</style>
