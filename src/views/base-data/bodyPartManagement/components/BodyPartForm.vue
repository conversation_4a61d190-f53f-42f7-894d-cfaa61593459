<template>
  <div class="app-container">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="rules"
      label-width="80px"
      class="consumable-type-form"
      :disabled="isPreview"
      scroll-to-error
    >
      <el-form-item label="名称" prop="Key">
        <el-input v-model="formData.Key" placeholder="请输入名称" @blur="handleBlurKey" />
      </el-form-item>
      <el-form-item label="拼音码" prop="PinyinCode">
        <el-input v-model="formData.PinyinCode" placeholder="请输入拼音码" />
      </el-form-item>
      <el-form-item label="编码" prop="Value">
        <el-input v-model="formData.Value" placeholder="请输入编码" />
      </el-form-item>
      <el-form-item label="排序" prop="OrderNumber">
        <el-input-number v-model="formData.OrderNumber" :precision="0" :step="1" />
      </el-form-item>
      <el-form-item label="备注" prop="Remark">
        <el-input v-model="formData.Remark" type="textarea" placeholder="请输入备注" />
      </el-form-item>
      <el-form-item label="是否启用" prop="IsEnabled">
        <el-switch v-model="formData.IsEnabled" />
      </el-form-item>
      <el-form-item label="关联病种" />
      <el-transfer
        v-model="selectedDiseaseIds"
        :titles="['待选列表', '已选列表']"
        filterable
        :filter-method="diseaseFilterMethod"
        filter-placeholder="请输入疾病名称搜索"
        :data="diseaseAllList"
        :props="{
          key: 'Id',
          label: 'Key',
        }"
      />
    </el-form>
  </div>
</template>

<script setup lang="ts">
import { DictCreateUpdateInputDTO } from "@/api/dictionary/types";
import { chineseToPinyin } from "@/utils/pinyin-helper";
import { getDiseaseList } from "@/utils/dict";
import { FormInstance, FormRules } from "element-plus";

const diseaseFilterMethod = (query: string, item: any) => {
  return item.Key.indexOf(query) > -1;
};

const isPreview = inject("isPreview") as Ref<boolean>;

const formData = ref<DictCreateUpdateInputDTO>({
  Key: "",
  PinyinCode: "",
  Value: "",
  Remark: "",
  IsEnabled: true,
  OrgId: "",
  IsPublish: false,
  DictId: 16,
  OrderNumber: 0,
});

const formRef = ref<FormInstance>();

const selectedDiseaseIds = ref<string[]>([]);

const diseaseAllList = ref<ReadDict[]>([]);

const rules = reactive<FormRules>({
  Key: [
    { required: true, message: "请输入名称", trigger: "blur" },
    { min: 1, max: 50, message: "长度在 1 到 50 个字符", trigger: "blur" },
  ],
  PinyinCode: [{ required: true, message: "请输入拼音码", trigger: "blur" }],
  Value: [{ required: true, message: "请输入编码", trigger: "blur" }],
});
const handleGetSubmitParams = async (): Promise<DictCreateUpdateInputDTO | null> => {
  // 校验表单
  await formRef.value!.validate();
  formData.value.DictItemRelateds = selectedDiseaseIds.value.map((item) => ({
    DictItemIdA: item,
  }));
  return formData.value;
};

const handleProcessDetail = (value: ReadDict | null) => {
  console.log("value", value);
  if (!value) return;
  const defaultData: DictCreateUpdateInputDTO = {
    ...formData.value,
    Key: value.Key!,
    PinyinCode: value.PinyinCode!,
    Value: value.Value!,
    Remark: value.Remark!,
    IsEnabled: value.IsEnabled!,
    OrgId: value.OrgId!,
    IsPublish: value.IsPublish!,
    DictId: value.DictId ? value.DictId : 16,
    OrderNumber: value.OrderNumber || 0,
    Id: value.Id,
  };
  if (!value.Id) {
    delete defaultData.Id;
  }
  formData.value = { ...defaultData };
  selectedDiseaseIds.value = value.DictItemRelateds?.map((item) => item.DictItemIdA) || [];
};

const handleBlurKey = () => {
  formData.value.PinyinCode = chineseToPinyin(formData.value.Key);
};

const handleGetDiseaseList = async () => {
  const list = await getDiseaseList();
  diseaseAllList.value = list;
};

interface Props {
  bodyDetail: ReadDict | null;
}
const props = defineProps<Props>();

onBeforeMount(() => {
  handleGetDiseaseList();
});

watch(
  () => props.bodyDetail,
  (newVal) => {
    handleProcessDetail(newVal);
  },
  { immediate: true }
);
defineExpose({
  handleGetSubmitParams,
});
</script>

<style lang="scss" scoped>
.app-container {
  height: 500px;
  overflow-y: auto;
}

:deep(.el-transfer-panel) {
  width: calc(50% - 80px);
}
</style>
