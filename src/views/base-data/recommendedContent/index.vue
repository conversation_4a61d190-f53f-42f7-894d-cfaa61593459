<template>
  <div class="app-container">
    <BaseTableSearchContainer @size-changed="tableResize">
      <!-- 顶部筛选条件 -->
      <template #search>
        <TBSearchContainer :is-show-toggle="true">
          <template #left>
            <el-form :model="queryParams" label-position="right" :inline="true">
              <el-form-item label="是否启用">
                <KSelect
                  v-model="queryParams.IsEnable"
                  :data="[
                    { label: '是', value: true },
                    { label: '否', value: false },
                  ]"
                  :show-all="true"
                />
              </el-form-item>
              <el-form-item label="关键字">
                <el-input
                  v-model="queryParams.keyword"
                  placeholder="请输入"
                  clearable
                  @keyup.enter="handleQuery"
                />
              </el-form-item>
            </el-form>
          </template>
          <template #right>
            <el-button type="primary" icon="search" @click="handleQuery">搜索</el-button>
            <el-button type="primary" @click="onPushItem">推送</el-button>
            <el-button type="primary" @click="onAddItem">添加</el-button>
          </template>
        </TBSearchContainer>
      </template>
      <!-- 列表 -->
      <template #table>
        <el-table
          :ref="kTableRef"
          v-loading="tableLoading"
          :data="pageData"
          :total="total"
          row-key="Id"
          :height="tableFluidHeight"
          :header-cell-style="{ textAlign: 'center' }"
          :cell-style="{ textAlign: 'center' }"
          border
          highlight-current-row
          @selection-change="(selection) => handleSelectionChange(selection, 'Id')"
        >
          <el-table-column type="selection" width="55" />
          <el-table-column prop="Name" label="推荐内容" />
          <el-table-column prop="Code" label="编码" />
          <el-table-column prop="PYM" label="拼音码" />
          <el-table-column prop="CreatedTime" label="创建时间" :formatter="tableDateFormat" />
          <el-table-column label="是否启用" width="100" align="center">
            <template #default="scope">
              {{ scope.row.IsEnable ? "是" : "否" }}
            </template>
          </el-table-column>
          <!-- 操作 -->
          <el-table-column fixed="right" label="操作" width="180">
            <template #default="scope">
              <el-button link type="primary" @click="onEdit(scope.row)">编辑</el-button>
              <el-button link type="primary" @click="onDelete(scope.row)">删除</el-button>
            </template>
          </el-table-column>
        </el-table>
      </template>
      <!-- 分页 -->
      <template #pagination>
        <Pagination
          v-if="total > 0"
          v-model:total="total"
          v-model:page="queryParams.PageIndex"
          v-model:limit="queryParams.PageSize"
          @pagination="requestTableList"
        />
      </template>
    </BaseTableSearchContainer>
  </div>

  <!-- 添加/编辑/查看 -->
  <el-dialog
    v-model="showDataDialog.isShow"
    :title="showDataDialog.title"
    width="500"
    destroy-on-close
    @close="showDataDialog.isShow = false"
  >
    <RecommendedContentForm
      :data="showDataDialog.data"
      :disabled="showDataDialog.disabled"
      @cancel="showDataDialog.isShow = false"
      @submit="onConfirmSubmitItem"
    />
  </el-dialog>

  <!-- 选择机构 -->
  <el-dialog v-model="showOrgDialog" title="选择机构" width="700px" destroy-on-close>
    <HospitalTransfer
      :loading="orgDialogLoading"
      @cancel="showOrgDialog = false"
      @submit="onConfirmOrganizations"
    />
  </el-dialog>
</template>

<script setup lang="ts">
import { useTableConfig } from "@/hooks/useTableConfig";
import useOrgDialog from "@/hooks/useOrgDialog";
import { GetRecommendPageParams, RecommendOutputDTO } from "@/api/content/types";
import RecommendedContentForm from "./components/RecommendedContentForm.vue";
import Content_Api from "@/api/content";

/** 调试开关 */
const kEnableDebug = false;
defineOptions({
  name: "RecommendedContent",
});

const {
  kTableRef,
  tableRef,
  pageData,
  tableLoading,
  tableFluidHeight,
  total,
  tableResize,
  handleSelectionChange,
  selectedTableIds,
  tableDateFormat,
} = useTableConfig<RecommendOutputDTO>();
const { showOrgDialog, orgDialogLoading } = useOrgDialog();

/** 查询条件 */
const queryParams = reactive<GetRecommendPageParams>({
  PageIndex: 1,
  PageSize: 20,
});

/** 点击搜索 */
function handleQuery() {
  queryParams.PageIndex = 1;
  requestTableList();
}

/** 查看/添加/编辑弹窗 */
const showDataDialog = reactive({
  isShow: false,
  title: "",
  disabled: false,
  data: {} as RecommendOutputDTO, // 查看/添加/编辑详情
});

/** 点击添加 */
function onAddItem() {
  kEnableDebug && console.debug("点击添加");

  showDataDialog.title = "新增";
  showDataDialog.disabled = false;
  showDataDialog.data = {};
  showDataDialog.isShow = true;
}

/** 点击编辑 */
async function onEdit(row: RecommendOutputDTO) {
  kEnableDebug && console.debug("编辑", row);

  showDataDialog.title = "编辑";
  showDataDialog.disabled = false;
  showDataDialog.data = row;
  showDataDialog.isShow = true;
}

/** 确定新增提交 */
function onConfirmSubmitItem() {
  kEnableDebug && console.debug("确定提交");

  // 提交成功
  showDataDialog.isShow = false;
  ElNotification.success("提交成功");

  // 刷新列表
  requestTableList();
}

/** 点击删除 */
async function onDelete(row: RecommendOutputDTO) {
  kEnableDebug && console.debug("删除", row);

  if (!row.Id) {
    ElMessage.error("id为空");
    return;
  }

  ElMessageBox.confirm("此操作将删除该条数据, 是否继续?", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(async () => {
    const r = await Content_Api.deleteRecommend({ Id: row.Id! });
    if (r.Type !== 200) {
      ElMessage.error(r.Content);
      return;
    }

    ElNotification.success("删除成功");
    requestTableList();
  });
}

/** 点击推送 */
function onPushItem() {
  kEnableDebug && console.debug("点击推送");

  if (selectedTableIds.value.length === 0) {
    ElMessage.warning("请选择要推送的内容");
    return;
  }

  showOrgDialog.value = true;
}

/** 确定选择机构 */
async function onConfirmOrganizations(organizationIds: string[]) {
  kEnableDebug && console.log("选择机构", organizationIds);

  if (organizationIds.length === 0) {
    ElMessage.warning("请选择要推送的机构");
    return;
  }

  // 推送
  orgDialogLoading.value = true;
  const tableIds = selectedTableIds.value ?? [];
  const r = await Content_Api.pushRecommend({ Ids: tableIds, OrgIds: organizationIds });
  orgDialogLoading.value = false;
  if (r.Type !== 200) {
    ElMessage.error(r.Content);
    return;
  }

  showOrgDialog.value = false;
  ElNotification.success("推送成功");

  // 清空选项
  selectedTableIds.value = [];
  tableRef.value?.clearSelection();
}

/** 请求列表数据 */
async function requestTableList() {
  tableLoading.value = true;
  const r = await Content_Api.getRecommendPage(queryParams);
  tableLoading.value = false;
  if (r.Type !== 200) {
    ElMessage.error(r.Content);
    return;
  }

  // 请求成功
  pageData.value = r.Data.Data;
  total.value = r.Data.TotalCount;
}

onActivated(() => {
  requestTableList();
});
</script>

<style lang="scss" scoped></style>
