<template>
  <div v-loading="formLoading" class="p-20px overflow-y-auto">
    <el-form
      :ref="kFormRef"
      :model="formData"
      :rules="rules"
      label-width="auto"
      :disabled="props.disabled"
    >
      <el-form-item label="推荐内容" prop="Name">
        <el-input
          v-model="formData.Name"
          placeholder="请输入推荐内容"
          :disabled="!!props.data.Id"
          clearable
          @blur="onBlurName"
        />
      </el-form-item>
      <el-form-item label="编码" prop="Code">
        <el-input
          v-model="formData.Code"
          :disabled="!!props.data.Id"
          placeholder="请输入编码"
          clearable
        />
      </el-form-item>
      <el-form-item label="拼音码" prop="PYM">
        <el-input
          v-model="formData.PYM"
          placeholder="请输入拼音码"
          :disabled="!!props.data.Id"
          clearable
        />
      </el-form-item>
      <el-form-item label="是否启用" prop="IsEnable">
        <el-switch v-model="formData.IsEnable" />
      </el-form-item>
    </el-form>
  </div>
  <!-- 底部按钮 -->
  <div class="flex justify-end">
    <el-button @click="emit('cancel')">取消</el-button>
    <el-button v-if="!props.disabled" type="primary" @click="onSubmitForm()">确定</el-button>
  </div>
</template>

<script setup lang="ts">
import { FormRules, FormInstance } from "element-plus";
import { RecommendInputDTO, RecommendOutputDTO } from "@/api/content/types";
import Content_Api from "@/api/content";
import { chineseToPinyin } from "@/utils/pinyin-helper";
import { useUserStore } from "@/store";

const kEnableDebug = false;
const kFormRef = "ruleFormRef";

defineOptions({
  name: "RecommendedContentForm",
});

const props = defineProps<{
  data: RecommendOutputDTO;
  disabled: boolean;
}>();

const emit = defineEmits<{
  cancel: [];
  submit: [];
}>();

onMounted(() => {
  const data = JSON.parse(JSON.stringify(props.data));
  formData.Name = data.Name;
  formData.Code = data.Code;
  formData.PYM = data.PYM;
  formData.IsEnable = data.IsEnable ?? true;
});

const formLoading = ref(false);
/** 表单实例 */
const formRef = useTemplateRef<FormInstance>(kFormRef);
/** 表单数据 */
const formData = reactive<RecommendInputDTO>({
  Name: "",
  Code: "",
  PYM: "",
  IsEnable: true,
});

/** 表单验证规则 */
const rules = reactive<FormRules<RecommendInputDTO>>({
  Name: [{ required: true, message: "请输入推荐内容", trigger: "blur" }],
  Code: [{ required: true, message: "请输入编码", trigger: "blur" }],
  PYM: [{ required: true, message: "请输入拼音码", trigger: "blur" }],
});

/** 自动获取编码、拼音码 */
function onBlurName() {
  if (!formData.Name.trim()) {
    return;
  }

  kEnableDebug && console.debug("推荐内容失去焦点", formData.Name);
  formData.Code = chineseToPinyin(formData.Name);
  formData.PYM = chineseToPinyin(formData.Name);
}

/** 提交表单 */
function onSubmitForm() {
  if (!formRef.value) return;

  formRef.value.validate((valid, fields) => {
    if (valid) {
      requestAddOrUpdateData();
    } else {
      kEnableDebug && console.debug("提交失败", fields);
    }
  });
}

/** 添加/更新数据 */
async function requestAddOrUpdateData() {
  kEnableDebug && console.debug("添加/更新数据", formData);

  formLoading.value = true;
  const params: RecommendInputDTO = {
    ...formData,
    CreatorId: useUserStore().userInfo.Id,
    IsPublish: props.data.IsPublish,
    ReferenceId: props.data.ReferenceId,
    OrganizationId: props.data.OrganizationId,
    DeptIds: props.data.DeptIds,
    CreatedTime: props.data.CreatedTime,
  };
  if (props.data.Id) {
    params.ObjectId = props.data.Id;
  }

  const r = await Content_Api.insertOrUpdateRecommend(params);
  formLoading.value = false;
  if (r.Type === 200) {
    emit("submit");
  } else {
    ElMessage.error(r.Content);
  }
}
</script>

<style lang="scss" scoped></style>
