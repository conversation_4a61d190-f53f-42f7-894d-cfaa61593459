<template>
  <el-container class="w-full h-full">
    <!-- 左侧目录 -->
    <el-aside width="230px" class="p-10px">
      <EditTree
        ref="treeRef"
        :data="treeList"
        :props="defaultTreeProps"
        node-key="Id"
        :current-node-key="queryParams.type"
        :operations="getTreeNodeOperations"
        @add="(e) => onAddOrEditTreeNode(e, true)"
        @edit="(e) => onAddOrEditTreeNode(e, false)"
        @delete="(e) => onDeleteTreeNode(e)"
        @node-click="onTreeClick"
        @node-contextmenu="onNodeRightClick"
      />
    </el-aside>
    <el-main class="p-10px!">
      <BaseTableSearchContainer @size-changed="tableResize">
        <!-- 顶部筛选条件 -->
        <template #search>
          <TBSearchContainer :is-show-toggle="true">
            <template #left>
              <el-form :model="queryParams" label-position="right" :inline="true">
                <el-form-item label="是否启用">
                  <KSelect
                    v-model="queryParams.isEnable"
                    :data="[
                      { label: '是', value: true },
                      { label: '否', value: false },
                    ]"
                    :show-all="true"
                  />
                </el-form-item>
                <el-form-item label="关键字">
                  <el-input
                    v-model="queryParams.keywords"
                    placeholder="名称/编码/拼音码"
                    clearable
                    @keyup.enter="handleQuery"
                  />
                </el-form-item>
              </el-form>
            </template>
            <template #right>
              <el-button type="primary" icon="search" @click="handleQuery">搜索</el-button>
              <el-button type="primary" @click="onAddItem">添加</el-button>
            </template>
          </TBSearchContainer>
        </template>
        <!-- 列表 -->
        <template #table>
          <el-table
            v-loading="tableLoading"
            :data="pageData"
            :total="total"
            row-key="Id"
            :height="tableFluidHeight"
            :header-cell-style="{ textAlign: 'center' }"
            :cell-style="{ textAlign: 'center' }"
            border
            highlight-current-row
          >
            <el-table-column prop="Code" label="编码" width="120" />
            <el-table-column prop="Name" label="穴位名称" min-width="100" show-overflow-tooltip />
            <el-table-column
              prop="AcuPointTypeId"
              label="分类"
              min-width="100"
              show-overflow-tooltip
              :formatter="formatAcuPointType"
            />
            <el-table-column label="图片" width="80">
              <template #default="scope">
                <el-button link type="primary" @click="onPreviewAcuPointImages(scope.row)">
                  预览
                </el-button>
              </template>
            </el-table-column>
            <el-table-column prop="Remark" label="说明" min-width="100" show-overflow-tooltip />
            <el-table-column label="是否启用" width="80" align="center">
              <template #default="scope">
                {{ scope.row.IsEnable ? "是" : "否" }}
              </template>
            </el-table-column>
            <el-table-column fixed="right" label="操作" width="150">
              <template #default="scope">
                <el-button link type="primary" @click="onPreviewOrEdit(scope.row, true)">
                  查看
                </el-button>
                <el-button link type="primary" @click="onPreviewOrEdit(scope.row, false)">
                  编辑
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </template>
        <!-- 分页 -->
        <template #pagination>
          <Pagination
            v-if="total > 0"
            v-model:total="total"
            v-model:page="queryParams.page"
            v-model:limit="queryParams.pageSize"
            @pagination="requestTableList"
          />
        </template>
      </BaseTableSearchContainer>
    </el-main>
  </el-container>

  <!-- 预览图片 -->
  <el-image-viewer
    v-if="showPreview"
    :url-list="imagesSrcList"
    show-progress
    :initial-index="0"
    @close="showPreview = false"
  />

  <!-- 添加/编辑左侧树节点 -->
  <el-dialog
    v-model="showTreeNodeDialog.isShow"
    :title="showTreeNodeDialog.title"
    width="500px"
    destroy-on-close
    @close="showTreeNodeDialog.isShow = false"
  >
    <AcupointTypeForm
      :data="showTreeNodeDialog.data"
      :disabled="false"
      @cancel="showTreeNodeDialog.isShow = false"
      @submit="onConfirmAddOrEditTreeNode"
    />
  </el-dialog>

  <!-- 添加/编辑/查看 -->
  <el-dialog
    v-model="showDataDialog.isShow"
    :title="showDataDialog.title"
    width="650"
    destroy-on-close
    @close="showDataDialog.isShow = false"
  >
    <AcupointForm
      :data="showDataDialog.data"
      :type-list="typeList"
      :disabled="showDataDialog.disabled"
      @cancel="showDataDialog.isShow = false"
      @submit="onConfirmSubmitItem"
    />
  </el-dialog>
</template>

<script setup lang="ts">
import { useTableConfig } from "@/hooks/useTableConfig";
import { AcupointType, GetAcuPointPageDataParams } from "@/api/content/types";
import Content_Api from "@/api/content";
import { ImageInstance } from "element-plus";
import AcupointTypeForm from "./components/AcupointTypeForm.vue";
import AcupointForm from "./components/AcupointForm.vue";

interface AcupointTypeNode extends AcupointType {
  Children?: AcupointTypeNode[];
}

/** 调试开关 */
const kEnableDebug = false;
defineOptions({
  name: "AcupointManagement",
});

const { pageData, tableLoading, tableFluidHeight, total, tableResize, tableDateFormat } =
  useTableConfig<BaseAcuPoint>();

/** 查询条件 */
const queryParams = reactive<GetAcuPointPageDataParams>({
  page: 1,
  pageSize: 20,
});

/** 递归查询当前类别 */
function findTreeNodeById(
  tree: AcupointTypeNode[],
  targetId: string
): AcupointTypeNode | undefined {
  // 遍历树中的每个节点
  for (const node of tree) {
    // 如果当前节点的 Id 匹配目标 Id，返回该节点
    if (node.Id === targetId) {
      return node;
    }

    // 如果当前节点有子节点，递归查找子节点
    if (node.Children && node.Children.length > 0) {
      const foundNode = findTreeNodeById(node.Children, targetId);
      if (foundNode) {
        return foundNode; // 如果在子节点中找到，直接返回
      }
    }
  }

  // 如果未找到匹配的节点，返回 undefined
  return undefined;
}

/** 格式化分类 */
function formatAcuPointType(row: BaseAcuPoint) {
  if (!row.AcuPointTypeId) return "";

  return findTreeNodeById(treeList.value, row.AcuPointTypeId)?.Name ?? "";
}

const imageRef = ref<ImageInstance>();
const imagesSrcList = ref<string[]>([]);
const showPreview = ref(false);

/** 点击预览穴位图片 */
function onPreviewAcuPointImages(row: BaseAcuPoint) {
  if (!row.AcuPointImg || row.AcuPointImg.length === 0) {
    return;
  }
  kEnableDebug && console.debug("点击预览穴位图片", row);

  imagesSrcList.value = row.AcuPointImg;
  showPreview.value = true;

  imageRef.value!.showPreview();
}

/** 点击搜索 */
function handleQuery() {
  queryParams.page = 1;
  requestTableList();
}

/** 查看/添加/编辑弹窗 */
const showDataDialog = reactive({
  isShow: false,
  title: "",
  disabled: false,
  data: {} as BaseAcuPoint, // 查看/添加/编辑详情
});

/** 点击添加 */
function onAddItem() {
  kEnableDebug && console.debug("点击添加");

  if (!queryParams.type) {
    ElMessage.warning("请选择穴位分类");
    return;
  }
  const node = findTreeNodeById(treeList.value, queryParams.type);
  if (!node?.IsEnable) {
    ElMessageBox.confirm("分类未启用，不能添加内容！", {
      confirmButtonText: "确定",
      type: "warning",
    });
    return;
  }

  showDataDialog.title = "新增";
  showDataDialog.disabled = false;
  showDataDialog.data = {
    AcuPointTypeId: queryParams.type!,
  };
  showDataDialog.isShow = true;
}

/** 点击查看/编辑 */
async function onPreviewOrEdit(row: BaseAcuPoint, disabled: boolean = false) {
  kEnableDebug && console.debug("查看/编辑", row, disabled);

  showDataDialog.title = disabled ? "查看" : "编辑";
  showDataDialog.disabled = disabled;
  showDataDialog.data = row;
  showDataDialog.isShow = true;
}

/** 确定新增提交 */
function onConfirmSubmitItem() {
  kEnableDebug && console.debug("确定提交");

  // 提交成功
  showDataDialog.isShow = false;
  ElNotification.success("提交成功");

  // 刷新列表
  requestTableList();
}

/** 请求列表数据 */
async function requestTableList() {
  tableLoading.value = true;
  const r = await Content_Api.getAcuPointPageData(queryParams);
  tableLoading.value = false;
  if (r.Type !== 200) {
    ElMessage.error(r.Content);
    return;
  }

  // 请求成功
  pageData.value = r.Data.Data;
  total.value = r.Data.TotalCount;

  return;
}

/** 编辑/添加左侧树节点弹窗 */
const showTreeNodeDialog = reactive({
  isShow: false,
  title: "",
  data: {} as AcupointTypeNode,
});

/** 树实例 */
const treeRef = useTemplateRef("treeRef");

/** 左侧树列表数据结构 */
const treeList = ref<AcupointTypeNode[]>([]);
const typeList = ref<AcupointType[]>([]);

const defaultTreeProps = reactive({
  children: "Children",
  label: "Name",
});

/** 树点击事件 */
async function onTreeClick(data: AcupointTypeNode) {
  kEnableDebug && console.debug("树点击事件", data);
  queryParams.type = data.Id;
  queryParams.page = 1;

  requestTableList();
}

/** 树右键点击事件 */
function onNodeRightClick(data: AcupointTypeNode) {
  kEnableDebug && console.debug("树右键点击事件", data);
  showTreeNodeDialog.data = data;
}

/** 获取树节点右键操作 */
function getTreeNodeOperations(data: AcupointTypeNode) {
  if (data.Name === "全部") {
    return {
      add: true,
    };
  }

  return {
    edit: true,
    delete: true,
  };
}

/** 点击删除左侧树数据 */
async function onDeleteTreeNode(data: AcupointTypeNode) {
  kEnableDebug && console.debug("点击删除左侧树数据");

  if (!data.Id) {
    ElMessage.error("穴位类别Id为空");
    return;
  }

  ElMessageBox.confirm("此操作将删除该条数据, 是否继续?", {
    confirmButtonText: "确定",
    type: "warning",
  }).then(async () => {
    const r = await Content_Api.deleteAcuPointType(data.Id!);
    if (r.Type !== 200) {
      ElMessage.error(r.Content);
      return;
    }

    ElNotification.success("删除成功");

    // 刷新树
    treeRef.value?.remove(data);

    // 删除当前分类，刷新列表
    if (queryParams.type === data.Id) {
      queryParams.type = undefined;
      queryParams.page = 1;
      requestTableList();
    }
  });
}

/** 点击添加/编辑左侧树数据 */
function onAddOrEditTreeNode(data: AcupointTypeNode, add: boolean) {
  kEnableDebug && console.debug("点击添加/编辑左侧树数据", add, data);

  if (add) {
    showTreeNodeDialog.data = {};
    showTreeNodeDialog.title = "添加穴位类别";
    showTreeNodeDialog.isShow = true;
    return;
  }

  showTreeNodeDialog.data = data;
  showTreeNodeDialog.title = "编辑穴位类别";
  showTreeNodeDialog.isShow = true;
}

/** 确认添加/编辑左侧树数据 */
async function onConfirmAddOrEditTreeNode() {
  kEnableDebug && console.debug("确认添加/编辑左侧树数据");

  showTreeNodeDialog.isShow = false;
  ElNotification.success("操作成功");

  // 刷新树
  const r = await requestTreeData();
  if (r.Type !== 200) {
    ElMessage.error(r.Content);
  }
}

/** 请求左侧树列表数据 */
async function requestTreeData() {
  const r = await Content_Api.getAcuPointTypePageData({});
  if (r.Type === 200) {
    typeList.value = r.Data;
    treeList.value = [
      {
        Name: "全部",
        Children: r.Data,
      },
    ];
  }

  return r;
}

onMounted(async () => {
  const r = await requestTreeData();
  if (r.Type !== 200) {
    ElMessage.error(r.Content);
  }
});

onActivated(() => {
  requestTableList();
});
</script>

<style lang="scss" scoped></style>
