<template>
  <div v-loading="formLoading" class="p-20px overflow-y-auto">
    <el-form
      :ref="kFormRef"
      :model="formData"
      :rules="rules"
      label-width="auto"
      :disabled="props.disabled"
    >
      <el-form-item label="名称" prop="Name">
        <el-input v-model="formData.Name" placeholder="请输入名称" show-word-limit maxlength="25" />
      </el-form-item>
      <el-form-item label="编码" prop="Code">
        <el-input v-model="formData.Code" placeholder="请输入编码" show-word-limit maxlength="10" />
      </el-form-item>
      <el-form-item label="是否启用">
        <el-switch v-model="formData.IsEnable" />
      </el-form-item>
    </el-form>
  </div>
  <!-- 底部按钮 -->
  <div class="flex justify-end">
    <el-button @click="emit('cancel')">取消</el-button>
    <el-button v-if="!props.disabled" type="primary" @click="onSubmitForm()">确定</el-button>
  </div>
</template>

<script setup lang="ts">
import { FormRules, FormInstance, dayjs } from "element-plus";
import { AcupointType, AcuPointTypeInputDTO } from "@/api/content/types";
import Content_Api from "@/api/content";

const kEnableDebug = false;
const kFormRef = "ruleFormRef";

defineOptions({
  name: "AcupointTypeForm",
});

const props = defineProps<{
  data: AcupointType;
  disabled: boolean;
}>();

const emit = defineEmits<{
  cancel: [];
  submit: [];
}>();

onMounted(() => {
  const data = JSON.parse(JSON.stringify(props.data));
  formData.Name = data.Name;
  formData.Code = data.Code;
  formData.IsEnable = data.IsEnable ?? true;
});

const formLoading = ref(false);
/** 表单实例 */
const formRef = useTemplateRef<FormInstance>(kFormRef);
/** 表单数据 */
const formData = reactive<AcuPointTypeInputDTO>({
  Name: "",
  Code: "",
  IsEnable: true,
});

/** 表单验证规则 */
const rules = reactive<FormRules<AcuPointTypeInputDTO>>({
  Name: [{ required: true, message: "请输入名称", trigger: "blur" }],
  Code: [{ required: true, message: "请输入编码", trigger: "blur" }],
});

const formatTime = (time?: string | Date | number) => {
  const format = "YYYY-MM-DD HH:mm:ss";

  // 检查是否为无效时间
  if (!time) {
    return dayjs().format(format);
  }

  // 检查特殊无效时间字符串
  const timeStr = String(time);
  if (timeStr.includes("0001-01-01") || timeStr.includes("1900-01-01")) {
    return dayjs().format(format);
  }

  // 使用dayjs检查时间是否有效
  const dayjsTime = dayjs(time);
  if (!dayjsTime.isValid()) {
    return dayjs().format(format);
  }

  return dayjsTime.format(format);
};

/** 提交表单 */
function onSubmitForm() {
  if (!formRef.value) return;

  formRef.value.validate((valid, fields) => {
    if (valid) {
      requestAddOrUpdateData();
    } else {
      kEnableDebug && console.debug("提交失败", fields);
    }
  });
}

/** 添加/更新数据 */
async function requestAddOrUpdateData() {
  kEnableDebug && console.debug("添加/更新数据", formData);

  formLoading.value = true;
  const params: AcuPointTypeInputDTO = {
    Name: formData.Name,
    Code: formData.Code,
    IsEnable: formData.IsEnable,
  };

  if (props.data.Id) {
    if (!formData.IsEnable) {
      // 编辑时，如果禁用，需查询是否可禁用
      const r = await Content_Api.acuPointTypeIsDisable(props.data.Id);
      if (r.Type !== 200) {
        ElMessage.error(r.Content);
        return;
      }

      if (!r.Data) {
        ElMessageBox.confirm("此分类下有启用的数据不可禁用", {
          confirmButtonText: "确定",
          showCancelButton: false,
          type: "warning",
        }).then(async () => {
          formData.IsEnable = true;
          formLoading.value = false;
        });
        return;
      }
    }

    params.ObjectId = props.data.Id;
    params.CreateTime = formatTime(props.data.CreateTime);
    params.CreatorId = props.data.CreatorId;
  }

  const r = await Content_Api.insertOrUpdateAcuPointType(params);
  formLoading.value = false;
  if (r.Type === 200) {
    emit("submit");
  } else {
    ElMessage.error(r.Content);
  }
}
</script>

<style lang="scss" scoped></style>
