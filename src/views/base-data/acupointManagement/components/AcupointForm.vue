<template>
  <div v-loading="formLoading" class="p-20px overflow-y-auto">
    <el-form
      :ref="kFormRef"
      :model="formData"
      :rules="rules"
      label-width="auto"
      :inline="true"
      :disabled="props.disabled"
    >
      <el-form-item label="类别" prop="AcuPointTypeId">
        <KSelect
          v-model="formData.AcuPointTypeId"
          :data="typeList"
          :props="{ label: 'Name', value: 'Id' }"
          placeholder="请选择类别"
        />
      </el-form-item>
      <el-form-item label="穴位名称" prop="Name">
        <el-input v-model="formData.Name" clearable @blur="autoGeneratePinyin" />
      </el-form-item>

      <el-form-item label="编码" prop="Code">
        <el-input v-model="formData.Code" placeholder="请输入编码" clearable />
      </el-form-item>
      <el-form-item label="拼音码" prop="PYM">
        <el-input v-model="formData.PYM" clearable @blur="upperCasePYM" />
      </el-form-item>
      <el-form-item label="是否双穴">
        <el-switch v-model="formData.IsDouble" />
      </el-form-item>
      <el-form-item label="是否启用">
        <el-switch v-model="formData.IsEnable" />
      </el-form-item>
      <el-row>
        <el-form-item class="flex-1" label="图片" prop="AcuPointImg">
          <MultiImageUpload v-model="formData.AcuPointImg" :disabled="props.disabled" />
        </el-form-item>
      </el-row>
      <el-row>
        <el-form-item class="flex-1" label="说明" prop="Remark">
          <el-input
            v-model="formData.Remark"
            type="textarea"
            maxlength="1000"
            :autosize="{ minRows: 4, maxRows: 10 }"
            show-word-limit
            clearable
          />
        </el-form-item>
      </el-row>
    </el-form>
  </div>
  <!-- 底部按钮 -->
  <div class="flex justify-end">
    <el-button @click="emit('cancel')">取消</el-button>
    <el-button v-if="!props.disabled" type="primary" @click="onSubmitForm()">确定</el-button>
  </div>
</template>

<script setup lang="ts">
import { FormRules, FormInstance, dayjs } from "element-plus";
import { AcuPointInputDTO, AcupointType } from "@/api/content/types";
import Content_Api from "@/api/content";
import { chineseToPinyin } from "@/utils/pinyin-helper";

const kEnableDebug = false;
const kFormRef = "ruleFormRef";

defineOptions({
  name: "AcupointForm",
});

const props = defineProps<{
  data: BaseAcuPoint;
  typeList: AcupointType[];
  disabled: boolean;
}>();

const typeList = computed(() => {
  return props.typeList.map((item) => ({
    ...item,
    disabled: !item.IsEnable,
  }));
});

const emit = defineEmits<{
  cancel: [];
  submit: [];
}>();

onMounted(() => {
  const data = JSON.parse(JSON.stringify(props.data));
  Object.assign(formData, data);
});

const formLoading = ref(false);
/** 表单实例 */
const formRef = useTemplateRef<FormInstance>(kFormRef);
/** 表单数据 */
const formData = reactive<AcuPointInputDTO>({
  Name: "",
  PYM: "",
  Code: "",
  AcuPointTypeId: "",
  AcuPointImg: [],
  IsEnable: true,
  IsDouble: false,
});

/** 表单验证规则 */
const rules = reactive<FormRules<AcuPointInputDTO>>({
  AcuPointTypeId: [{ required: true, message: "请选择类别", trigger: "change" }],
  Name: [{ required: true, message: "请输入穴位名称", trigger: "blur" }],
  Code: [{ required: true, message: "请输入编码", trigger: "blur" }],
  PYM: [{ required: true, message: "请输入拼音码", trigger: "blur" }],
  AcuPointImg: [{ required: true, message: "请上传图片", trigger: "change" }],
});

/** 拼音码改为大写 */
function upperCasePYM() {
  formData.PYM = formData.PYM.toUpperCase();
}

/** 拼音码自动生成 */
function autoGeneratePinyin() {
  if (formData.Name && !formData.PYM.trim()) {
    formData.PYM = chineseToPinyin(formData.Name);
  }
}

/** 提交表单 */
function onSubmitForm() {
  if (!formRef.value) return;

  formRef.value.validate((valid, fields) => {
    if (valid) {
      if (formData.AcuPointImg.length === 0) {
        ElMessage.warning("请上传图片");
        return;
      }

      // 检查类别是否启用
      if (formData.IsEnable) {
        const type = typeList.value.find((item) => item.Id === formData.AcuPointTypeId);
        if (!type?.IsEnable) {
          ElMessageBox.confirm("所属类别未启用，故此条数据不可启用", {
            confirmButtonText: "确定",
            type: "warning",
          }).then(() => {
            formData.IsEnable = false;
          });
          return;
        }
      }

      requestAddOrUpdateData();
    } else {
      kEnableDebug && console.debug("提交失败", fields);
    }
  });
}

/** 添加/更新数据 */
async function requestAddOrUpdateData() {
  kEnableDebug && console.debug("添加/更新数据", formData);

  formLoading.value = true;
  const params: AcuPointInputDTO = {
    Name: formData.Name,
    PYM: formData.PYM,
    Code: formData.Code,
    AcuPointTypeId: formData.AcuPointTypeId,
    AcuPointImg: formData.AcuPointImg,
    Remark: formData.Remark,
    IsEnable: formData.IsEnable,
    IsDouble: formData.IsDouble,
    CreateTime: formData.CreateTime ? formData.CreateTime : dayjs().format("YYYY-MM-DD HH:mm:ss"),
    CreatorId: formData.CreatorId,
    OrganizationId: formData.OrganizationId,
  };
  if (props.data.Id) {
    params.ObjectId = props.data.Id;
  }
  const r = await Content_Api.insertOrUpdateAcuPoint(params);
  formLoading.value = false;
  if (r.Type === 200) {
    emit("submit");
  } else {
    ElMessage.error(r.Content);
  }
}
</script>

<style lang="scss" scoped></style>
