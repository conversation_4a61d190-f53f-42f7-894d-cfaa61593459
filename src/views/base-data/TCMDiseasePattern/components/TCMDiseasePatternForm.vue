<template>
  <div v-loading="formLoading" class="p-20px overflow-y-auto">
    <el-form
      :ref="kFormRef"
      :model="formData"
      :rules="rules"
      label-width="auto"
      :disabled="props.disabled"
      class="self-stretch"
    >
      <el-row>
        <el-col :span="8">
          <el-form-item label="名称" prop="Key">
            <el-input
              v-model="formData.Key"
              placeholder="请输入名称"
              :disabled="formData.IsPublish"
              maxlength="25"
              show-word-limit
              clearable
            />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="编码" prop="Value">
            <el-input
              v-model="formData.Value"
              placeholder="请输入编码"
              :disabled="formData.IsPublish"
              maxlength="10"
              show-word-limit
              clearable
            />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item class="ml-20px" label="是否启用" prop="IsEnabled">
            <el-switch v-model="formData.IsEnabled" />
          </el-form-item>
        </el-col>
      </el-row>
      <el-form-item label="备注" prop="Remark">
        <el-input
          v-model="formData.Remark"
          placeholder="请输入备注"
          type="textarea"
          :autosize="{ minRows: 5, maxRows: 10 }"
          :disabled="formData.IsPublish"
          clearable
        />
      </el-form-item>
    </el-form>
    <TableTransfer
      v-model:pending-list="syndromeData"
      v-model:selected-list="selectedData"
      :pending-loading="syndromeDataLoading"
      :pending-search-config="{
        placeholder: '请输入症候名称',
        searchMethod: handleFilterTCMSyndromeData,
      }"
      :pending-page-config="{
        pageChangeMethod: handleFilterTCMSyndromeData,
        pendingTotal: syndromeDataTotal,
      }"
      :selected-loading="selectedDataLoading"
      :disabled="props.disabled"
    >
      <template #pending>
        <el-table-column property="Value" label="编码" />
        <el-table-column property="Key" label="名称" />
      </template>
      <template #selected>
        <el-table-column property="Value" label="编码" />
        <el-table-column property="Key" label="名称" />
      </template>
    </TableTransfer>
  </div>
  <!-- 底部按钮 -->
  <div class="flex justify-end">
    <el-button @click="emit('cancel')">取消</el-button>
    <el-button v-if="!props.disabled" type="primary" @click="onSubmitForm()">确定</el-button>
  </div>
</template>

<script setup lang="ts">
import { FormRules, FormInstance } from "element-plus";
import { DictCreateUpdateInputDTO } from "@/api/dictionary/types";
import Dictionary_Api from "@/api/dictionary";
import { useDictData } from "@/hooks/useDictData";

const { requestBaseDictId, requestDictDataList } = useDictData();

const kEnableDebug = false;
const kFormRef = "ruleFormRef";

defineOptions({
  name: "TCMDiseasePatternForm",
});

const props = defineProps<{
  data: ReadDict;
  disabled: boolean;
}>();

const emit = defineEmits<{
  cancel: [];
  submit: [];
}>();

const formLoading = ref(false);
/** 表单实例 */
const formRef = useTemplateRef<FormInstance>(kFormRef);
/** 表单数据 */
const formData = reactive<DictCreateUpdateInputDTO>({
  Key: "",
  PinyinCode: "",
  IsEnabled: true,
  DictId: -1,
});

/** 表单验证规则 */
const rules = reactive<FormRules<DictCreateUpdateInputDTO>>({
  Key: [{ required: true, message: "请输入名称", trigger: "blur" }],
  Value: [{ required: true, message: "请输入编码", trigger: "blur" }],
});

/** 提交表单 */
function onSubmitForm() {
  if (!formRef.value) return;

  formRef.value.validate((valid, fields) => {
    if (valid) {
      requestAddOrUpdateData();
    } else {
      kEnableDebug && console.debug("提交失败", fields);
    }
  });
}

/** 添加/更新数据 */
async function requestAddOrUpdateData() {
  kEnableDebug && console.debug("添加/更新数据", formData);

  formLoading.value = true;
  const params: DictCreateUpdateInputDTO = {
    Key: formData.Key,
    Value: formData.Value,
    OrgId: formData.OrgId,
    IsPublish: formData.IsPublish,
    PinyinCode: formData.PinyinCode,
    Remark: formData.Remark,
    IsEnabled: formData.IsEnabled,
    ParentId: formData.ParentId,
    DictId: formData.DictId,
    DictItemRelateds: selectedData.value.map((e) => ({
      DictItemIdA: e.Id ?? "",
    })),
  };
  let r: ServerResult;
  if (formData.Id) {
    params.Id = formData.Id;
    params.CreatedTime = formData.CreatedTime;
    r = await Dictionary_Api.updateDict([params]);
  } else {
    r = await Dictionary_Api.createDict([params]);
  }
  formLoading.value = false;

  if (r.Type === 200) {
    emit("submit");
  } else {
    ElMessage.error(r.Content);
  }
}

/** 中医症候基础数据 */
const syndromeData = ref<ReadDict[]>([]);
const syndromeDataLoading = ref(false);

/** 中医症候基础数据总条数 */
const syndromeDataTotal = ref(0);

/**
 * 请求待选中医症候基础数据
 */
async function requestTCMSyndromeData(data: { query?: string; pageIndex: number }) {
  syndromeDataLoading.value = true;
  const rules: Rule[] = [
    {
      Field: "IsEnabled",
      Value: true,
      Operate: 3,
    },
    {
      Field: "IsPublish",
      Value: true,
      Operate: 3,
    },
  ];
  let groupsRules: Rule[] = [];
  if (data.query) {
    groupsRules = [
      {
        Field: "Key",
        Value: data.query,
        Operate: 11,
      },
      {
        Field: "Value",
        Value: data.query,
        Operate: 11,
      },
    ];
  }
  const filterGroup: FilterGroup = {
    Rules: rules,
    Groups: [
      {
        Rules: groupsRules,
        Operate: 2,
      },
    ],
    Operate: 1,
  };
  const params: DictQueryParams = {
    PageCondition: {
      PageIndex: data.pageIndex,
      PageSize: 100,
      SortConditions: [
        {
          SortField: "CreatedTime",
          ListSortDirection: 1,
        },
      ],
    },
    FilterGroup: filterGroup,
  };
  const r = await requestDictDataList("CMSymptomsDict", params);
  syndromeDataLoading.value = false;

  if (r.Type === 200) {
    syndromeData.value = r.Data.Rows;
    syndromeDataTotal.value = r.Data.Total;
  }

  return r;
}

/**
 * 点击搜索中医症候数据，或切换中医症候分页
 */
async function handleFilterTCMSyndromeData(params: { keyword?: string; pageIndex: number }) {
  formLoading.value = true;
  const r = await requestTCMSyndromeData({
    query: params.keyword,
    pageIndex: params.pageIndex,
  });
  formLoading.value = false;
  if (r.Type !== 200) {
    ElMessage.error(r.Content);
    return;
  }
}

/** 选中中医症候基础数据 */
const selectedData = ref<ReadDict[]>([]);
const selectedDataLoading = ref(false);

/**
 * 请求选中的中医症候基础数据
 */
async function requestSelectedTCMSyndromeData() {
  selectedDataLoading.value = true;
  const ids = props.data.DictItemRelateds?.map((e) => e.DictItemIdA) ?? [];
  if (ids.length === 0) {
    return {
      Type: 200,
      Content: "",
      Data: {
        Total: 0,
        Rows: [],
      },
    };
  }

  const rules: Rule[] = [
    {
      Field: "IsEnabled",
      Value: true,
      Operate: 3,
    },
    {
      Field: "IsPublish",
      Value: true,
      Operate: 3,
    },
  ];
  const filterGroup: FilterGroup = {
    Rules: rules,
    Groups: [
      {
        Rules: ids.map((id) => ({
          Field: "Id",
          Value: id,
          Operate: 3,
        })),
        Operate: 2,
      },
    ],
    Operate: 1,
  };
  const params: DictQueryParams = {
    PageCondition: {
      PageIndex: 1,
      PageSize: 1000,
      SortConditions: [
        {
          SortField: "CreatedTime",
          ListSortDirection: 1,
        },
      ],
    },
    FilterGroup: filterGroup,
  };
  const r = await requestDictDataList("CMSymptomsDict", params);
  selectedDataLoading.value = false;
  if (r.Type === 200) {
    selectedData.value = r.Data.Rows;
  }

  return r;
}

onMounted(async () => {
  const data = JSON.parse(JSON.stringify(props.data));
  Object.assign(formData, data);

  // 先获取dictId
  formLoading.value = true;
  const r = await requestBaseDictId("CMSymptomsDict");
  if (r.Type !== 200) {
    formLoading.value = false;
    ElMessage.error(r.Content);
    return;
  }

  // 请求中医症候列表及选中列表
  const rs = await Promise.all([
    requestTCMSyndromeData({ pageIndex: 1 }),
    requestSelectedTCMSyndromeData(),
  ]);
  formLoading.value = false;
  const failed = rs.find((r) => r.Type !== 200);
  if (failed) {
    ElMessage.error(failed.Content);
    return;
  }
});
</script>

<style lang="scss" scoped>
.header-wrapper {
  display: flex;
  align-items: center;
  width: 100%;
  height: 40px;
  padding: 0 10px;
  border-top: 1px solid #e4e7ed;
  border-right: 1px solid #e4e7ed;
  border-left: 1px solid #e4e7ed;
}

.pagination-wrapper {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  padding: 4px 0;
  border-right: 1px solid #e4e7ed;
  border-bottom: 1px solid #e4e7ed;
  border-left: 1px solid #e4e7ed;
}
</style>
