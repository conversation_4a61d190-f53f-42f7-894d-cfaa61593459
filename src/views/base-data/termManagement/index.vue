<template>
  <el-container class="w-full h-full">
    <!-- 左侧目录 -->
    <el-aside width="230px" class="p-10px">
      <el-tree
        class="w-full h-full p-10px"
        :data="treeList"
        :props="defaultTreeProps"
        node-key="Id"
        :current-node-key="queryParams.DictId"
        highlight-current
        default-expand-all
        @node-click="onTreeClick"
      />
    </el-aside>
    <el-main class="p-10px!">
      <BaseTableSearchContainer @size-changed="tableResize">
        <!-- 顶部筛选条件 -->
        <template #search>
          <TBSearchContainer :is-show-toggle="true">
            <template #left>
              <el-form :model="queryParams" label-position="right" :inline="true">
                <el-form-item label="关键字">
                  <el-input
                    v-model="queryParams.Keyword"
                    placeholder="请输入"
                    clearable
                    @keyup.enter="handleQuery"
                  />
                </el-form-item>
                <el-form-item label="是否启用" class="w-180px">
                  <KSelect
                    v-model="queryParams.IsEnabled"
                    :data="[
                      { label: '是', value: true },
                      { label: '否', value: false },
                    ]"
                    :show-all="true"
                  />
                </el-form-item>
                <el-form-item class="w-180px" label="是否发布">
                  <KSelect
                    v-model="queryParams.IsPublish"
                    :data="[
                      { label: '是', value: true },
                      { label: '否', value: false },
                    ]"
                    :show-all="true"
                  />
                </el-form-item>
                <el-form-item
                  v-if="queryParams.DictId === '31'"
                  class="w-180px"
                  label="是否默认推送"
                >
                  <KSelect
                    v-model="queryParams.IsDefaultPush"
                    :data="[
                      { label: '是', value: true },
                      { label: '否', value: false },
                    ]"
                    :show-all="true"
                  />
                </el-form-item>
              </el-form>
            </template>
            <template #right>
              <el-button type="primary" icon="search" @click="handleQuery">查询</el-button>
              <el-button type="primary" @click="onAddItem">添加</el-button>
            </template>
          </TBSearchContainer>
        </template>
        <!-- 列表 -->
        <template #table>
          <el-table
            v-loading="tableLoading"
            :data="pageData"
            :total="total"
            row-key="Id"
            :height="tableFluidHeight"
            :header-cell-style="{ textAlign: 'center' }"
            :cell-style="{ textAlign: 'center' }"
            border
            highlight-current-row
          >
            <el-table-column prop="Key" min-width="150" label="词条名称" />
            <el-table-column prop="PinyinCode" min-width="100" label="拼音码" />
            <el-table-column
              prop="OrderNumber"
              :label="queryParams.DictId === '31' ? '使用次数' : '排序'"
              width="100"
            />
            <el-table-column label="是否发布" width="80">
              <template #default="scope">
                {{ scope.row.IsPublish ? "是" : "否" }}
              </template>
            </el-table-column>
            <el-table-column label="是否启用" width="80" align="center">
              <template #default="scope">
                {{ scope.row.IsEnabled ? "是" : "否" }}
              </template>
            </el-table-column>
            <el-table-column
              prop="CreatedTime"
              label="创建时间"
              :formatter="tableDateFormat"
              min-width="120"
            />
            <el-table-column
              v-if="queryParams.DictId === '31'"
              prop="IsDefaultPush"
              label="是否默认推送"
              width="80"
            >
              <template #default="scope">
                {{ scope.row.IsDefaultPush ? "是" : "否" }}
              </template>
            </el-table-column>
            <el-table-column fixed="right" label="操作" width="180">
              <template #default="scope">
                <el-button link type="primary" @click="onPreviewOrEdit(scope.row, true)">
                  查看
                </el-button>
                <el-button link type="primary" @click="onPreviewOrEdit(scope.row, false)">
                  编辑
                </el-button>
                <el-button
                  v-if="!scope.row.IsPublish"
                  link
                  type="primary"
                  @click="onPublish(scope.row)"
                >
                  发布
                </el-button>
                <el-button
                  v-if="!scope.row.IsPublish"
                  link
                  type="primary"
                  @click="onDelete(scope.row)"
                >
                  删除
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </template>
        <!-- 分页 -->
        <template #pagination>
          <Pagination
            v-if="total > 0"
            v-model:total="total"
            v-model:page="queryParams.PageIndex"
            v-model:limit="queryParams.PageSize"
            @pagination="requestTableList"
          />
        </template>
      </BaseTableSearchContainer>
    </el-main>
  </el-container>

  <!-- 添加/编辑/查看 -->
  <el-dialog
    v-model="showDataDialog.isShow"
    :title="showDataDialog.title"
    width="500"
    destroy-on-close
    @close="showDataDialog.isShow = false"
  >
    <TermForm
      :data="showDataDialog.data"
      :type-list="typeList"
      :disabled="showDataDialog.disabled"
      @cancel="showDataDialog.isShow = false"
      @submit="onConfirmSubmitItem"
    />
  </el-dialog>
</template>

<script setup lang="ts">
import { useTableConfig } from "@/hooks/useTableConfig";
import Dictionary_Api from "@/api/dictionary";
import TermForm from "./components/TermForm.vue";

interface TreeNode extends DictCode {
  Children?: TreeNode[];
}

interface QueryParams {
  IsEnabled?: boolean;
  IsPublish?: boolean;
  IsDefaultPush?: boolean;
  Keyword?: string;
  DictId: string;
  PageIndex: number;
  PageSize: number;
}

/** 调试开关 */
const kEnableDebug = false;
defineOptions({
  name: "TermManagement",
});

const { pageData, tableLoading, tableFluidHeight, total, tableResize, tableDateFormat } =
  useTableConfig<ReadDict>();

/** 查询条件 */
const queryParams = reactive<QueryParams>({
  PageIndex: 1,
  PageSize: 20,
  DictId: "",
});

/** 点击搜索 */
function handleQuery() {
  queryParams.PageIndex = 1;
  requestTableList();
}

/** 查看/添加/编辑弹窗 */
const showDataDialog = reactive({
  isShow: false,
  title: "",
  disabled: false,
  data: {} as ReadDict, // 查看/添加/编辑详情
});

/** 点击添加 */
function onAddItem() {
  kEnableDebug && console.debug("点击添加");

  if (!queryParams.DictId) {
    ElMessage.warning("请先选择分类");
    return;
  }

  showDataDialog.title = "新增";
  showDataDialog.disabled = false;
  showDataDialog.data = {
    DictId: Number(queryParams.DictId),
  };
  showDataDialog.isShow = true;
}

/** 点击查看/编辑 */
async function onPreviewOrEdit(row: ReadDict, disabled: boolean = false) {
  kEnableDebug && console.debug("查看/编辑", row, disabled);

  showDataDialog.title = disabled ? "查看" : "编辑";
  showDataDialog.disabled = disabled;
  showDataDialog.data = row;
  showDataDialog.isShow = true;
}

/** 确定新增提交 */
function onConfirmSubmitItem() {
  kEnableDebug && console.debug("确定提交");

  // 提交成功
  showDataDialog.isShow = false;
  ElNotification.success("提交成功");

  // 刷新列表
  requestTableList();
}

/** 发布 */
function onPublish(row: ReadDict) {
  kEnableDebug && console.debug("发布", row);

  if (!row.Id) {
    ElMessage.error("id为空");
    return;
  }

  ElMessageBox.confirm("发布该条数据, 是否继续?", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(async () => {
    const r = await Dictionary_Api.publishDict({ id: row.Id! });
    if (r.Type !== 200) {
      ElMessage.warning(r.Content);
      return;
    }

    ElMessage.success("发布成功");
    requestTableList();
  });
}

/** 删除 */
function onDelete(row: ReadDict) {
  kEnableDebug && console.debug("删除", row);

  if (row.IsPublish) {
    ElMessage.warning("已发布, 不能删除");
    return;
  }

  if (!row.Id) {
    ElMessage.warning("id为空");
    return;
  }

  ElMessageBox.confirm("此操作将删除该条数据, 是否继续?", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(async () => {
    const r = await Dictionary_Api.deleteDict1({ id: row.Id! });
    if (r.Type !== 200) {
      ElMessage.warning(r.Content);
      return;
    }

    ElMessage.success("删除成功");
    requestTableList();
  });
}

/** 请求列表数据 */
async function requestTableList() {
  tableLoading.value = true;
  const rules: Rule[] = [{ Field: "OrgId", Value: null, Operate: 3 }];
  if (queryParams.DictId) {
    rules.push({ Field: "DictId", Value: queryParams.DictId, Operate: 3 });
  }
  if (queryParams.IsEnabled !== undefined) {
    rules.push({ Field: "IsEnabled", Value: queryParams.IsEnabled, Operate: 3 });
  }
  if (queryParams.IsPublish !== undefined) {
    rules.push({ Field: "IsPublish", Value: queryParams.IsPublish, Operate: 3 });
  }
  if (queryParams.IsDefaultPush !== undefined) {
    rules.push({ Field: "IsDefaultPush", Value: queryParams.IsDefaultPush, Operate: 3 });
  }
  let groupsRules: Rule[] = [];
  if (queryParams.Keyword) {
    groupsRules = [
      { Field: "Key", Value: queryParams.Keyword, Operate: 11 },
      { Field: "Value", Value: queryParams.Keyword, Operate: 11 },
      { Field: "PinyinCode", Value: queryParams.Keyword, Operate: 11 },
    ];
  }
  const params: DictQueryParams = {
    PageCondition: {
      PageIndex: queryParams.PageIndex,
      PageSize: queryParams.PageSize,
      SortConditions: [
        { SortField: "OrderNumber", ListSortDirection: 0 },
        { SortField: "CreatedTime", ListSortDirection: 1 },
      ],
    },
    FilterGroup: {
      Rules: rules,
      Groups: [
        {
          Rules: groupsRules,
          Operate: 2,
        },
      ],
      Operate: 1,
    },
  };
  const r = await Dictionary_Api.itemReadStd(params);
  tableLoading.value = false;
  if (r.Type !== 200) {
    ElMessage.error(r.Content);
    return;
  }

  pageData.value = r.Data.Rows;
  total.value = r.Data.Total;
}

/** 左侧树列表数据结构 */
const treeList = ref<TreeNode[]>([]);
const defaultTreeProps = reactive({
  label: "Name",
  children: "Children",
});

const typeList = ref<DictCode[]>([]);

/** 树点击事件 */
async function onTreeClick(data: TreeNode) {
  kEnableDebug && console.debug("树点击事件", data);
  if (!data.Id) {
    return;
  }

  if (data.Id === "31") {
    queryParams.IsDefaultPush = undefined;
  }
  queryParams.DictId = data.Id;
  queryParams.PageIndex = 1;

  requestTableList();
}

/** 请求左侧树列表数据 */
async function requestTreeData() {
  const params: DictQueryParams = {
    PageCondition: {
      PageIndex: 1,
      PageSize: 999,
      SortConditions: [{ SortField: "Id", ListSortDirection: 1 }],
    },
    FilterGroup: {
      Rules: [],
      Groups: [
        {
          Rules: [
            { Field: "Code", Value: "PastHistory", Operate: 11 },
            { Field: "Code", Value: "AllergyHistory", Operate: 11 },
            { Field: "Code", Value: "Skilled", Operate: 11 },
            { Field: "Code", Value: "EvaluateTag", Operate: 11 },
            { Field: "Code", Value: "TrainingTag", Operate: 11 },
            { Field: "Code", Value: "DiseaseTag", Operate: 11 },
            { Field: "Code", Value: "RefundReason", Operate: 11 },
          ],
          Operate: 2,
        },
      ],
      Operate: 1,
    },
  };
  const r = await Dictionary_Api.readStd(params);
  if (r.Type === 200) {
    typeList.value = r.Data.Rows;
    treeList.value = [
      {
        Name: "全部",
        Children: r.Data.Rows,
      },
    ];
    queryParams.DictId = r.Data.Rows[0].Id ?? "";
  }

  return r;
}

onActivated(async () => {
  if (!queryParams.DictId) {
    tableLoading.value = true;
    const r = await requestTreeData();
    if (r.Type !== 200) {
      tableLoading.value = false;
      ElMessage.error(r.Content);
      return;
    }
  }

  requestTableList();
});
</script>

<style lang="scss" scoped></style>
