<template>
  <div v-loading="formLoading" class="p-20px overflow-y-auto">
    <el-form
      :ref="kFormRef"
      :model="formData"
      :rules="rules"
      label-width="auto"
      :disabled="props.disabled"
    >
      <el-form-item label="分类" prop="DictId">
        <KSelect
          v-model="dictId"
          :data="props.typeList"
          :props="{ label: 'Name', value: 'Id' }"
          placeholder="请选择分类"
          :disabled="formData.IsPublish"
        />
      </el-form-item>
      <el-form-item label="词条名称" prop="Key">
        <el-input
          v-model="formData.Key"
          placeholder="请输入词条名称"
          :disabled="formData.IsPublish"
          clearable
          @blur="autoGeneratePinyinCode"
        />
      </el-form-item>
      <el-form-item label="拼音码" prop="PinyinCode">
        <el-input
          v-model="formData.PinyinCode"
          placeholder="请输入拼音码"
          :disabled="formData.IsPublish"
          clearable
        />
      </el-form-item>
      <el-form-item :label="formData.DictId === 31 ? '使用次数' : '排序'" prop="OrderNumber">
        <el-input-number
          v-model.number="formData.OrderNumber"
          :placeholder="formData.DictId === 31 ? '请输入使用次数' : '请输入排序'"
          clearable
        />
      </el-form-item>
      <el-form-item label="是否启用" prop="IsEnabled">
        <el-switch v-model="formData.IsEnabled" />
      </el-form-item>
      <el-form-item v-if="formData.DictId === 31" label="是否默认推送" prop="IsDefaultPush">
        <el-switch v-model="formData.IsDefaultPush" />
      </el-form-item>
    </el-form>
  </div>
  <!-- 底部按钮 -->
  <div class="flex justify-end">
    <el-button @click="emit('cancel')">取消</el-button>
    <el-button v-if="!props.disabled" type="primary" @click="onSubmitForm()">确定</el-button>
  </div>
</template>

<script setup lang="ts">
import { FormRules, FormInstance } from "element-plus";
import Dictionary_Api from "@/api/dictionary";
import { DictCreateUpdateInputDTO } from "@/api/dictionary/types";
import { chineseToPinyin } from "@/utils/pinyin-helper";

const kEnableDebug = false;
const kFormRef = "ruleFormRef";

defineOptions({
  name: "TermForm",
});

const props = defineProps<{
  data: ReadDict;
  typeList: DictCode[];
  disabled: boolean;
}>();

const emit = defineEmits<{
  cancel: [];
  submit: [];
}>();

onMounted(() => {
  const data = JSON.parse(JSON.stringify(props.data));
  Object.assign(formData, data);
  dictId.value = data.DictId.toString();
});

const formLoading = ref(false);
/** 表单实例 */
const formRef = useTemplateRef<FormInstance>(kFormRef);
/** 表单数据 */
const formData = reactive<DictCreateUpdateInputDTO>({
  Key: "",
  PinyinCode: "",
  IsEnabled: true,
  IsDefaultPush: false,
  DictId: 0,
  OrderNumber: 0,
});

/** 分类 */
const dictId = ref<string>("");
watch(dictId, (v) => {
  formData.DictId = Number(v);
});

/** 表单验证规则 */
const rules = reactive<FormRules<DictCreateUpdateInputDTO>>({
  DictId: [{ required: true, message: "请选择分类", trigger: "change" }],
  Key: [{ required: true, message: "请输入词条名称", trigger: "blur" }],
  PinyinCode: [{ required: true, message: "请输入拼音码", trigger: "blur" }],
  OrderNumber: [
    {
      required: true,
      message: `请输入${formData.DictId === 31 ? "使用次数" : "排序"}`,
      trigger: "blur",
    },
  ],
});

/** 自动生成拼音码 */
function autoGeneratePinyinCode() {
  if (!formData.Key.trim()) return;
  formData.PinyinCode = chineseToPinyin(formData.Key);
}

/** 提交表单 */
function onSubmitForm() {
  if (!formRef.value) return;

  formRef.value.validate((valid, fields) => {
    if (valid) {
      requestAddOrUpdateData();
    } else {
      kEnableDebug && console.debug("提交失败", fields);
    }
  });
}

/** 添加/更新数据 */
async function requestAddOrUpdateData() {
  kEnableDebug && console.debug("添加/更新数据", formData);

  formLoading.value = true;
  const params: DictCreateUpdateInputDTO = {
    Key: formData.Key,
    Value: formData.Key,
    OrgId: null,
    Remark: "",
    ParentId: "",
    DictItemRelateds: [],
    PinyinCode: formData.PinyinCode,
    OrderNumber: formData.OrderNumber,
    IsEnabled: formData.IsEnabled,
    IsPublish: formData.IsPublish,
    IsDefaultPush: formData.IsDefaultPush,
    DictId: formData.DictId,
  };
  let r: ServerResult;
  if (props.data.Id) {
    params.Id = props.data.Id;
    r = await Dictionary_Api.updateDict([params]);
  } else {
    r = await Dictionary_Api.createDict([params]);
  }
  formLoading.value = false;
  if (r.Type === 200) {
    emit("submit");
  } else {
    ElMessage.error(r.Content);
  }
}
</script>

<style lang="scss" scoped></style>
