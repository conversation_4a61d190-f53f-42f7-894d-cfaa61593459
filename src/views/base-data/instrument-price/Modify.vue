<template>
  <div class="InstrumentDictModify">
    <el-tabs type="card" @tab-click="onTabClick">
      <el-tab-pane label="基本信息">
        <el-form
          v-show="tabIndex === '0'"
          ref="baseFormRef"
          :model="info"
          :rules="rules"
          inline
          label-width="auto"
          :disabled="isDisable"
        >
          <el-form-item label="设备类型名称" prop="InstrumentId">
            <el-select
              v-model="info.InstrumentId"
              placeholder="请选择"
              class="w150"
              filterable
              clearable
              @change="onChangeDevice"
            >
              <el-option
                v-for="item in baseDeviceList"
                :key="item.InstrumentId"
                :label="item.Name"
                :value="item.InstrumentId"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="拼音码">
            <el-input v-model="info.PYM" type="text" class="w150" :disabled="true" />
          </el-form-item>
          <el-form-item label="编码">
            <el-input v-model="info.Code" type="text" class="w150" :disabled="true" />
          </el-form-item>
          <el-form-item label="设备类别">
            <el-select
              v-model="info.DeviceType"
              class="w150"
              clearable
              placeholder="分类"
              filterable
              :disabled="true"
            >
              <el-option
                v-for="(item, index) in deviceTypeData"
                :key="'DT' + index"
                :label="item.Key"
                :value="item.Id"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="设备是否启用">
            <div class="w150">
              <el-switch v-model="info.IsEnable" :disabled="true" />
            </div>
          </el-form-item>
          <el-form-item label="物联设备">
            <div class="w150">
              <el-switch v-model="info.IsIOTDevice" :disabled="true" />
            </div>
          </el-form-item>
          <div v-if="info.IsIOTDevice" class="Parameters">
            <el-form-item label="设备厂商">
              <el-select
                v-model="info.DeviceFactory"
                class="w150"
                clearable
                placeholder="分类"
                filterable
                :disabled="true"
                @change="$forceUpdate()"
              >
                <el-option
                  v-for="(item, index) in deviceFactoryData"
                  :key="index"
                  :label="item.Key"
                  :value="item.Value"
                />
              </el-select>
            </el-form-item>
          </div>
          <div
            v-for="(item, index) in parameterList"
            v-if="info.IsIOTDevice"
            :key="'param' + index"
            class="Parameters"
          >
            <el-form-item label="参数名称">
              <el-input v-model="item.Name" type="text" class="w150" :disabled="true" />
            </el-form-item>
            <el-form-item label="字段标记">
              <el-input v-model="item.SignCode" type="text" class="w150" :disabled="true" />
            </el-form-item>
            <el-form-item label="参数类型">
              <el-select
                v-model="item.Type"
                class="w150"
                placeholder="分类"
                filterable
                :disabled="true"
              >
                <el-option
                  v-for="(paramType, paramIndex) in paramTypeData"
                  :key="'pt' + paramIndex"
                  :label="paramType.Key"
                  :value="paramType.Id"
                />
              </el-select>
            </el-form-item>
            <el-form-item v-if="item.Type === 1" label="选项值">
              <el-input
                v-model="item.OptionValue"
                type="text"
                class="w410"
                placeholder="每个选项值用 ‘,’ 分隔"
                :disabled="true"
              />
              <span style="color: #ddd">（如：模式一:1,模式二:2）</span>
            </el-form-item>
            <el-form-item v-if="item.Type === 0" label="参数单位">
              <el-input v-model="item.Unit" type="text" class="w150" :disabled="true" />
            </el-form-item>
          </div>
        </el-form>
      </el-tab-pane>
      <el-tab-pane label="设备租金">
        <el-form
          v-show="tabIndex === '1'"
          ref="rentFormRef"
          :model="rentFormModel"
          :rules="rentFormRules"
          inline
          :disabled="isDisable"
          label-width="auto"
          @submit.prevent="addRend"
        >
          <el-form-item label="收费项目" prop="ChargeItemId">
            <el-select
              v-model="rentFormModel.ChargeItemId"
              filterable
              remote
              :remote-method="searchChargeItem"
              :loading="loading"
              class="w150"
              placeholder="输入关键字筛选"
            >
              <el-option
                v-for="(item, index) in rentBaseChargeItemList"
                :key="index"
                :label="item.Name"
                :value="item.Id"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="数量" prop="Quantity">
            <el-input v-model="rentFormModel.Quantity" type="number" class="w150" />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" native-type="submit">添加</el-button>
          </el-form-item>
        </el-form>
        <el-table
          height="300px"
          :data="rentList"
          fit
          border
          :header-cell-style="{ 'text-align': 'center' }"
          :cell-style="{ 'text-align': 'center' }"
        >
          <el-table-column prop="Name" label="名称" />
          <el-table-column prop="Code" label="编码" />
          <el-table-column prop="Quantity" label="数量" />
          <el-table-column prop="Unit" label="单位" />
          <el-table-column prop="Amount" label="价格" />
          <el-table-column fixed="right" label="操作" width="180">
            <template #default="{ row }">
              <el-button :disabled="isDisable" link @click="deleteRend(row)">删除</el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-tab-pane>
      <el-tab-pane label="设备押金">
        <el-form
          v-show="tabIndex === '2'"
          ref="depositFormRef"
          :model="depositFormModel"
          :rules="depositFormRules"
          inline
          :disabled="isDisable"
          label-width="auto"
          @submit.prevent="addDeposit"
        >
          <el-form-item label="收费项目" prop="ChargeItemId">
            <el-select
              v-model="depositFormModel.ChargeItemId"
              filterable
              remote
              :remote-method="searchChargeItem"
              :loading="loading"
              placeholder="输入关键字筛选"
            >
              <el-option
                v-for="(item, index) in depositBaseChargeItemList"
                :key="index"
                :label="item.Name"
                :value="item.Id"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="数量" prop="Quantity">
            <el-input v-model="depositFormModel.Quantity" type="number" />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" native-type="submit">添加</el-button>
          </el-form-item>
        </el-form>
        <el-table
          height="300px"
          :data="depositList"
          fit
          border
          :header-cell-style="{ 'text-align': 'center' }"
          :cell-style="{ 'text-align': 'center' }"
        >
          <el-table-column prop="Name" label="名称" />
          <el-table-column prop="Code" label="编码" />
          <el-table-column prop="Quantity" label="数量" />
          <el-table-column prop="Unit" label="单位" />
          <el-table-column prop="Amount" label="价格" />
          <el-table-column fixed="right" label="操作" width="180">
            <template #default="{ row }">
              <el-button :disabled="isDisable" link @click="deleteDeposit(row)">删除</el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-tab-pane>
    </el-tabs>
    <div v-if="hasFooter" class="text-right mt-4">
      <el-button size="small" @click="close">取 消</el-button>
      <el-button size="small" type="primary" @click="submit">确 定</el-button>
    </div>
  </div>
</template>
<script setup lang="ts">
import Content_Api from "@/api/content";
import { OperateInstrumentInputDTO } from "@/api/content/types";
import { FormInstance, FormRules, TabsPaneContext } from "element-plus";

type Oper = "view" | "edit" | "add";

interface _ChargeItem extends BaseChargeItemType {
  Id: string;
  Name: string;
  Code?: string;
  Quantity: number;
  Unit: string;
  Amount: number;
}

interface _DeviceParameter {
  Name: string;
  Type: number;
  Value: string;
  OptionValue: string | null;
  Unit: string;
  SignCode: string;
}

const props = defineProps({
  data: {
    type: Object as PropType<BaseInstrument>,
    default: () => ({}),
  },
  oper: {
    type: String as PropType<Oper>,
    default: "view",
  },
});

const emit = defineEmits<{
  (e: "success"): void;
  (e: "close"): void;
}>();

/** 设备厂商 */
const deviceFactoryData = [
  {
    Id: 1,
    Key: "VR",
    Value: "VR治疗仪",
  },
  {
    Id: 2,
    Key: "OA治疗仪",
    Value: "OA治疗仪",
  },
  {
    Id: 3,
    Key: "功率车",
    Value: "PowerCar",
  },
];
/** 设备类型 */
const deviceTypeData = [
  {
    Id: 0,
    Key: "治疗",
  },
  {
    Id: 1,
    Key: "评定",
  },
];
/** 参数选项 */
const paramTypeData = [
  {
    Id: 0,
    Key: "数值",
  },
  {
    Id: 1,
    Key: "选择",
  },
];

/**是否正在提交 */
const isSubmit = ref(false);
const hasFooter = computed(() => props.oper !== "view");
const isDisable = computed(() => props.oper === "view" || isSubmit.value);

const rules: FormRules = {
  InstrumentId: [
    {
      type: "string",
      required: true,
      message: "请选择设备类型名称",
      trigger: "change",
    },
  ],
};
const baseForm = useTemplateRef<FormInstance>("baseFormRef");
const rentForm = useTemplateRef<FormInstance>("rentFormRef");
const depositForm = useTemplateRef<FormInstance>("depositFormRef");
const tabIndex = ref("0");

const getInitData = () => {
  return {
    Name: "",
    PYM: "",
    Code: "",
    InstrumentId: "",
    IsEnable: true,
    IsIOTDevice: true,
    DeviceType: 0,
    DeviceFactory: "",
    IsPublish: true,
    InstrumentRentData: "",
    InstrumentDepositData: "",
    Parameters: "",
  };
};
const reset = () => {
  info.value = getInitData();
  parameterList.value = [];
};

const info = ref<OperateInstrumentInputDTO>(getInitData());
const parameterList = ref<_DeviceParameter[]>([]);
const baseChargeItemMap = reactive<Record<string, BaseChargeItemType>>({});
const baseDeviceList = ref<BaseInstrument[]>([]);
const loading = ref(false);

/** 获取运营端设备类型 */
const loadBaseDeviceList = async () => {
  const res = await Content_Api.getOriginalInstruments();
  if (res.Type != 200) {
    ElMessage.error(res.Content);
    return;
  }
  baseDeviceList.value = res.Data;
};

/**
 * 设置设备信息
 * @param data 设备信息
 * @param isInit 是否初始化
 */
const setInfo = (data: BaseInstrument, isInit: boolean) => {
  info.value = { ...data };

  // 处理参数
  if (info.value.IsIOTDevice) {
    const parameters = JSON.parse(info.value.Parameters) as BaseInstrumentParameter[];
    parameterList.value = parameters.map((x) => {
      let optionValue: string | null = null;
      if (x.Type === 1) {
        const optionValues = x.OptionValue?.map(
          (o: { Key: string; Value: string | number }) => `${o.Key}:${o.Value}`
        );
        optionValue = optionValues?.join(",") || "";
      }
      return { ...x, OptionValue: optionValue } as _DeviceParameter;
    });
  }

  if (isInit) {
    setRentAndDeposit(info.value.InstrumentRentData, info.value.InstrumentDepositData);
  }
};

/**
 * 设置设备租金及设备押金
 * @param rentJSON 租金JSON字符串
 * @param depositJSON 押金JSON字符串
 */
const setRentAndDeposit = async (rentJSON?: string, depositJSON?: string) => {
  // 解析租金和押金数据
  const rent = rentJSON
    ? (JSON.parse(rentJSON) as Array<{ ChargeItemId: string; Quantity: number }>)
    : [];
  const deposit = depositJSON
    ? (JSON.parse(depositJSON) as Array<{ ChargeItemId: string; Quantity: number }>)
    : [];

  // 获取所有收费项目ID并去重
  const rentIds = rent.map((x) => x.ChargeItemId);
  const depositIds = deposit.map((x) => x.ChargeItemId);
  const ids = Array.from(new Set([...rentIds, ...depositIds].filter(Boolean)));

  if (!ids.length) return;

  // 获取收费项目详情
  const res = await Content_Api.getChargeItemByIds(ids);
  if (res.Type !== 200) {
    ElMessage.error(res.Content);
    return;
  }

  // 缓存收费项目信息
  res.Data.forEach((item) => {
    baseChargeItemMap[item.Id] = item;
  });

  // 处理租金列表
  rentList.value = rent.map((item) => {
    const chargeItem = baseChargeItemMap[item.ChargeItemId];
    const amount = Number((item.Quantity * chargeItem.Price).toFixed(2));
    return {
      ...chargeItem,
      Quantity: item.Quantity,
      Amount: amount,
    };
  });

  // 处理押金列表
  depositList.value = deposit.map((item) => {
    const chargeItem = baseChargeItemMap[item.ChargeItemId];
    const amount = Number((item.Quantity * chargeItem.Price).toFixed(2));
    return {
      ...chargeItem,
      Quantity: item.Quantity,
      Amount: amount,
    };
  });
};

onBeforeMount(() => {
  // console.log(this.data, this.oper);
  reset();
  setInfo(JSON.parse(JSON.stringify(props.data)), true);
  loadBaseDeviceList();
});

const onChangeDevice = () => {
  if (info.value.InstrumentId) {
    const device = baseDeviceList.value.find((x) => x.InstrumentId === info.value.InstrumentId);
    if (!device) {
      return;
    }
    setInfo({ ...device }, false);
  } else {
    reset();
  }
};

const onTabClick = (tab: TabsPaneContext) => {
  tabIndex.value = tab.index!;
  switch (tabIndex.value) {
    case "1":
      searchChargeItem("");
      nextTick(() => {
        rentForm.value?.resetFields();
      });
      break;
    case "2":
      searchChargeItem("");
      nextTick(() => {
        depositForm.value?.resetFields();
      });
      break;
  }
};

/**
 * 搜索收费项目
 * @param query 搜索关键词
 */
const searchChargeItem = async (query: string) => {
  try {
    loading.value = true;
    const keyword = query?.trim() || "";
    // 根据tabIndex判断类型:1-设备租金,2-设备押金
    const type = tabIndex.value === "1" ? 1 : 2;

    const res = await Content_Api.getChargeItemPageData({
      page: 1,
      pageSize: 50,
      isEnable: true,
      keywords: keyword,
      type,
    });

    if (res.Type !== 200) {
      throw new Error(res.Content);
    }

    const rows = res.Data.Data;

    // 缓存收费项目信息
    if (rows.length) {
      rows.forEach((item) => {
        baseChargeItemMap[item.Id] = item;
      });
    }

    // 根据类型更新对应列表
    tabIndex.value === "1"
      ? (rentBaseChargeItemList.value = rows)
      : (depositBaseChargeItemList.value = rows);
  } catch (error) {
    ElMessage.error(error instanceof Error ? error.message : "搜索收费项目失败");
  } finally {
    loading.value = false;
  }
};

/** 设备租金 */
const rentList = ref<_ChargeItem[]>([]);
const rentBaseChargeItemList = ref<BaseChargeItemType[]>([]);
const rentFormModel = ref({
  ChargeItemId: "",
  Quantity: null as number | null,
});
const rentFormRules: FormRules = {
  ChargeItemId: [
    {
      type: "string",
      whitespace: true,
      required: true,
      message: "请选择收费项目",
      trigger: "change",
    },
  ],
  Quantity: [
    {
      type: "string",
      required: true,
      pattern: /^\+?[1-9]\d*$/,
      message: "数量为大于0的整数",
      trigger: "blur",
    },
  ],
};

/** 设备押金 */
const depositList = ref<_ChargeItem[]>([]);
const depositBaseChargeItemList = ref<BaseChargeItemType[]>([]);
const depositFormModel = ref({
  ChargeItemId: "",
  Quantity: null as number | null,
});
const depositFormRules: FormRules = {
  ChargeItemId: [
    {
      type: "string",
      whitespace: true,
      required: true,
      message: "请选择收费项目",
      trigger: "change",
    },
  ],
  Quantity: [
    {
      type: "string",
      required: true,
      pattern: /^\+?[1-9]\d*$/,
      message: "数量为大于0的整数",
      trigger: "blur",
    },
  ],
};

// 添加设备租金
const addRend = () => {
  rentForm.value?.validate(async (valid: boolean) => {
    if (valid) {
      // 判断是否重复
      const isHas = rentList.value.some((x) => x.Id === rentFormModel.value.ChargeItemId);
      if (isHas) {
        ElMessage.warning("收费项目重复");
        return;
      }
      // 选中收费项目
      const rent = baseChargeItemMap[rentFormModel.value.ChargeItemId];
      const amount = parseFloat((rent.Price * rentFormModel.value.Quantity!).toFixed(2));
      const data: _ChargeItem = {
        ...rent,
        Quantity: rentFormModel.value.Quantity!,
        Amount: amount,
      };
      rentList.value.push(data);
      rentForm.value?.resetFields();
    }
  });
};

// 删除设备租金收费项目
const deleteRend = (item: _ChargeItem) => {
  rentList.value = rentList.value.filter((x) => x.Id !== item.Id);
};
// 添加设备押金
const addDeposit = () => {
  depositForm.value?.validate((valid: boolean) => {
    if (valid) {
      // 判断是否重复
      const isHas = depositList.value.some((x) => x.Id === depositFormModel.value.ChargeItemId);
      if (isHas) {
        ElMessage.warning("收费项目重复");
        return;
      }
      // 选中诊断信息
      const deposit = baseChargeItemMap[depositFormModel.value.ChargeItemId];
      const amount = parseFloat((deposit!.Price * depositFormModel.value.Quantity!).toFixed(2));
      const data = {
        ...deposit,
        Quantity: depositFormModel.value.Quantity!,
        Amount: amount,
      };
      depositList.value.push(data);
      depositForm.value?.resetFields();
    }
  });
};
// 删除设备押金收费项目
const deleteDeposit = (item: _ChargeItem) => {
  depositList.value = depositList.value.filter((x) => x.Id !== item.Id);
};

const submit = () => {
  isSubmit.value = true;
  baseForm.value?.validate(async (valid: boolean, fields: any) => {
    if (valid) {
      const rentData = rentList.value.map((x) => {
        return {
          ChargeItemId: x.Id,
          Quantity: x.Quantity,
        };
      });
      const depositData = depositList.value.map((x) => {
        return {
          ChargeItemId: x.Id,
          Quantity: x.Quantity,
        };
      });

      const res = await Content_Api.insertOrUpdateInstrument({
        Name: info.value.Name,
        Code: info.value.Code,
        OrganizationId: info.value.OrganizationId,
        IsPublish: info.value.IsPublish,
        PYM: info.value.PYM,
        IsEnable: info.value.IsEnable,
        InstrumentId: info.value.InstrumentId, // 原始id
        IsIOTDevice: info.value.IsIOTDevice, // 是否物联设备
        DeviceType: info.value.DeviceType, // 设备类别参数参数 0治疗 1评定
        DeviceFactory: info.value.IsIOTDevice ? info.value.DeviceFactory : "", // 设备厂商
        Parameters: info.value.Parameters,
        InstrumentImg: info.value.InstrumentImg,
        ObjectId: info.value.InstrumentId,
        InstrumentRentData: JSON.stringify(rentData),
        InstrumentDepositData: JSON.stringify(depositData),
      });

      isSubmit.value = false;
      if (res.Type != 200) {
        ElMessage.error(res.Content);
        return;
      }

      ElNotification.success(res.Content);
      emit("success");
    } else {
      console.warn(fields);
      isSubmit.value = false;
      const keys = Object.keys(fields);
      if (keys.length) {
        const error = fields[keys[0]][0].message;
        ElMessage.warning(error || "请检查输入内容");
      } else {
        ElMessage.warning("请检查输入内容");
      }
    }
  });
};

const close = () => {
  emit("close");
};
</script>
<style lang="scss" scoped>
.InstrumentDictModify {
  .Parameters {
    margin-bottom: 10px;
    border-bottom: 1px dashed #ddd;
  }
}
</style>
