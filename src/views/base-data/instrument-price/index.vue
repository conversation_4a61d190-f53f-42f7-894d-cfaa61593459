<template>
  <div class="app-container">
    <BaseTableSearchContainer :tableResize="tableResize">
      <template #search>
        <el-form
          class="p-10px pb-0"
          :model="query"
          label-position="right"
          inline
          @submit.prevent="search"
        >
          <el-form-item label="是否启用" prop="IsEnabled">
            <el-select v-model="query.IsEnabled" placeholder="请选择" clearable>
              <el-option label="是" :value="true" />
              <el-option label="否" :value="false" />
            </el-select>
          </el-form-item>
          <el-form-item label="关键字" prop="keyword">
            <el-input v-model="query.keyword" placeholder="名称/编码/拼音码" />
          </el-form-item>

          <div class="float-right">
            <el-button type="primary" icon="search" native-type="submit">搜索</el-button>
            <el-button type="primary" @click="handleAction('add')">添加</el-button>
          </div>
        </el-form>
      </template>
      <template #table>
        <el-table
          v-loading="tableLoading"
          :data="pageData"
          border
          fit
          show-overflow-tooltip
          :header-cell-style="{ 'text-align': 'center' }"
          :cell-style="{ 'text-align': 'center' }"
        >
          <el-table-column prop="Name" label="名称" :show-overflow-tooltip="false" />
          <el-table-column prop="Code" label="编码" />
          <el-table-column prop="PYM" label="拼音码" />
          <el-table-column
            prop="DeviceType"
            label="设备类别"
            :formatter="(row, column, value) => ['治疗', '评定'][value]"
          />
          <el-table-column
            prop="IsIOTDevice"
            label="物联设备"
            :formatter="(row, column, value) => (value ? '是' : '否')"
          />
          <el-table-column
            prop="CreatedTime"
            label="创建时间"
            :formatter="dateFormat"
            width="150"
          />
          <el-table-column
            prop="IsEnable"
            label="设备是否启用"
            :formatter="(row, column, value) => (value ? '是' : '否')"
          />
          <el-table-column fixed="right" label="操作" width="200">
            <template #default="{ row }">
              <el-button link type="primary" @click="handleAction('view', row)">查看</el-button>
              <el-button link type="primary" @click="handleAction('edit', row)">编辑</el-button>
              <el-button
                v-if="!row.IsPublish"
                link
                type="primary"
                @click="handleAction('publish', row)"
              >
                发布
              </el-button>
              <el-button
                v-if="!row.IsPublish"
                link
                type="warning"
                @click="handleAction('delete', row)"
              >
                删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </template>
      <template #pagination>
        <Pagination
          v-if="total > 0"
          v-model:total="total"
          v-model:page="query.pageIndex"
          v-model:limit="query.pageSize"
          @pagination="loadData"
        />
      </template>
    </BaseTableSearchContainer>

    <el-dialog
      v-model="dialogModel.visible"
      :title="dialogTitle"
      :close-on-click-modal="dialogCloseable"
      :close-on-press-escape="dialogCloseable"
      destroy-on-close
    >
      <Modify
        ref="modifyRef"
        :data="dialogModel.data ?? ({} as BaseInstrument)"
        :oper="dialogModel.action ?? 'view'"
        @success="
          () => {
            loadData();
            dialogModel.visible = false;
          }
        "
        @close="dialogModel.visible = false"
      />
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { useTableConfig } from "@/hooks/useTableConfig";
import Content_Api from "@/api/content";
import dayjs from "dayjs";

import Modify from "./Modify.vue";

const query = reactive({
  IsEnabled: undefined as boolean | undefined,
  keyword: "",
  pageIndex: 1,
  pageSize: 20,
});
const { pageData, tableLoading, total, tableResize } = useTableConfig<BaseInstrument>();

// 获取列表数据
async function loadData() {
  tableLoading.value = true;
  const res = await Content_Api.getInstrumentsPageData({
    page: query.pageIndex,
    pageSize: query.pageSize,
    isLoadAmount: true,
    isEnable: query.IsEnabled,
    keywords: query.keyword,
  });
  tableLoading.value = false;
  if (res.Type !== 200) {
    ElMessage.error(res.Content);
    return;
  }
  pageData.value = res.Data.Data;
  total.value = res.Data.TotalCount;
}

function search() {
  query.pageIndex = 1;
  loadData();
}

onMounted(() => {
  loadData();
});

function dateFormat(row: any, column: any, value: string) {
  if (!value) return "";
  return dayjs(value).format("YYYY-MM-DD HH:mm:ss");
}

async function publish(Id: string) {
  ElMessageBox.confirm("发布该条数据, 是否继续?", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  })
    .then(async () => {
      const res = await Content_Api.publishInstrument({ instrumentId: Id });
      if (res.Type != 200) {
        ElNotification.error(res.Content);
        return;
      }

      ElNotification.success(res.Content);
      loadData();
    })
    .catch(() => {});
}
function deleteData(Id: string) {
  ElMessageBox.confirm("此操作将删除该条数据, 是否继续?", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  })
    .then(async () => {
      const res = await Content_Api.deleteInstrument({ instrumentId: Id });
      if (res.Type != 200) {
        ElNotification.error(res.Content);
        return;
      }

      ElNotification.success(res.Content);
      loadData();
    })
    .catch(() => {});
}

const dialogModel = reactive({
  visible: false,
  action: null as "add" | "edit" | "view" | null,
  data: null as BaseInstrument | null,
});

const dialogTitle = computed(() => {
  switch (dialogModel.action) {
    case "add":
      return "新增";
    case "edit":
      return "编辑";
    case "view":
      return "查看";
  }
  return "";
});

const dialogCloseable = computed(() => {
  return dialogModel.action === "view";
});

const modifyRef = useTemplateRef<InstanceType<typeof Modify>>("modifyRef");

function handleAction(
  action: "add" | "edit" | "view" | "delete" | "publish",
  data?: BaseInstrument
) {
  switch (action) {
    case "view":
    case "edit":
    case "add":
      dialogModel.action = action;
      // nextTick(() => {
      //   console.log(action, modifyRef.value);
      //   modifyRef.value?.init(action, data);
      // });
      break;
    case "delete":
      deleteData(data!.Id!);
      return;
    case "publish":
      publish(data!.Id!);
      return;
  }
  dialogModel.data = data || null;
  dialogModel.visible = true;
}
</script>
