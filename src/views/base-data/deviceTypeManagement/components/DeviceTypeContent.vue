<template>
  <el-form ref="formRef" :model="form" :rules="rules" inline :disabled="isPreview" scroll-to-error>
    <el-form-item label="名称" prop="Name">
      <el-input v-model="form.Name" link style="width: 150px" @blur="handleNameBlur" />
    </el-form-item>
    <el-form-item label="拼音码" prop="PYM">
      <el-input v-model="form.PYM" link style="width: 150px" />
    </el-form-item>
    <el-form-item label="编码" prop="Code">
      <el-input v-model="form.Code" link style="width: 150px" />
    </el-form-item>
    <el-form-item label="设备类别">
      <el-select v-model="form.DeviceType" style="width: 100px" filterable placeholder="分类">
        <el-option
          v-for="(item, index) in deviceTypeData"
          :key="'DT' + index"
          :label="item.Key"
          :value="item.Id"
        />
      </el-select>
    </el-form-item>
    <el-form-item label="是否启用">
      <el-switch v-model="form.IsEnable" />
    </el-form-item>
    <el-form-item label="物联设备">
      <el-switch v-model="form.IsIOTDevice" />
    </el-form-item>
    <div v-if="form.IsIOTDevice">
      <el-form-item label="设备厂商">
        <el-select
          v-model="form.DeviceFactory"
          style="width: 120px"
          clearable
          placeholder="设备厂商"
          filterable
        >
          <el-option
            v-for="(item, index) in deviceFactoryData"
            :key="index"
            :label="item.Key"
            :value="item.Value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button size="small" type="primary" @click="handleAddParameter">添加参数</el-button>
      </el-form-item>
      <div v-for="(item, index) in parameters" :key="'par' + index" class="parameters">
        <el-form-item label="参数名称">
          <el-input v-model="item.Name" link style="width: 150px" />
        </el-form-item>
        <el-form-item label="字段标记">
          <el-input v-model="item.SignCode" link style="width: 150px" />
        </el-form-item>
        <el-form-item label="参数类型">
          <el-select v-model="item.Type" style="width: 150px" placeholder="分类" filterable>
            <el-option
              v-for="(o, oIndex) in parTypeData"
              :key="'pt' + oIndex"
              :label="o.Key"
              :value="o.Id"
            />
          </el-select>
        </el-form-item>
        <el-form-item v-if="item.Type === 1" label="选项值">
          <el-input
            v-model="item.OptionValue"
            link
            style="width: 380px"
            placeholder="每个选项值用 ‘,’ 分隔"
          />
          <span style="color: #ddd">（如：模式一:1,模式二:2）</span>
        </el-form-item>
        <el-form-item v-if="item.Type === 0" label="参数单位">
          <el-input v-model="item.Unit" link style="width: 150px" />
        </el-form-item>
        <el-form-item v-if="!isPreview" class="delete-btn" @click="handleDeleteParameter(index)">
          <el-icon><Delete /></el-icon>
          删除
        </el-form-item>
      </div>
    </div>
  </el-form>
</template>

<script setup lang="ts">
import { chineseToPinyin } from "@/utils/pinyin-helper";
import { FormInstance, FormRules } from "element-plus";
import { Delete } from "@element-plus/icons-vue";
import { OperateInstrumentInputDTO } from "@/api/content/types";

// interface PageInstrument extends Omit<OperateInstrumentInputDTO, "Parameters"> {
//   Parameters: PageParameter[];
// }
interface PageParameter {
  Name: string;
  Type: number;
  Value: string;
  OptionValue: string | null;
  Unit: string;
  SignCode: string;
}

const isPreview = inject("isPreview") as Ref<boolean>;
const formRef = ref<FormInstance>();
const rules = reactive<FormRules>({
  Name: [
    { required: true, message: "请输入名称", trigger: "blur" },
    { max: 25, message: "名称不能超过25个字符", trigger: "blur" },
  ],
  PYM: [
    { required: true, message: "请输入拼音码", trigger: "blur" },
    { max: 25, message: "拼音码不能超过25个字符", trigger: "blur" },
  ],
  Code: [
    { required: true, message: "请输入编码", trigger: "blur" },
    { max: 25, message: "编码不能超过25个字符", trigger: "blur" },
  ],
});
const form = ref<OperateInstrumentInputDTO>({
  Name: "",
  PYM: "",
  Code: "",
  DeviceType: 0,
  IsEnable: true,
  IsIOTDevice: false,
  Parameters: "",
  InstrumentId: "",
  DeviceFactory: "",
  IsPublish: false,
});

const parameters = ref<PageParameter[]>([]);

const deviceTypeData = ref<{ Id: number; Key: string }[]>([
  {
    Id: 0,
    Key: "治疗",
  },
  {
    Id: 1,
    Key: "评定",
  },
]);
const deviceFactoryData = ref<{ Id: number; Key: string; Value: string }[]>([
  {
    Id: 1,
    Key: "VR",
    Value: "VR",
  },
  {
    Id: 2,
    Key: "OA治疗仪",
    Value: "OA",
  },
  {
    Id: 3,
    Key: "功率车",
    Value: "PowerCar",
  },
]);
const parTypeData = ref<{ Id: number; Key: string }[]>([
  {
    Id: 0,
    Key: "数值",
  },
  {
    Id: 1,
    Key: "选择",
  },
]);

const handleAddParameter = () => {
  parameters.value.push({
    Name: "",
    Type: 1, // 参数参数   0数值  1选择
    Value: "", // 数值就是默认值 ，选择就是option
    OptionValue: "",
    Unit: "",
    SignCode: "", // 后端字段对应
  });
};

const handleDeleteParameter = (index: number) => {
  parameters.value.splice(index, 1);
};

const handleProcessingData = (info: BaseInstrument) => {
  form.value.Name = info.Name;
  form.value.PYM = info.PYM;
  form.value.Code = info.Code;
  form.value.DeviceType = info.DeviceType;
  form.value.IsEnable = info.IsEnable;
  form.value.IsIOTDevice = info.IsIOTDevice;
  form.value.DeviceFactory = info.DeviceFactory;
  form.value.InstrumentId = info.InstrumentId;
  form.value.IsPublish = info.IsPublish;
  if (info.Parameters) {
    try {
      var pars: PageParameter[] = [];
      var parList = JSON.parse(info.Parameters);
      parList.forEach((x: any) => {
        if (x.Type === 1) {
          var oval = JSON.parse(JSON.stringify(x.OptionValue));
          var arr: string[] = [];
          oval.forEach((o: any) => {
            arr.push(o.Key + ":" + o.Value);
          });
          x.OptionValue = arr.join(",");
        } else {
          x.OptionValue = "";
        }
        pars.push(x);
      });
      parameters.value = pars;
    } catch {
      parameters.value = [];
    }
  }
};

const handleSubmit = async (): Promise<OperateInstrumentInputDTO | null> => {
  try {
    await formRef.value!.validate();
    // 对填写的参数进行校验
    if (form.value.IsIOTDevice && !validateParameters()) {
      return null;
    }
    // 对Parameters数据进行组装
    let pars: any[] = [];
    parameters.value.forEach((item: any) => {
      var parItem = { ...item };
      if (item.Type === 1) {
        var oval = item.OptionValue.split(",");
        var list: { Key: string; Value: string }[] = [];
        oval.forEach((item: string) => {
          var arr = item.split(":");
          if (arr[0] || arr[1]) {
            list.push({
              Key: arr[0] ? arr[0] : "",
              Value: arr[1] ? arr[1] : "",
            });
          }
        });
        parItem.OptionValue = list.length > 0 ? list : null;
        parItem.Unit = "";
      } else {
        parItem.OptionValue = null;
      }
      pars.push(parItem);
    });
    const copyData = JSON.parse(JSON.stringify(form.value));
    copyData.Parameters = JSON.stringify(pars);
    if (!copyData.InstrumentId) delete copyData.InstrumentId;
    return copyData;
  } catch {
    return null;
  }
};
// 校验参数
const validateParameters = (): boolean => {
  var isFull = parameters.value.some((x: any) => {
    var b = false;
    b = !x.Name || !x.SignCode;
    if (x.Type === 1) {
      b = b || !x.OptionValue;
    } else {
      b = b || !x.Unit;
    }
    return b;
  });
  if (isFull) {
    ElMessage.warning("请把参数信息填写完整");
    return false;
  }
  return true;
};

const handleNameBlur = () => {
  if (!form.value.Name) return;
  form.value.PYM = chineseToPinyin(form.value.Name);
};

interface Props {
  info: BaseInstrument | null;
}
const props = defineProps<Props>();

watch(
  () => props.info,
  (newVal) => {
    if (newVal) {
      handleProcessingData(newVal);
    }
  },
  { immediate: true }
);

defineExpose({
  handleSubmit,
});
</script>

<style lang="scss" scoped>
.parameters {
  position: relative;
  width: 100%;
  margin-bottom: 10px;
  border-bottom: 1px dashed #ddd;

  .el-form-item--mini.el-form-item {
    margin-bottom: 10px;
  }

  .delete-btn {
    padding: 0 8px;
    font-size: 12px;
    color: #f56c6c;
    cursor: pointer;
    background-color: #fff;
    border: 1px solid #dcdfe6;
    border-radius: 4px;
    transition: all 0.3s ease;

    &:hover {
      color: #fff;
      background-color: #f56c6c;
      border-color: #f56c6c;
      box-shadow: 0 2px 4px rgb(245 108 108 / 30%);
    }

    .el-icon {
      font-size: 14px;
    }
  }
}
</style>
