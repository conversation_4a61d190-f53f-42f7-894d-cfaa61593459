<template>
  <el-container class="w-full h-full">
    <el-aside width="230px" class="p-10px">
      <el-tree
        class="w-full h-full p-10px"
        :data="treeData"
        :props="defaultTreeProps"
        node-key="value"
        :current-node-key="queryParams.Class"
        highlight-current
        default-expand-all
        @node-click="onTreeClick"
      />
    </el-aside>
    <el-main class="p-10px!">
      <BaseTableSearchContainer @size-changed="tableResize">
        <template #search>
          <!-- 顶部筛选条件 -->
          <TBSearchContainer>
            <template #left>
              <el-form label-position="right" :model="queryParams" :inline="true">
                <el-form-item label="是否启用" prop="IsEnble">
                  <KSelect
                    v-model="queryParams.Enable"
                    :data="[
                      { label: '是', value: true },
                      { label: '否', value: false },
                    ]"
                    :show-all="true"
                  />
                </el-form-item>
                <el-form-item label="关键字" prop="Keyword">
                  <el-input
                    v-model="queryParams.Keyword"
                    clearable
                    placeholder="输入关键字筛选"
                    @keyup.enter="requestGaugeData"
                  />
                </el-form-item>
              </el-form>
            </template>
            <template #right>
              <el-button type="primary" icon="search" @click="requestGaugeData">搜索</el-button>
              <el-button type="primary" @click="onAddPhrase">添加</el-button>
            </template>
          </TBSearchContainer>
        </template>
        <template #table>
          <el-table :data="pageData" highlight-current-row border :height="tableFluidHeight">
            <el-table-column prop="Class.label" label="类别" align="center" width="200" />
            <el-table-column prop="Text" show-overflow-tooltip label="内容" align="center" />
            <el-table-column prop="Order" label="排序" align="center" width="120" />
            <el-table-column prop="IsEnbleText" label="是否启用" align="center" width="120" />
            <el-table-column fixed="right" label="操作" width="180" align="center">
              <template #default="scope">
                <el-button link size="small" type="primary" @click="onPhraseDetail(scope.row)">
                  查看
                </el-button>
                <el-button link size="small" type="primary" @click="onEditPhrase(scope.row)">
                  编辑
                </el-button>
                <el-button link size="small" type="primary" @click="onDeletePhrase(scope.row)">
                  删除
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </template>
        <!-- 分页 -->
        <template #pagination>
          <Pagination
            v-if="total > 0"
            v-model:total="total"
            v-model:page="queryParams.pageIndex"
            v-model:limit="queryParams.pageSize"
            @pagination="requestGaugeData"
          />
        </template>
      </BaseTableSearchContainer>
    </el-main>
  </el-container>

  <!-- 添加/编辑/查看量表 -->
  <el-dialog
    v-model="showPhraseDialog.isShow"
    :title="showPhraseDialog.title"
    width="800"
    destroy-on-close
    @close="showPhraseDialog.isShow = false"
  >
    <PhraseForm
      :phrase="showPhraseDialog.phrase"
      :disabled="showPhraseDialog.disabled"
      @cancel="showPhraseDialog.isShow = false"
      @submit="onConfirmSubmitPhrase"
    />
  </el-dialog>
</template>

<script setup lang="ts">
import Content_Api from "@/api/content";
import { GetPhraseParams, Phrase } from "@/api/content/types";
import { useTableConfig } from "@/hooks/useTableConfig";
import { classList, ClassModel } from "./constant";
import PhraseForm from "./components/PhraseForm.vue";

const kEnableDebug = false;
const { tableLoading, pageData, total, tableFluidHeight, tableResize } =
  useTableConfig<PhraseShow>();

interface PhraseShow extends Omit<Phrase, "Class"> {
  IsEnbleText?: string;
  Class?: ClassModel;
}

interface Tree {
  // 展示数据
  label: string;
  // 实际网络请求需要的值
  value?: number;
  children?: Tree[];
}

// 查询参数
const queryParams = reactive<GetPhraseParams>({
  Source: 0,
  pageIndex: 1,
  pageSize: 20,
});

const showPhraseDialog = reactive({
  isShow: false,
  title: "",
  phrase: {} as Phrase,
  disabled: false,
});

// 左侧列表数据
const treeData = reactive<Tree[]>([
  {
    label: "全部",
    children: [
      {
        label: "患者端",
        value: 0,
      },
      {
        label: "医生端-在线问诊",
        value: 1,
      },
      {
        label: "医生端-诊后管理",
        value: 2,
      },
      {
        label: "医生端-咨询服务",
        value: 3,
      },
      {
        label: "医生端-方案说明",
        value: 4,
      },
    ],
  },
]);
const defaultTreeProps = reactive({
  children: "children",
  label: "label",
});

// 树点击事件
async function onTreeClick(data: Tree) {
  kEnableDebug && console.debug("树点击事件", data);
  queryParams.Class = data.value;
  queryParams.pageIndex = 1;

  const r = await requestGaugeData();
  if (r.Type !== 200) {
    ElMessage.error(r.Content);
  }
}

// 添加常用语
async function onAddPhrase() {
  kEnableDebug && console.debug("添加常用语");

  showPhraseDialog.phrase = {
    Class: queryParams.Class,
    Enable: true,
    Order: 0,
  };
  showPhraseDialog.title = "添加常用语";
  showPhraseDialog.disabled = false;
  showPhraseDialog.isShow = true;
}

// 查看常用语
async function onPhraseDetail(item: PhraseShow) {
  kEnableDebug && console.debug("查看常用语", item);

  showPhraseDialog.phrase = {
    ...item,
    Class: item.Class?.value,
  };
  showPhraseDialog.title = "查看常用语";
  showPhraseDialog.disabled = true;
  showPhraseDialog.isShow = true;
}

// 编辑常用语
async function onEditPhrase(item: PhraseShow) {
  kEnableDebug && console.debug("编辑常用语", item);

  const data: PhraseShow = JSON.parse(JSON.stringify(item));
  delete data.IsEnbleText;
  showPhraseDialog.phrase = {
    ...data,
    Class: data.Class?.value,
  };
  showPhraseDialog.title = "编辑常用语";
  showPhraseDialog.disabled = false;
  showPhraseDialog.isShow = true;
}

// 删除常用语
async function onDeletePhrase(item: PhraseShow) {
  kEnableDebug && console.debug("删除常用语", item);

  if (!item.Id) {
    ElMessage.error("常用语id为空");
    return;
  }

  ElMessageBox.confirm("此操作将删除该条数据, 是否继续?", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(async () => {
    const r = await Content_Api.deletePhrases([item.Id!]);
    if (r.Type === 200) {
      ElNotification.success("删除成功");
      requestGaugeData();
    } else {
      ElMessage.error(r.Content);
    }
  });
}

// 确认提交常用语
async function onConfirmSubmitPhrase(data: Phrase) {
  kEnableDebug && console.debug("确认提交常用语", data);
  showPhraseDialog.isShow = false;

  let r: ServerResult;
  if (data.Id) {
    // 编辑
    r = await Content_Api.updatePhrases([data]);
  } else {
    // 新增
    r = await Content_Api.insertPhrases([data]);
  }

  if (r.Type === 200) {
    ElNotification.success("操作成功");
    requestGaugeData();
  } else {
    ElMessage.error(r.Content);
  }
}

// 请求常用语数据数据
async function requestGaugeData() {
  tableLoading.value = true;
  const r = await Content_Api.getPhraseList(queryParams);
  if (r.Type === 200) {
    pageData.value = r.Data.Data.map((e) => {
      return {
        ...e,
        IsEnbleText: e.Enable ? "是" : "否",
        Class: classList.find((item) => item.value === e.Class),
      };
    });
    total.value = r.Data.TotalCount;
  }
  tableLoading.value = false;
  return r;
}

defineOptions({
  name: "CommonPhraseManagement",
});
onActivated(() => {
  // 调用时机为首次挂载
  // 以及每次从缓存中被重新插入时
  requestGaugeData();
});
</script>

<style lang="scss" scoped></style>
