<template>
  <div v-loading="formLoading" class="p-20px overflow-y-auto">
    <el-form
      :ref="kFormRef"
      :model="formData"
      :rules="rules"
      label-width="auto"
      :disabled="props.disabled"
    >
      <el-row>
        <el-form-item class="flex-1" label="名称" prop="Name">
          <el-input
            v-model="formData.Name"
            placeholder="请输入名称"
            maxlength="25"
            show-word-limit
            clearable
          />
        </el-form-item>
        <el-form-item label="编码" class="w-1/3" label-width="80" prop="Code">
          <el-input
            v-model="formData.Code"
            placeholder="请输入编码"
            maxlength="10"
            show-word-limit
            clearable
          />
        </el-form-item>
        <el-form-item label="是否启用" label-width="90">
          <el-switch v-model="formData.IsEnable" />
        </el-form-item>
      </el-row>
      <el-form-item label="穴位" prop="AcuPointInfos" class="mb-20px!">
        <TagsSelect
          v-model="selectedAcuPoints"
          :options="acuPointList"
          :props="{ label: 'Name', value: 'Id' }"
          :loading="acuPointListLoading"
          :disabled="props.disabled"
        />
      </el-form-item>
      <el-row>
        <el-form-item
          v-for="item in formData.AcuPointInfos"
          :key="item.AcuPointId"
          :label="item.Name"
        >
          <el-input-number v-model="item.AcuPointCount" class="mr-10px" :min="1" :max="99" />
        </el-form-item>
      </el-row>
      <TableTransfer
        v-model:pending-list="moItemList"
        v-model:selected-list="selectedMoItems"
        :pending-loading="moItemListLoading"
        :pending-search-config="{
          placeholder: '请输入医嘱名称',
          searchMethod: requestMoItemList,
        }"
        :pending-page-config="{
          pageChangeMethod: requestMoItemList,
          pendingTotal: moItemListTotal,
        }"
        :selected-loading="selectedMoItemsLoading"
        :disabled="props.disabled"
      >
        <template #pending>
          <el-table-column prop="Name" label="名称" />
          <el-table-column prop="Code" label="编码" />
        </template>
        <template #selected>
          <el-table-column prop="Name" label="名称" />
          <el-table-column prop="Code" label="编码" />
        </template>
      </TableTransfer>
    </el-form>
  </div>
  <!-- 底部按钮 -->
  <div class="flex justify-end">
    <el-button @click="emit('cancel')">取消</el-button>
    <el-button v-if="!props.disabled" type="primary" @click="onSubmitForm()">确定</el-button>
  </div>
</template>

<script setup lang="ts">
import { FormRules, FormInstance, dayjs } from "element-plus";
import {
  AcuPointTemplateInputDTO,
  GetAcuPointPageDataParams,
  MoItemPageDataInputDTO,
} from "@/api/content/types";
import Content_Api from "@/api/content";

interface MoItemData {
  Id?: string;
  Name?: string;
  Code?: string;
}

const kEnableDebug = false;
const kFormRef = "ruleFormRef";

defineOptions({
  name: "AcupointTemplateForm",
});

const props = defineProps<{
  data: BaseAcuPointTemplate;
  disabled: boolean;
}>();

const emit = defineEmits<{
  cancel: [];
  submit: [];
}>();

onMounted(async () => {
  const data = JSON.parse(JSON.stringify(props.data));
  formData.Name = data.Name;
  formData.Code = data.Code;
  formData.IsEnable = data.IsEnable ?? true;

  requestAcuPointList();
  await requestMoItemList({ pageIndex: 1 });
  requestSelectedMoItemList();
});

const formLoading = ref(false);
/** 表单实例 */
const formRef = useTemplateRef<FormInstance>(kFormRef);
/** 表单数据 */
const formData = reactive<AcuPointTemplateInputDTO>({
  Name: "",
  Code: "",
  IsEnable: true,
  AcuPointInfos: [],
  MoItemIds: [],
  AcuPointIds: [],
});

/** 表单验证规则 */
const rules = reactive<FormRules<AcuPointTemplateInputDTO>>({
  Name: [{ required: true, message: "请输入名称", trigger: "blur" }],
  Code: [{ required: true, message: "请输入编码", trigger: "blur" }],
  AcuPointInfos: [
    {
      required: true,
      message: "请添加穴位",
      trigger: ["blur", "change"],
    },
  ],
});

/** 提交表单 */
function onSubmitForm() {
  if (!formRef.value) return;

  formRef.value.validate((valid, fields) => {
    if (valid) {
      requestAddOrUpdateData();
    } else {
      kEnableDebug && console.debug("提交失败", fields);
    }
  });
}

/** 添加/更新数据 */
async function requestAddOrUpdateData() {
  kEnableDebug && console.debug("添加/更新数据", formData);

  formLoading.value = true;
  const params: AcuPointTemplateInputDTO = {
    Name: formData.Name,
    Code: formData.Code,
    MoItemIds: selectedMoItems.value.map((e) => e.Id ?? ""),
    OrganizationId: props.data.OrganizationId,
    IsEnable: formData.IsEnable,
    AcuPointIds: formData.AcuPointIds,
    AcuPointInfos: formData.AcuPointInfos,
  };
  if (props.data.Id) {
    params.ObjectId = props.data.Id;
    params.CreateTime = props.data.CreateTime
      ? props.data.CreateTime
      : dayjs().format("YYYY-MM-DD HH:mm:ss");
    params.CreatorId = props.data.CreatorId;
  }
  const r = await Content_Api.insertOrUpdateAcuPointTemplate(params);
  formLoading.value = false;
  if (r.Type === 200) {
    emit("submit");
  } else {
    ElMessage.error(r.Content);
  }
}

/** 穴位列表 */
const acuPointList = ref<BaseAcuPoint[]>([]);
const acuPointListLoading = ref(false);

/** 选中的穴位 */
const selectedAcuPoints = ref<BaseAcuPoint[]>([]);
watch(
  selectedAcuPoints,
  (newVal) => {
    kEnableDebug && console.debug("选中的穴位", newVal);
    formData.AcuPointIds = newVal.map((e) => e.Id ?? "") ?? [];
    formData.AcuPointInfos = newVal.map((e) => ({
      AcuPointId: e.Id,
      Name: e.Name ?? "",
      AcuPointCount: e.AcuPointCount ?? 1,
      IsDouble: e.IsDouble ?? false,
    }));
  },
  { deep: true }
);

/** 请求穴位列表 */
async function requestAcuPointList() {
  acuPointListLoading.value = true;
  const params: GetAcuPointPageDataParams = {
    page: 1,
    pageSize: 9999,
    isEnable: true,
  };
  const r = await Content_Api.getAcuPointPageData(params);
  acuPointListLoading.value = false;
  if (r.Type !== 200) {
    ElMessage.error(r.Content);
    return;
  }

  acuPointList.value = r.Data.Data;
  selectedAcuPoints.value = r.Data.Data.reduce((acc, curr) => {
    const acuPointInfo = props.data.AcuPointInfos?.find((e) => e.AcuPointId === curr.Id);
    if (acuPointInfo) {
      acc.push({
        ...curr,
        AcuPointCount: acuPointInfo?.AcuPointCount ?? 1,
        IsDouble: acuPointInfo?.IsDouble ?? false,
      });
    }
    return acc;
  }, [] as BaseAcuPoint[]);
}

/** 医嘱列表 */
const moItemList = ref<MoItemData[]>([]);
const moItemListLoading = ref(false);
const moItemListTotal = ref(0);

/** 请求医嘱列表 */
async function requestMoItemList(data: { keyword?: string; pageIndex: number }) {
  moItemListLoading.value = true;
  const params: MoItemPageDataInputDTO = {
    page: data.pageIndex,
    pageSize: 10,
    isEnable: true,
    moItemMethod: 1,
    keywords: data.keyword,
  };
  const r = await Content_Api.getMoItemPageData(params);
  moItemListLoading.value = false;
  if (r.Type !== 200) {
    ElMessage.error(r.Content);
    return;
  }

  moItemList.value = r.Data.Data;
  moItemListTotal.value = r.Data.TotalCount;
}

/** 已选医嘱列表 */
const selectedMoItems = ref<MoItemData[]>([]);
const selectedMoItemsLoading = ref(false);

/** 获取已选医嘱列表 */
async function requestSelectedMoItemList() {
  selectedMoItemsLoading.value = true;
  const r = await Content_Api.getMoItemByIds({ Ids: props.data.MoItemIds ?? [] });
  selectedMoItemsLoading.value = false;
  if (r.Type !== 200) {
    ElMessage.error(r.Content);
    return;
  }

  selectedMoItems.value = r.Data;
}
</script>

<style lang="scss" scoped></style>
