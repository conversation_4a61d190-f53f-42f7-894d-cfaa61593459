<template>
  <div class="app-container">
    <BaseTableSearchContainer @size-changed="tableResize">
      <!-- 顶部筛选条件 -->
      <template #search>
        <TBSearchContainer :is-show-toggle="true">
          <template #left>
            <el-form :model="queryParams" label-position="right" :inline="true">
              <el-form-item label="是否启用">
                <KSelect
                  v-model="queryParams.isEnable"
                  :data="[
                    { label: '是', value: true },
                    { label: '否', value: false },
                  ]"
                  :show-all="true"
                />
              </el-form-item>
              <el-form-item label="医嘱">
                <KSelect
                  v-model="queryParams.moItem"
                  :data="moItemList"
                  :props="{ label: 'Name', value: 'Id' }"
                  :loading="moItemListLoading"
                  show-all
                  filterable
                  placeholder="请选择医嘱"
                />
              </el-form-item>
              <el-form-item label="关键字">
                <el-input
                  v-model="queryParams.keywords"
                  placeholder="名称/编码"
                  clearable
                  @keyup.enter="handleQuery"
                />
              </el-form-item>
            </el-form>
          </template>
          <template #right>
            <el-button type="primary" icon="search" @click="handleQuery">搜索</el-button>
            <el-button type="primary" @click="onAddItem">添加</el-button>
          </template>
        </TBSearchContainer>
      </template>
      <!-- 列表 -->
      <template #table>
        <el-table
          v-loading="tableLoading"
          :data="pageData"
          :total="total"
          row-key="Id"
          :height="tableFluidHeight"
          :header-cell-style="{ textAlign: 'center' }"
          :cell-style="{ textAlign: 'center' }"
          border
          highlight-current-row
        >
          <el-table-column prop="Code" min-width="80" label="编码" />
          <el-table-column prop="Name" min-width="100" show-overflow-tooltip label="模板名称" />
          <el-table-column
            prop="AcuPointNames"
            min-width="120"
            show-overflow-tooltip
            label="穴位"
          />
          <el-table-column label="是否启用" width="80" align="center">
            <template #default="scope">
              {{ scope.row.IsEnable ? "是" : "否" }}
            </template>
          </el-table-column>
          <el-table-column prop="MoNames" min-width="120" label="关联医嘱" />
          <el-table-column
            prop="CreateTime"
            label="创建时间"
            width="180"
            :formatter="tableDateFormat"
          />
          <!-- 操作 -->
          <el-table-column fixed="right" label="操作" width="180">
            <template #default="scope">
              <el-button link type="primary" @click="onPreviewOrEdit(scope.row, true)">
                查看
              </el-button>
              <el-button
                v-hasNoPermission="['promoter']"
                link
                type="primary"
                @click="onPreviewOrEdit(scope.row, false)"
              >
                编辑
              </el-button>
              <el-button link type="primary" @click="onDeleteItem(scope.row)">删除</el-button>
            </template>
          </el-table-column>
        </el-table>
      </template>
      <!-- 分页 -->
      <template #pagination>
        <Pagination
          v-if="total > 0"
          v-model:total="total"
          v-model:page="queryParams.page"
          v-model:limit="queryParams.pageSize"
          @pagination="requestTableList"
        />
      </template>
    </BaseTableSearchContainer>
  </div>

  <!-- 添加/编辑/查看 -->
  <el-dialog
    v-model="showDataDialog.isShow"
    :title="showDataDialog.title"
    width="800"
    destroy-on-close
    @close="showDataDialog.isShow = false"
  >
    <AcupointTemplateForm
      :data="showDataDialog.data"
      :disabled="showDataDialog.disabled"
      @cancel="showDataDialog.isShow = false"
      @submit="onConfirmSubmitItem"
    />
  </el-dialog>
</template>

<script setup lang="ts">
import { useTableConfig } from "@/hooks/useTableConfig";
import Content_Api from "@/api/content";
import {
  GetAcuPointTemplatePageDataParams,
  MoItemPageData,
  MoItemPageDataInputDTO,
} from "@/api/content/types";
import AcupointTemplateForm from "./components/AcupointTemplateForm.vue";

interface TableAcuPointTemplate extends BaseAcuPointTemplate {
  AcuPointNames?: string;
  MoNames?: string;
}

/** 调试开关 */
const kEnableDebug = false;
defineOptions({
  name: "AcupointTemplate",
});

const { pageData, tableLoading, tableFluidHeight, total, tableResize, tableDateFormat } =
  useTableConfig<TableAcuPointTemplate>();

/** 查询条件 */
const queryParams = reactive<GetAcuPointTemplatePageDataParams>({
  page: 1,
  pageSize: 20,
});

/** 点击搜索 */
function handleQuery() {
  queryParams.page = 1;
  requestTableList();
}

/** 医嘱列表 */
const moItemList = ref<MoItemPageData[]>([]);
const moItemListLoading = ref(false);

/** 请求医嘱列表 */
async function requestMoItemList() {
  moItemListLoading.value = true;
  const params: MoItemPageDataInputDTO = {
    page: 1,
    pageSize: 9999,
    isEnable: true,
    moItemMethod: 1,
  };
  const r = await Content_Api.getMoItemPageData(params);
  moItemListLoading.value = false;
  if (r.Type !== 200) {
    ElMessage.error(r.Content);
    return;
  }

  moItemList.value = r.Data.Data;
}

/** 查看/添加/编辑弹窗 */
const showDataDialog = reactive({
  isShow: false,
  title: "",
  disabled: false,
  data: {} as TableAcuPointTemplate, // 查看/添加/编辑详情
});

/** 点击添加 */
function onAddItem() {
  kEnableDebug && console.debug("点击添加");

  showDataDialog.title = "新增";
  showDataDialog.disabled = false;
  showDataDialog.data = {};
  showDataDialog.isShow = true;
}

/** 点击查看/编辑 */
async function onPreviewOrEdit(row: TableAcuPointTemplate, disabled: boolean = false) {
  kEnableDebug && console.debug("查看/编辑", row, disabled);

  showDataDialog.title = disabled ? "查看" : "编辑";
  showDataDialog.disabled = disabled;
  showDataDialog.data = row;
  showDataDialog.isShow = true;
}

/** 点击删除 */
async function onDeleteItem(row: TableAcuPointTemplate) {
  kEnableDebug && console.debug("删除", row);

  if (!row.Id) {
    ElMessage.error("id为空");
    return;
  }

  ElMessageBox.confirm("此操作将删除该条数据, 是否继续?", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(async () => {
    const r = await Content_Api.deleteAcuPointTemplate(row.Id!);
    if (r.Type !== 200) {
      ElMessage.error(r.Content);
      return;
    }

    ElNotification.success("删除成功");
    requestTableList();
  });
}

/** 确定新增提交 */
function onConfirmSubmitItem() {
  kEnableDebug && console.debug("确定提交");

  // 提交成功
  showDataDialog.isShow = false;
  ElNotification.success("提交成功");

  // 刷新列表
  requestTableList();
}

/** 请求列表数据 */
async function requestTableList() {
  tableLoading.value = true;
  const r = await Content_Api.getAcuPointTemplatePageData(queryParams);
  tableLoading.value = false;
  if (r.Type !== 200) {
    ElMessage.error(r.Content);
    return;
  }

  // 请求成功
  pageData.value = r.Data.Data.map((item) => ({
    ...item,
    AcuPointNames: item.AcuPointInfos?.map((e) => e.Name).join("，"),
    MoNames: item.MoItems?.map((e) => e.Name).join("，"),
  }));
  total.value = r.Data.TotalCount;
}

onMounted(() => {
  requestMoItemList();
});

onActivated(() => {
  requestTableList();
});
</script>

<style lang="scss" scoped></style>
