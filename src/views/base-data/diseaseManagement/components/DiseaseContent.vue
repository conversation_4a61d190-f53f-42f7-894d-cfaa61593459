<template>
  <div class="app-container">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="rules"
      label-width="80px"
      class="consumable-type-form"
      :disabled="isPreview"
      scroll-to-error
    >
      <el-form-item label="名称" prop="Key">
        <el-input v-model="formData.Key" placeholder="请输入名称" @blur="handleBlurKey" />
      </el-form-item>
      <el-form-item label="拼音码" prop="PinyinCode">
        <el-input v-model="formData.PinyinCode" placeholder="请输入拼音码" />
      </el-form-item>
      <el-form-item label="编码" prop="Value">
        <el-input v-model="formData.Value" placeholder="请输入编码" />
      </el-form-item>
      <el-form-item label="备注" prop="Remark">
        <el-input v-model="formData.Remark" type="textarea" placeholder="请输入备注" />
      </el-form-item>
      <el-form-item label="上级分类" prop="ParentId">
        <el-select
          v-model="formData.ParentId"
          placeholder="请选择上级分类"
          :empty-values="[null, undefined]"
        >
          <el-option
            v-for="item in superiorDysfunctionTypeList"
            :key="item.Id"
            :label="item.Key"
            :value="item.Id!"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="是否启用" prop="IsEnabled">
        <el-switch v-model="formData.IsEnabled" />
      </el-form-item>
      <el-form-item label="关联icd" prop="DictItemRelateds">
        <div class="flex w-full">
          <el-select
            v-model="tempSelectedIcdFromSelect"
            placeholder="请选择关联icd"
            multiple
            filterable
            remote
            :remote-method="handleRemoteSearchIcd"
            :loading="icdLoading"
            collapse-tags-tooltip
            style="flex: 1; margin-right: 10px"
            :max-collapse-tags="3"
          >
            <el-option v-for="item in icdList" :key="item.Id" :label="item.Key" :value="item.Id!" />
          </el-select>
          <el-button type="primary" @click="handleAddIcd">添加</el-button>
        </div>
      </el-form-item>
      <el-table
        :data="selectTableList"
        border
        row-key="Value"
        highlight-current-row
        style="width: 100%"
      >
        <el-table-column label="名称" prop="Key" align="center" />
        <el-table-column label="编码" prop="Value" align="center" />
        <el-table-column label="操作" width="100" align="center">
          <template #default="scope">
            <el-button type="primary" @click="handleDelete(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-form>
  </div>
</template>

<script setup lang="ts">
import Dictionary_Api from "@/api/dictionary";
import { DictCreateUpdateInputDTO } from "@/api/dictionary/types";
import { chineseToPinyin } from "@/utils/pinyin-helper";
import { getIcdList, RecoveryParams } from "@/utils/dict";
import { FormInstance, FormRules } from "element-plus";

const superiorDysfunctionTypeList = inject("superiorDysfunctionTypeList") as Ref<ReadDict[]>;

const isPreview = inject("isPreview") as Ref<boolean>;

const selectTableList = ref<ReadDict[]>([]);

const tempSelectedIcdFromSelect = ref<string[]>([]);

const formData = ref<DictCreateUpdateInputDTO>({
  Key: "",
  PinyinCode: "",
  Value: "",
  Remark: "",
  IsEnabled: true,
  OrgId: "",
  IsPublish: false,
  DictId: 18,
  ParentId: undefined,
});

const formRef = ref<FormInstance>();

const icdLoading = ref(false);

const icdParams = ref<RecoveryParams>({
  PageSize: 100,
  Key: "",
  IsEnabled: true,
  IsPublish: true,
});

const icdList = ref<ReadDict[]>([]);

const rules = reactive<FormRules>({
  Key: [
    { required: true, message: "请输入名称", trigger: "blur" },
    { min: 1, max: 50, message: "长度在 1 到 50 个字符", trigger: "blur" },
  ],
  PinyinCode: [{ required: true, message: "请输入拼音码", trigger: "blur" }],
  Value: [{ required: true, message: "请输入编码", trigger: "blur" }],
});
const handleGetSubmitParams = async (): Promise<DictCreateUpdateInputDTO | null> => {
  // 校验表单
  await formRef.value!.validate();

  if (selectTableList.value && selectTableList.value.length) {
    formData.value.DictItemRelateds = selectTableList.value.map((item) => ({
      DictItemIdA: item.Id!,
    }));
  } else {
    formData.value.DictItemRelateds = [];
  }

  return formData.value;
};

const handleProcessDetail = (value: ReadDict | null) => {
  if (!value) return;
  const defaultData: DictCreateUpdateInputDTO = {
    ...formData.value,
    Key: value.Key!,
    PinyinCode: value.PinyinCode!,
    Value: value.Value!,
    Remark: value.Remark!,
    IsEnabled: value.IsEnabled!,
    OrgId: value.OrgId!,
    IsPublish: value.IsPublish!,
    DictId: value.DictId ? value.DictId : 18,
    ParentId: value.ParentId!,
    Id: value.Id,
  };
  if (!value.Id) {
    delete defaultData.Id;
  }
  formData.value = { ...defaultData };
  // 获取已选择icd的详情数据
  onGetSelectedIcdListData(value.DictItemRelateds);
};

const handleDelete = (row: ReadDict) => {
  const index = selectTableList.value.findIndex((item) => item.Id === row.Id);
  selectTableList.value.splice(index, 1);
};

const onGetSelectedIcdListData = async (dictItemRelateds: DictItemRelated[] | undefined) => {
  if (!dictItemRelateds || !dictItemRelateds.length) return;
  const params = {
    PageCondition: {
      PageIndex: 1,
      PageSize: dictItemRelateds.length,
      SortConditions: [{ SortField: "CreatedTime", ListSortDirection: 1 }],
    },
    FilterGroup: {
      Rules: [
        { Field: "DictId", Value: "9", Operate: 3 },
        { Field: "IsEnabled", Value: true, Operate: 3 },
        { Field: "IsPublish", Value: true, Operate: 3 },
      ],
      Groups: [
        {
          Rules: [] as any,
          Operate: 2,
        },
      ],
      Operate: 1,
    },
  };
  dictItemRelateds.forEach((s) => {
    params.FilterGroup.Groups[0].Rules.push({ Field: "Id", Value: s.DictItemIdA, Operate: 3 });
  });
  const res = await Dictionary_Api.readDict(params);
  if (res.Type === 200) {
    selectTableList.value = res.Data.Rows;
  }
};

const handleBlurKey = () => {
  formData.value.PinyinCode = chineseToPinyin(formData.value.Key);
};

const handleAddIcd = () => {
  if (!tempSelectedIcdFromSelect.value || tempSelectedIcdFromSelect.value.length === 0) {
    return;
  }

  tempSelectedIcdFromSelect.value.forEach((selectedId) => {
    const isAlreadyInTable = selectTableList.value.some((tableItem) => tableItem.Id === selectedId);
    if (!isAlreadyInTable) {
      const itemToAdd = icdList.value.find((icdItem) => icdItem.Id === selectedId);
      if (itemToAdd) {
        selectTableList.value.push(itemToAdd);
      }
    }
  });

  tempSelectedIcdFromSelect.value = [];
};

interface Props {
  diseaseData: ReadDict | null;
  parentId?: string;
}
const props = defineProps<Props>();

const handleRemoteSearchIcd = async (query: string) => {
  if (query) {
    icdParams.value.Key = query;
    await onGetIcdListData();
  } else {
    // 当查询为空时，清除搜索关键字并重新加载列表，以显示所有或默认项
    icdParams.value.Key = "";
    await onGetIcdListData();
  }
};

const onGetIcdListData = async () => {
  icdLoading.value = true;
  try {
    const list = await getIcdList(icdParams.value);
    icdList.value = list;
  } catch (error) {
    console.error("Failed to fetch ICD list:", error);
    icdList.value = [];
  } finally {
    icdLoading.value = false;
  }
};

watch(
  () => props.diseaseData,
  (newVal) => {
    handleProcessDetail(newVal);
  },
  { immediate: true }
);
watch(
  () => props.parentId,
  (newVal) => {
    formData.value.ParentId = newVal;
  },
  { immediate: true }
);
defineExpose({
  handleGetSubmitParams,
});
</script>

<style lang="scss" scoped>
.app-container {
  height: 500px;
  overflow-y: auto;
}

:deep(.el-transfer-panel) {
  width: calc(50% - 80px);
}
</style>
