<template>
  <div class="app-container">
    <BaseTableSearchContainer @size-changed="tableResize">
      <template #search>
        <TBSearchContainer>
          <template #left>
            <div />
          </template>
          <template #right>
            <!-- <el-button v-if="showSpecialButton" type="primary" @click="handleAddDirectory">
              添加目录
            </el-button> -->
          </template>
        </TBSearchContainer>
      </template>
      <template #table>
        <el-table
          ref="tableRef"
          v-loading="tableLoading"
          row-key="path"
          :data="pageData"
          :tree-props="{
            children: 'children',
            hasChildren: 'hasChildren',
          }"
          class="data-table__content"
          border
        >
          <el-table-column label="菜单名称" width="220">
            <template #default="scope">
              <template v-if="scope.row.meta?.icon && scope.row.meta.icon.startsWith('el-icon')">
                <el-icon style="vertical-align: -0.15em">
                  <component :is="scope.row.meta.icon.replace('el-icon-', '')" />
                </el-icon>
              </template>
              <template v-else-if="scope.row.meta?.icon">
                <div :class="`i-svg:${scope.row.meta.icon}`" />
              </template>
              {{ scope.row.meta?.title || scope.row.name }}
            </template>
          </el-table-column>

          <el-table-column label="类型" align="center" width="100">
            <template #default="scope">
              <el-tag v-if="scope.row.component === 'Layout'" type="warning">目录</el-tag>
              <el-tag v-else type="success">菜单</el-tag>
            </template>
          </el-table-column>
          <el-table-column label="路由名称" align="left" width="150" prop="name" />
          <el-table-column label="路由路径" align="left" width="150" prop="path" />
          <el-table-column label="组件路径" align="left" width="250" prop="component" />
          <el-table-column label="重定向" align="center" width="100" prop="redirect" />
          <el-table-column label="状态" align="center" width="80">
            <template #default="scope">
              <el-tag v-if="scope.row.meta?.hidden === true" type="info">隐藏</el-tag>
              <el-tag v-else type="success">显示</el-tag>
            </template>
          </el-table-column>
          <el-table-column label="排序" align="center" width="80" prop="sort" />
          <el-table-column label="角色权限" align="center">
            <template #default="scope">
              <el-tag
                v-for="role in handleGetRoleNames(scope.row)"
                :key="role"
                size="small"
                class="mr-5px!"
              >
                {{ role }}
              </el-tag>
            </template>
          </el-table-column>
          <!-- <el-table-column
            fixed="right"
            align="center"
            label="操作"
            :width="showSpecialButton ? 220 : 80"
          >
            <template #default="scope">
              <el-button
                v-if="showSpecialButton"
                type="primary"
                link
                size="small"
                icon="plus"
                @click="handleAdd(scope.row)"
              >
                新增
              </el-button>
              <el-button
                type="primary"
                link
                size="small"
                icon="edit"
                @click="handleEdit(scope.row)"
              >
                编辑
              </el-button>
              <el-button
                v-if="showSpecialButton"
                type="danger"
                link
                size="small"
                icon="delete"
                @click="handleDelete(scope.row)"
              >
                删除
              </el-button>
            </template>
          </el-table-column> -->
        </el-table>
      </template>
    </BaseTableSearchContainer>

    <!-- 菜单编辑抽屉 -->
    <el-drawer
      v-model="drawerVisible"
      :title="drawerTitle"
      direction="rtl"
      size="600px"
      :before-close="handleDrawerClose"
    >
      <MenuForm
        :operation-type="operationType"
        :current-data="currentEditRow"
        :parent-menu-options="parentMenuOptions"
        @submit="handleSaveMenu"
        @cancel="handleDrawerClose"
      />
    </el-drawer>
  </div>
</template>

<script setup lang="ts">
import { useTableConfig } from "@/hooks/useTableConfig";
import { asyncRoutes } from "@/menu/index";
import type { RouteVO } from "@/api/system/menu";
import { ElMessage, ElMessageBox } from "element-plus";
import MenuForm from "./components/MenuForm.vue";
import { kDebug } from "@/utils";
import Passport_Api from "@/api/passport";

defineOptions({
  name: "MenuSetting",
});

const isPreview = ref<boolean>(false);
provide("isPreview", isPreview);

const roleList = ref<BaseRole[]>([]);

// 只有开发环境才显示特殊按钮
const showSpecialButton = computed(() => kDebug);

// 弹窗相关状态
const drawerVisible = ref<boolean>(false);
const drawerTitle = ref<string>("添加目录");
const currentEditRow = ref<RouteVO | null>(null);
const operationType = ref<"add" | "edit" | "addChild">("add");

const { tableLoading, pageData, tableRef, tableResize, dialogConfirmLoading } =
  useTableConfig<unknown>();

// 生成唯一GUID/UUID
const generateId = () => {
  // 使用crypto.randomUUID()生成标准UUID (如果支持)
  if (typeof crypto !== "undefined" && crypto.randomUUID) {
    return crypto.randomUUID();
  }

  // 降级方案：手动生成UUID v4格式
  return "xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g, function (c) {
    const r = (Math.random() * 16) | 0;
    const v = c === "x" ? r : (r & 0x3) | 0x8;
    return v.toString(16);
  });
};

// 构建父级菜单选项
const parentMenuOptions = computed(() => {
  const buildOptions = (routes: RouteVO[], level = 0): any[] => {
    return routes.map((route) => ({
      title: route.meta?.title || route.name,
      path: route.path,
      children: route.children ? buildOptions(route.children, level + 1) : undefined,
      disabled: level >= 2, // 限制最多两级
      id: route.id,
    }));
  };

  return buildOptions(pageData.value as RouteVO[]);
});

// 为路由数据添加父子关系
const processRouteData = (routes: RouteVO[], parentPath = ""): RouteVO[] => {
  return routes.map((route) => {
    const processedRoute = {
      ...route,
      id: generateId(),
      parentId: parentPath || null,
    };

    if (route.children && route.children.length > 0) {
      processedRoute.children = processRouteData(route.children, processedRoute.id);
    }

    return processedRoute;
  });
};

// 添加目录
const handleAddDirectory = () => {
  operationType.value = "add";
  drawerTitle.value = "添加目录";
  currentEditRow.value = null;
  drawerVisible.value = true;
};

// 新增子菜单
const handleAdd = (row: RouteVO) => {
  operationType.value = "addChild";
  drawerTitle.value = "新增菜单";
  currentEditRow.value = row;
  drawerVisible.value = true;
};

// 编辑菜单
const handleEdit = (row: RouteVO) => {
  operationType.value = "edit";
  drawerTitle.value = "编辑菜单";
  currentEditRow.value = { ...row };
  drawerVisible.value = true;
};

// 删除菜单
const handleDelete = async (row: RouteVO) => {
  try {
    // 检查是否有子菜单
    const hasChildren = row.children && row.children.length > 0;
    const confirmMessage = hasChildren
      ? `确定要删除菜单"${row.meta?.title || row.name}"吗？\n删除后其所有子菜单也将被删除，且无法恢复！`
      : `确定要删除菜单"${row.meta?.title || row.name}"吗？删除后将无法恢复！`;

    await ElMessageBox.confirm(confirmMessage, "删除确认", {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning",
      dangerouslyUseHTMLString: false,
    });

    // 执行删除逻辑（级联删除）
    deleteRouteFromData(row);

    // 数据持久化
    saveDataToStorage();

    ElMessage.success("删除成功");
  } catch {
    // 用户取消删除
  }
};

// 从数据中删除路由
const deleteRouteFromData = (targetRoute: RouteVO) => {
  const deleteFromArray = (routes: RouteVO[]): RouteVO[] => {
    return routes.filter((route) => {
      if (route.path === targetRoute.path) {
        return false;
      }
      if (route.children) {
        route.children = deleteFromArray(route.children);
      }
      return true;
    });
  };

  pageData.value = deleteFromArray(pageData.value as RouteVO[]);
};

// 关闭抽屉
const handleDrawerClose = () => {
  drawerVisible.value = false;
  currentEditRow.value = null;
};

// 验证菜单数据的有效性
const validateMenuData = (menuData: RouteVO): boolean => {
  // 验证parentId是否有效（如果存在）
  const parentId = (menuData as any).parentId;
  if (parentId) {
    const findParent = (routes: RouteVO[]): boolean => {
      for (const route of routes) {
        if ((route as any).id === parentId) {
          return true;
        }
        if (route.children && findParent(route.children)) {
          return true;
        }
      }
      return false;
    };

    if (!findParent(pageData.value as RouteVO[])) {
      ElMessage.error("选择的父级菜单不存在，请重新选择");
      return false;
    }
  }

  // 验证路径唯一性（编辑时排除当前菜单）
  const checkPathUnique = (routes: RouteVO[], targetPath: string, excludeId?: string): boolean => {
    for (const route of routes) {
      if (route.path === targetPath && (route as any).id !== excludeId) {
        return false;
      }
      if (route.children && !checkPathUnique(route.children, targetPath, excludeId)) {
        return false;
      }
    }
    return true;
  };

  // 验证路径是否存在
  if (!menuData.path) {
    ElMessage.error("路由路径不能为空");
    return false;
  }

  const excludeId = operationType.value === "edit" ? (menuData as any).id : undefined;
  if (!checkPathUnique(pageData.value as RouteVO[], menuData.path, excludeId)) {
    ElMessage.error("路由路径已存在，请使用不同的路径");
    return false;
  }

  return true;
};

// 保存菜单数据
const handleSaveMenu = (menuData: RouteVO) => {
  // 数据验证
  if (!validateMenuData(menuData)) {
    return;
  }

  try {
    if (operationType.value === "add") {
      // 添加新的顶级目录
      (pageData.value as RouteVO[]).push(menuData);
    } else if (operationType.value === "addChild") {
      // 添加子菜单
      addChildToRoute(menuData, currentEditRow.value!);
    } else if (operationType.value === "edit") {
      // 编辑现有菜单
      updateRouteInData(menuData);
    }

    // 重新排序
    sortRoutes(pageData.value as RouteVO[]);

    // 数据持久化
    saveDataToStorage();

    handleDrawerClose();
    ElMessage.success("保存成功");
  } catch (error) {
    console.error("保存菜单数据时发生错误:", error);
    ElMessage.error("保存失败，请重试");
  }
};

// 添加子菜单到指定路由
const addChildToRoute = (childRoute: RouteVO, parentRoute: RouteVO) => {
  const addToArray = (routes: RouteVO[]) => {
    routes.forEach((route) => {
      if (route.path === parentRoute.path) {
        if (!route.children) {
          route.children = [];
        }
        route.children.push(childRoute);
      } else if (route.children) {
        addToArray(route.children);
      }
    });
  };

  addToArray(pageData.value as RouteVO[]);
};

// 更新路由数据（支持跨目录编辑）
const updateRouteInData = (updatedRoute: RouteVO) => {
  const originalPath = currentEditRow.value?.path;
  if (!originalPath) {
    console.error("更新路由数据失败：找不到原始路径");
    return;
  }

  try {
    // 先从原位置删除
    const deleteFromArray = (routes: RouteVO[]): RouteVO[] => {
      return routes.filter((route) => {
        if (route.path === originalPath) {
          return false;
        }
        if (route.children) {
          route.children = deleteFromArray(route.children);
        }
        return true;
      });
    };

    pageData.value = deleteFromArray(pageData.value as RouteVO[]);

    // 然后添加到新位置
    const parentId = (updatedRoute as any).parentId;
    if (parentId) {
      // 添加到指定父级菜单下
      let parentFound = false;
      const addToParent = (routes: RouteVO[]) => {
        routes.forEach((route) => {
          if ((route as any).id === parentId) {
            if (!route.children) {
              route.children = [];
            }
            route.children.push(updatedRoute);
            parentFound = true;
          } else if (route.children) {
            addToParent(route.children);
          }
        });
      };
      addToParent(pageData.value as RouteVO[]);

      if (!parentFound) {
        console.error("更新路由数据失败：找不到指定的父级菜单");
        ElMessage.error("更新失败：找不到指定的父级菜单");
        return;
      }
    } else {
      // 添加到顶级
      (pageData.value as RouteVO[]).push(updatedRoute);
    }
  } catch (error) {
    console.error("更新路由数据时发生错误:", error);
    ElMessage.error("更新菜单数据失败，请重试");
  }
};

// 路由排序
const sortRoutes = (routes: RouteVO[]) => {
  routes.sort((a, b) => (a.sort ?? 0) - (b.sort ?? 0));
  routes.forEach((route) => {
    if (route.children) {
      sortRoutes(route.children);
    }
  });
};

// 数据持久化相关
const STORAGE_KEY = "debug_menu_data";

// 保存数据到localStorage
const saveDataToStorage = () => {
  try {
    const dataToSave = JSON.stringify(pageData.value);
    localStorage.setItem(STORAGE_KEY, dataToSave);
    console.log("菜单数据已保存到localStorage");
  } catch (error) {
    console.error("保存菜单数据失败:", error);
  }
};

// 从localStorage加载数据
const loadDataFromStorage = (): RouteVO[] | null => {
  try {
    const savedData = localStorage.getItem(STORAGE_KEY);
    if (savedData) {
      return JSON.parse(savedData);
    }
  } catch (error) {
    console.error("加载菜单数据失败:", error);
  }
  return null;
};

const handleGetRoleNames = (row: RouteVO): string[] => {
  if (row.meta?.roles) {
    return row.meta.roles.map((role) => {
      const roleItem = roleList.value.find((item) => item.RoleType === role);
      return roleItem?.Name!;
    });
  }
  return [];
};

const handleGetTableList = async () => {
  const processedData = processRouteData(asyncRoutes);
  pageData.value = processedData;
};

const onGetRoleList = async () => {
  const r = await Passport_Api.read({});
  if (r.Type === 200) {
    roleList.value = r.Data;
  }
};

onMounted(() => {
  handleGetTableList();
  // 获取角色信息
  onGetRoleList();
});
</script>

<style lang="scss" scoped></style>
