<template>
  <div v-loading="formLoading" class="p-20px overflow-y-auto">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="rules"
      label-width="120px"
      :disabled="disabled"
    >
      <!-- 基础信息 -->
      <el-form-item label="菜单类型" prop="menuType">
        <el-radio-group v-model="formData.menuType" :disabled="isEditMode">
          <el-radio value="directory">目录</el-radio>
          <el-radio value="menu">菜单</el-radio>
        </el-radio-group>
      </el-form-item>

      <el-form-item v-if="showParentSelect" label="父级菜单" prop="parentId">
        <el-tree-select
          v-model="formData.parentId"
          :data="parentMenuOptions"
          :props="{ label: 'title', value: 'id', children: 'children' }"
          placeholder="请选择父级菜单"
          clearable
          check-strictly
          :render-after-expand="false"
          :disabled="isEditMode"
        />
      </el-form-item>

      <el-form-item label="菜单名称" prop="title">
        <el-input
          v-model="formData.title"
          placeholder="请输入菜单名称"
          maxlength="50"
          show-word-limit
          clearable
        />
      </el-form-item>

      <el-form-item prop="path">
        <template #label>
          <div class="flex-y-center">
            路由路径
            <el-tooltip placement="bottom" effect="light">
              <template #content>浏览器的url路径</template>
              <el-icon class="ml-1 cursor-pointer">
                <QuestionFilled />
              </el-icon>
            </el-tooltip>
          </div>
        </template>
        <el-input
          v-model="formData.path"
          placeholder="请输入路由路径"
          maxlength="200"
          clearable
          :disabled="isEditMode"
        />
      </el-form-item>

      <el-form-item prop="name">
        <template #label>
          <div class="flex-y-center">
            路由名称
            <el-tooltip placement="bottom" effect="light">
              <template #content>路由的Name 一般是大写开头</template>
              <el-icon class="ml-1 cursor-pointer">
                <QuestionFilled />
              </el-icon>
            </el-tooltip>
          </div>
        </template>
        <el-input
          v-model="formData.name"
          placeholder="请输入路由名称"
          maxlength="50"
          clearable
          :disabled="isEditMode"
        />
      </el-form-item>

      <el-form-item v-if="formData.menuType !== 'directory'" prop="component">
        <template #label>
          <div class="flex-y-center">
            组件路径
            <el-tooltip placement="bottom" effect="light">
              <template #content>该菜单代码存在的源码地址</template>
              <el-icon class="ml-1 cursor-pointer">
                <QuestionFilled />
              </el-icon>
            </el-tooltip>
          </div>
        </template>
        <el-input
          v-model="formData.component"
          placeholder="请输入组件路径"
          maxlength="200"
          clearable
          :disabled="isEditMode"
        >
          <template #prepend>src/views/</template>
          <template #append>.vue</template>
        </el-input>
      </el-form-item>

      <el-form-item label="重定向" prop="redirect">
        <el-input
          v-model="formData.redirect"
          placeholder="请输入重定向路径"
          maxlength="200"
          clearable
          :disabled="isEditMode"
        />
      </el-form-item>

      <el-form-item label="菜单图标" prop="icon">
        <IconSelect v-model="formData.icon" />
      </el-form-item>

      <el-form-item label="排序" prop="sort">
        <el-input-number v-model="formData.sort" :min="0" :max="9999" placeholder="请输入排序值" />
      </el-form-item>

      <!-- 显示设置 -->
      <el-divider content-position="left">显示设置</el-divider>

      <el-form-item label="是否隐藏菜单">
        <el-switch v-model="formData.hidden" />
      </el-form-item>

      <el-form-item v-if="formData.menuType === 'directory'">
        <template #label>
          <div class="flex-y-center">
            始终显示
            <el-tooltip placement="bottom" effect="light">
              <template #content>只有一个子路由时是否始终显示父级菜单</template>
              <el-icon class="ml-1 cursor-pointer">
                <QuestionFilled />
              </el-icon>
            </el-tooltip>
          </div>
        </template>
        <el-switch v-model="formData.alwaysShow" :disabled="isEditMode" />
      </el-form-item>

      <el-form-item v-if="formData.menuType === 'menu'">
        <template #label>
          <div class="flex-y-center">
            页面缓存
            <el-tooltip placement="bottom" effect="light">
              <template #content>是否切换菜单保留之前的操作，建议开启</template>
              <el-icon class="ml-1 cursor-pointer">
                <QuestionFilled />
              </el-icon>
            </el-tooltip>
          </div>
        </template>
        <el-switch v-model="formData.keepAlive" />
      </el-form-item>

      <!-- 权限设置 -->
      <el-divider content-position="left">权限设置</el-divider>

      <el-form-item label="角色权限" prop="roles">
        <el-select
          v-model="formData.roles"
          multiple
          placeholder="请选择角色权限"
          collapse-tags
          collapse-tags-tooltip
          style="width: 100%"
        >
          <el-option
            v-for="role in roleOptions"
            :key="role.value"
            :label="role.label"
            :value="role.value"
          />
        </el-select>
      </el-form-item>
    </el-form>

    <div class="flex justify-end mt-20px">
      <el-button @click="handleCancel">取消</el-button>
      <el-button type="primary" :loading="submitLoading" @click="handleSubmit">确定</el-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { FormInstance, FormRules } from "element-plus";
import type { RouteVO } from "@/api/system/menu";
import IconSelect from "@/components/IconSelect/index.vue";

interface MenuFormData {
  menuType: "directory" | "menu";
  parentId: string | null;
  title: string;
  name: string;
  path: string;
  component: string;
  redirect: string;
  icon: string;
  sort: number;
  hidden: boolean;
  alwaysShow: boolean;
  keepAlive: boolean;
  roles: string[];
}

interface Props {
  /** 是否禁用表单 */
  disabled?: boolean;
  /** 操作类型 */
  operationType: "add" | "edit" | "addChild";
  /** 当前编辑的数据 */
  currentData?: RouteVO | null;
  /** 父级菜单选项 */
  parentMenuOptions: any[];
}

const props = withDefaults(defineProps<Props>(), {
  disabled: false,
  currentData: null,
});

const emit = defineEmits<{
  submit: [data: RouteVO];
  cancel: [];
}>();

const formRef = ref<FormInstance>();
const formLoading = ref(false);
const submitLoading = ref(false);

// 角色选项
const roleOptions = [
  { label: "超级管理员", value: "superAdmin" },
  { label: "超级操作员", value: "superOperate" },
  { label: "销售", value: "sales" },
  { label: "运营", value: "operations" },
  { label: "助理", value: "assistant" },
  { label: "财务", value: "finance" },
  { label: "仓储", value: "storage" },
  { label: "互联网医院管理员", value: "internetHospitalAdmin" },
  { label: "外部销售", value: "externalSeller" },
  { label: "科技展览", value: "scienceTechnologyExhibition" },
  { label: "店铺操作", value: "shopOperate" },
  { label: "视光筛查管理员", value: "visionScreenAdmin" },
];

// 表单数据
const formData = reactive<MenuFormData>({
  menuType: "directory",
  parentId: null,
  title: "",
  name: "",
  path: "",
  component: "",
  redirect: "",
  icon: "",
  sort: 0,
  hidden: false,
  alwaysShow: false,
  keepAlive: false,
  roles: [],
});

// 是否显示父级菜单选择
const showParentSelect = computed(() => {
  return props.operationType === "edit" || props.operationType === "add";
});

// 是否为编辑模式
const isEditMode = computed(() => {
  return props.operationType === "edit";
});

// 表单验证规则
const rules = computed<FormRules<MenuFormData>>(() => {
  const baseRules: FormRules<MenuFormData> = {
    title: [
      { required: true, message: "请输入菜单名称", trigger: "blur" },
      { min: 1, max: 50, message: "长度在 1 到 50 个字符", trigger: "blur" },
    ],
    sort: [
      { required: true, message: "请输入排序值", trigger: "blur" },
      { type: "number", min: 0, max: 9999, message: "排序值范围为 0-9999", trigger: "blur" },
    ],
  };

  // 编辑模式下不需要验证这些字段
  if (!isEditMode.value) {
    baseRules.name = [
      { required: true, message: "请输入路由名称", trigger: "blur" },
      { min: 1, max: 50, message: "长度在 1 到 50 个字符", trigger: "blur" },
    ];
    baseRules.path = [
      { required: true, message: "请输入路由路径", trigger: "blur" },
      { min: 1, max: 200, message: "长度在 1 到 200 个字符", trigger: "blur" },
    ];
    baseRules.component = [
      {
        validator: (_rule, value, callback) => {
          if (formData.menuType !== "directory" && !value) {
            callback(new Error("请输入组件路径"));
          } else {
            callback();
          }
        },
        trigger: "blur",
      },
    ];
  }

  return baseRules;
});

// 判断菜单类型
const determineMenuType = (data: RouteVO): "directory" | "menu" => {
  if (data.component === "Layout") {
    return "directory";
  } else {
    return "menu";
  }
};

// 重置表单数据
const resetFormData = () => {
  Object.assign(formData, {
    menuType: props.operationType === "add" ? "directory" : "menu",
    parentId: props.operationType === "addChild" ? props.currentData?.id || null : null,
    title: "",
    name: "",
    path: "",
    component: props.operationType === "add" ? "Layout" : "",
    redirect: "",
    icon: "",
    sort: 0,
    hidden: false,
    alwaysShow: false,
    keepAlive: true,
    roles: [],
  });
};

// 设置基础路由信息
const setBasicRouteInfo = (data: RouteVO) => {
  formData.name = data.name || "";
  formData.path = data.path || "";
  formData.component = data.component || "";
  formData.redirect = data.redirect || "";
  formData.sort = data.sort || 0;
  formData.parentId = (data as any).parentId || null;
  formData.menuType = determineMenuType(data);
};

// 设置元数据信息
const setMetaInfo = (data: RouteVO) => {
  const meta = data.meta || {};
  formData.title = meta.title || "";
  formData.icon = meta.icon || "";
  formData.hidden = meta.hidden || false;
  formData.alwaysShow = meta.alwaysShow || false;
  formData.keepAlive = meta.keepAlive || false;
  formData.roles = meta.roles || [];
};

// 初始化编辑模式表单数据
const initEditFormData = (data: RouteVO) => {
  setBasicRouteInfo(data);
  setMetaInfo(data);
};

// 初始化表单数据
const initFormData = () => {
  if (props.currentData && props.operationType === "edit") {
    initEditFormData(props.currentData);
  } else {
    resetFormData();
  }
};

// 创建基础路由数据
const createBaseRouteData = (): RouteVO => {
  return {
    name: formData.name,
    path: formData.path,
    component: formData.menuType === "directory" ? "Layout" : formData.component,
    redirect: formData.redirect || undefined,
    sort: formData.sort,
    meta: {
      title: formData.title,
      icon: formData.icon || undefined,
      hidden: formData.hidden,
      alwaysShow: formData.alwaysShow,
      keepAlive: formData.keepAlive,
      roles: formData.roles as any,
    },
  };
};

// 处理编辑模式数据
const processEditModeData = (routeData: RouteVO): void => {
  if (!props.currentData) return;

  // 保留原有的id
  (routeData as any).id = props.currentData.id;

  // 保留原有的children数组（编辑目录时很重要）
  if (formData.menuType === "directory" && props.currentData.children) {
    routeData.children = props.currentData.children;
  }

  // 使用用户设置的parentId（优先级高于原有parentId）
  (routeData as any).parentId = formData.parentId;
};

// 处理新增模式数据
const processAddModeData = (routeData: RouteVO): void => {
  // 添加parentId用于跨目录编辑
  (routeData as any).parentId = formData.parentId;

  // 如果是目录类型，初始化children数组
  if (formData.menuType === "directory") {
    routeData.children = [];
  }
};

// 提交表单
const handleSubmit = async () => {
  try {
    await formRef.value?.validate();
    submitLoading.value = true;

    // 构造基础路由数据
    const routeData = createBaseRouteData();

    // 根据操作类型处理数据
    if (props.operationType === "edit") {
      processEditModeData(routeData);
    } else {
      processAddModeData(routeData);
    }

    emit("submit", routeData);
  } catch (error) {
    console.error("表单验证失败:", error);
  } finally {
    submitLoading.value = false;
  }
};

// 取消操作
const handleCancel = () => {
  emit("cancel");
};

// 监听props变化，重新初始化表单
watch(
  () => [props.currentData, props.operationType],
  () => {
    initFormData();
  },
  { immediate: true }
);
</script>

<style lang="scss" scoped>
.el-divider {
  margin: 20px 0;
}
</style>
