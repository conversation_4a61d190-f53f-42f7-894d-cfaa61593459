<template>
  <div class="app-container">
    <BaseTableSearchContainer @size-changed="tableResize">
      <!-- 列表 -->
      <template #table>
        <el-table
          :ref="kTableRef"
          v-loading="tableLoading"
          :data="pageData"
          :total="total"
          row-key="Id"
          :height="tableFluidHeight"
          :header-cell-style="{ textAlign: 'center' }"
          :cell-style="{ textAlign: 'center' }"
          border
          highlight-current-row
          @selection-change="(selection) => handleSelectionChange(selection, 'Id')"
        >
          <el-table-column prop="Name" label="名称" width="180" />
          <el-table-column prop="Names" label="提醒时间" />
          <!-- 操作 -->
          <el-table-column fixed="right" label="操作" width="150">
            <template #default="scope">
              <el-button link type="primary" @click="onPreviewOrEdit(scope.row, true)">
                查看
              </el-button>
              <el-button link type="primary" @click="onPreviewOrEdit(scope.row, false)">
                编辑
              </el-button>
              <el-button link type="primary" @click="onPush(scope.row)">推送</el-button>
            </template>
          </el-table-column>
        </el-table>
      </template>
      <!-- 分页 -->
      <template #pagination>
        <Pagination
          v-if="total > 0"
          v-model:total="total"
          v-model:page="queryParams.PageIndex"
          v-model:limit="queryParams.PageSize"
          @pagination="requestTableList"
        />
      </template>
    </BaseTableSearchContainer>
  </div>

  <!-- 添加/编辑/查看 -->
  <el-dialog
    v-model="showDataDialog.isShow"
    :title="showDataDialog.title"
    width="700px"
    destroy-on-close
    @close="showDataDialog.isShow = false"
  >
    <ReturnHospitalReminderForm
      :group-id="showDataDialog.groupId"
      :disabled="showDataDialog.disabled"
      @cancel="showDataDialog.isShow = false"
      @submit="onConfirmSubmitItem"
    />
  </el-dialog>

  <!-- 选择机构 -->
  <el-dialog v-model="showOrgDialog" title="选择机构" width="700px" destroy-on-close>
    <HospitalTransfer
      :loading="orgDialogLoading"
      @cancel="showOrgDialog = false"
      @submit="onConfirmOrganizations"
    />
  </el-dialog>
</template>

<script setup lang="ts">
import { useTableConfig } from "@/hooks/useTableConfig";
import { RxTempBackRemind } from "@/api/consult/types";
import Consult_Api from "@/api/consult";
import useOrgDialog from "@/hooks/useOrgDialog";
import { useUserStore } from "@/store";
import ReturnHospitalReminderForm from "./components/ReturnHospitalReminderForm.vue";

/** 调试开关 */
const kEnableDebug = false;
defineOptions({
  name: "ReturnHospitalReminder",
});

const {
  kTableRef,
  tableRef,
  pageData,
  tableLoading,
  tableFluidHeight,
  total,
  tableResize,
  handleSelectionChange,
  selectedTableIds,
} = useTableConfig<RxTempBackRemind>();

const { showOrgDialog, orgDialogLoading } = useOrgDialog();

const queryParams = reactive({
  PageIndex: 1,
  PageSize: 20,
});

/** 查看/编辑弹窗 */
const showDataDialog = reactive({
  isShow: false,
  title: "",
  disabled: false,
  groupId: "", // 查看/编辑详情
});

/** 点击查看/编辑 */
async function onPreviewOrEdit(row: RxTempBackRemind, disabled: boolean = false) {
  kEnableDebug && console.debug("查看/编辑", row, disabled);

  if (!row.GroupId) {
    ElMessage.warning("GroupId为空");
    return;
  }

  showDataDialog.title = disabled ? "查看" : "编辑";
  showDataDialog.disabled = disabled;
  showDataDialog.groupId = row.GroupId;
  showDataDialog.isShow = true;
}

/** 确定新增提交 */
function onConfirmSubmitItem() {
  kEnableDebug && console.debug("确定提交");

  // 提交成功
  showDataDialog.isShow = false;
  ElNotification.success("提交成功");

  // 刷新列表
  requestTableList();
}

/** 推送 */
function onPush(row: RxTempBackRemind) {
  kEnableDebug && console.debug("推送", row);

  if (!row.GroupId) {
    ElMessage.warning("GroupId为空");
    return;
  }

  selectedTableIds.value = [row.GroupId!];
  showOrgDialog.value = true;
}

/** 确定选择机构 */
async function onConfirmOrganizations(organizationIds: string[]) {
  kEnableDebug && console.log("选择机构", organizationIds);

  if (!organizationIds.length) {
    ElMessage.warning("请选择推送机构");
    return;
  }

  // 推送
  orgDialogLoading.value = true;
  const r = await Consult_Api.pushRxTempBackRemind({
    GroupId: selectedTableIds.value[0],
    OrgIds: organizationIds,
    UserId: useUserStore().userInfo.Id,
  });
  orgDialogLoading.value = false;
  if (r.Type !== 200) {
    ElMessage.error(r.Content);
    return;
  }

  showOrgDialog.value = false;
  ElNotification.success("推送成功");

  // 清空选项
  selectedTableIds.value = [];
  tableRef.value?.clearSelection();
}

/** 请求列表数据 */
async function requestTableList() {
  tableLoading.value = true;
  const params = {
    ...queryParams,
    IsGroup: true,
  };
  const r = await Consult_Api.queryRxTempBackRemind(params);
  tableLoading.value = false;
  if (r.Type !== 200) {
    ElMessage.error(r.Content);
    return;
  }

  // 请求成功
  pageData.value = r.Data.Rows;
  total.value = r.Data.Total;
}

onActivated(() => {
  requestTableList();
});
</script>

<style lang="scss" scoped></style>
