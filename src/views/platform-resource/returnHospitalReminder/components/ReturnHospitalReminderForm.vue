<template>
  <div v-loading="formLoading" class="p-20px">
    <el-form
      :ref="kFormRef"
      class="h-550px overflow-y-auto"
      :model="formData"
      label-width="auto"
      :disabled="props.disabled"
    >
      <el-form-item required label="名称">
        <el-input v-model="name" placeholder="请输入名称" />
      </el-form-item>
      <el-button class="mb-15px" type="primary" @click="addItem">
        <el-icon class="mr-5px"><CirclePlus /></el-icon>
        添加
      </el-button>
      <template v-for="(item, index) in formData" :key="item.Id" shadow="never">
        <el-row>
          <el-form-item
            label="提醒时间"
            :prop="`[${index}].Type`"
            :rules="{ required: true, message: '请选择提醒时间', trigger: 'change' }"
          >
            <el-radio-group v-model="item.Type">
              <el-radio :value="1">出院</el-radio>
              <el-radio :value="2">术后</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item
            label="时间"
            :prop="`[${index}].Day`"
            :rules="{ required: true, message: '请输入天数', trigger: 'blur' }"
          >
            <el-input-number
              v-model="item.Day"
              placeholder="天数"
              :min="0"
              :precision="0"
              @change="checkSameData(index)"
            />
            <el-text class="ml-5px!">天</el-text>
          </el-form-item>
          <el-button class="ml-35px" type="primary" @click="removeItem(index)">
            <el-icon><Delete /></el-icon>
          </el-button>
        </el-row>
        <el-form-item label="备注" :prop="`[${index}].Remark`">
          <el-input
            v-model="item.Remark"
            type="textarea"
            :autosize="{ minRows: 2, maxRows: 4 }"
            placeholder="请输入备注"
          />
        </el-form-item>
      </template>
    </el-form>
  </div>
  <!-- 底部按钮 -->
  <div class="flex justify-end">
    <el-button @click="emit('cancel')">取消</el-button>
    <el-button v-if="!props.disabled" type="primary" @click="onSubmitForm()">确定</el-button>
  </div>
</template>

<script setup lang="ts">
import { FormInstance } from "element-plus";
import { useUserStore } from "@/store";
import { OperaRxTempBackRemindParams, RxTempBackRemind } from "@/api/consult/types";
import Consult_Api from "@/api/consult";

const kEnableDebug = false;
const kFormRef = "ruleFormRef";

defineOptions({
  name: "ReturnHospitalReminderForm",
});

const props = defineProps<{
  groupId: string;
  disabled: boolean;
}>();

const emit = defineEmits<{
  cancel: [];
  submit: [];
}>();

const formLoading = ref(false);
/** 表单实例 */
const formRef = useTemplateRef<FormInstance>(kFormRef);
/** 表单数据 */
const formData = ref<RxTempBackRemind[]>([]);

/** 名称 */
const name = ref("");

/** 检查是否有相同提醒时间和时间的数据 */
function checkSameData(index: number) {
  const type = formData.value[index].Type;
  const day = formData.value[index].Day;
  const isSame = formData.value.some(
    (i, iIndex) => i.Type === type && i.Day === day && iIndex !== index
  );
  if (isSame) {
    ElMessage.warning("存在相同的回院提醒");
  }
}

/** 添加数据 */
function addItem() {
  formData.value.push({
    GroupId: props.groupId,
    IsDefault: true,
    Type: 1,
    Remark: "回院复诊",
    Sort: 0,
  });
}

/** 删除数据 */
function removeItem(index: number) {
  formData.value.splice(index, 1);
}

/** 提交表单 */
function onSubmitForm() {
  if (!formRef.value) return;

  if (!name.value.trim()) {
    ElMessage.warning("请输入名称");
    return;
  }

  formRef.value.validate((valid, fields) => {
    if (valid) {
      // 检查是否存在相同的回院提醒
      const isSame = formData.value.some(
        (e, index, arr) => arr.findIndex((u) => u.Day === e.Day && u.Type === e.Type) !== index
      );
      if (isSame) {
        ElMessage.warning("存在相同的回院提醒");
        return;
      }

      requestAddOrUpdateData();
    } else {
      kEnableDebug && console.debug("提交失败", fields);
    }
  });
}

/** 添加/更新数据 */
async function requestAddOrUpdateData() {
  kEnableDebug && console.debug("添加/更新数据", formData);

  formLoading.value = true;
  const userId = useUserStore().userInfo.Id;
  const params: OperaRxTempBackRemindParams = {
    GroupId: props.groupId,
    Data: formData.value.map((e) => ({
      ...e,
      Name: name.value,
    })),
    OperaId: userId,
  };
  const r = await Consult_Api.operaRxTempBackRemind(params);
  formLoading.value = false;
  if (r.Type === 200) {
    emit("submit");
  } else {
    ElMessage.error(r.Content);
  }
}

/** 请求回院提醒数据 */
async function requestRxTempBackRemindData() {
  formLoading.value = true;
  const r = await Consult_Api.queryRxTempBackRemind({
    GroupId: props.groupId,
    PageIndex: 1,
    PageSize: 1000,
    IsGroup: false,
  });
  formLoading.value = false;
  if (r.Type !== 200) {
    ElMessage.error(r.Content);
    return;
  }

  name.value = r.Data.Rows[0].Name ?? "";
  formData.value = r.Data.Rows;
}

onMounted(() => {
  requestRxTempBackRemindData();
});
</script>

<style lang="scss" scoped></style>
