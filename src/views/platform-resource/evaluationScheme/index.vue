<template>
  <div class="app-container">
    <BaseTableSearchContainer @size-changed="tableResize">
      <template #search>
        <TBSearchContainer>
          <template #left>
            <div />
          </template>
          <template #right>
            <el-button type="primary" @click="handlePreviewOrEdit(null, false)">新增</el-button>
          </template>
        </TBSearchContainer>
      </template>
      <template #table>
        <el-table
          ref="tableRef"
          v-loading="tableLoading"
          :data="pageData"
          :total="total"
          border
          row-key="Id"
          :height="tableFluidHeight"
          highlight-current-row
          style="text-align: center; flex: 1"
        >
          <el-table-column prop="Name" label="方案名称" align="center" />
          <el-table-column prop="Remark" label="说明" align="center" />
          <el-table-column label="是否启用" align="center">
            <template #default="scope">
              {{ scope.row.IsEnable ? "是" : "否" }}
            </template>
          </el-table-column>
          <el-table-column fixed="right" label="操作" width="180" align="center">
            <template #default="scope">
              <el-button link type="primary" @click="handlePreviewOrEdit(scope.row, true)">
                查看
              </el-button>
              <el-button link type="primary" @click="handlePreviewOrEdit(scope.row, false)">
                编辑
              </el-button>
              <el-button link type="primary" @click="handleDelete(scope.row)">删除</el-button>
            </template>
          </el-table-column>
        </el-table>
      </template>
      <template #pagination>
        <Pagination
          v-if="total > 0"
          v-model:total="total"
          v-model:page="queryParams.PageIndex"
          v-model:limit="queryParams.PageSize"
          @pagination="handleGetTableList"
        />
      </template>
    </BaseTableSearchContainer>
    <el-dialog
      v-model="showDialog"
      :title="dialogTitle"
      width="900"
      destroy-on-close
      :close-on-press-escape="isPreview"
      :close-on-click-modal="isPreview"
    >
      <EvaluationContent :id="evaluationId" ref="evaluationContentRef" />
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="showDialog = false">取消</el-button>
          <el-button
            v-if="!isPreview"
            type="primary"
            :loading="dialogConfirmLoading"
            @click="handleSubmit"
          >
            确定
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { useTableConfig } from "@/hooks/useTableConfig";
import { GaugeSchemePageItem } from "@/api/training/types";
import Training_Api from "@/api/training";
import EvaluationContent from "./components/EvaluationContent.vue";

defineOptions({
  name: "EvaluationScheme",
});

const queryParams = ref<{
  PageIndex: number;
  PageSize: number;
}>({
  PageIndex: 1,
  PageSize: 20,
});

const isPreview = ref<boolean>(false);
provide("isPreview", isPreview);

const showDialog = ref<boolean>(false);
const dialogTitle = ref<string>("查看");
const evaluationContentRef = useTemplateRef("evaluationContentRef");
const evaluationId = ref<string>("");

const {
  tableLoading,
  pageData,
  total,
  tableRef,
  tableFluidHeight,
  tableResize,
  dialogConfirmLoading,
} = useTableConfig<GaugeSchemePageItem>();
const handlePreviewOrEdit = async (row: GaugeSchemePageItem | null, isPreviewState: boolean) => {
  isPreview.value = row ? isPreviewState : false;
  evaluationId.value = row?.Id || "";
  showDialog.value = true;
};
const handleGetTableList = async () => {
  tableLoading.value = true;
  const res = await Training_Api.getGaugeSchemePage(queryParams.value);
  if (res.Type === 200) {
    pageData.value = res.Data.Data;
    total.value = res.Data.Total;
  }
  tableLoading.value = false;
};

const handleSubmit = async () => {
  const params = await evaluationContentRef.value?.handleSubmit();
  if (params) {
    dialogConfirmLoading.value = true;
    Training_Api.insertOrUpdateGaugeScheme(params)
      .then((res) => {
        if (res.Type === 200) {
          showDialog.value = false;
          ElMessage.success(res.Content);
          handleGetTableList();
        } else {
          ElMessage.error(res.Content);
        }
      })
      .finally(() => {
        dialogConfirmLoading.value = false;
      });
  }
};

const handleDelete = async (row: GaugeSchemePageItem) => {
  ElMessageBox.confirm("确定删除该方案吗？", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(async () => {
    const res = await Training_Api.deleteGaugeScheme({ Id: row.Id! });
    if (res.Type === 200) {
      ElMessage.success(res.Content);
      handleGetTableList();
    } else {
      ElMessage.error(res.Content);
    }
  });
};

onActivated(() => {
  handleGetTableList();
});
</script>

<style lang="scss" scoped></style>
