<template>
  <el-form
    ref="formRef"
    :model="ruleForm"
    :rules="rules"
    label-width="auto"
    :inline="true"
    :disabled="isPreview"
  >
    <el-form-item label="方案名称" prop="Name">
      <el-input v-model="ruleForm.Name" placeholder="请输入方案名称" style="width: 200px" />
    </el-form-item>
    <el-form-item label="是否启用" prop="IsEnable" style="margin-left: 20px">
      <el-switch v-model="ruleForm.IsEnable" active-color="#13ce66" inactive-color="#ff4949" />
    </el-form-item>
    <br />
    <el-form-item label="说明" prop="Remark">
      <el-input
        v-model="ruleForm.Remark"
        type="textarea"
        placeholder="请输入方案说明"
        class="w-400px!"
      />
    </el-form-item>
    <TableTransfer
      v-model:pending-list="gaugeList"
      v-model:selected-list="ruleForm.Details"
      rowKey="GaugeId"
      :pending-loading="gaugeListLoading"
      :pending-search-config="{
        placeholder: '请输入量表名称',
        searchMethod: handleFilterGaugeList,
      }"
      :pending-page-config="{
        pageChangeMethod: handleFilterGaugeList,
        pendingTotal: gaugeListTotal,
      }"
      :selected-loading="selectedLoading"
      :disabled="isPreview"
    >
      <template #pending>
        <el-table-column prop="Name" label="名称" />
        <el-table-column prop="Code" label="编码" />
      </template>
      <template #selected>
        <el-table-column prop="IsDoctor" label="医生填写" width="80">
          <template #default="scope">
            <el-switch
              v-model="scope.row.IsDoctor"
              active-color="#13ce66"
              inactive-color="#ff4949"
            />
          </template>
        </el-table-column>
        <el-table-column prop="GaugeName" label="名称" />
      </template>
    </TableTransfer>
  </el-form>
</template>

<script setup lang="ts">
import Training_Api from "@/api/training";
import { GaugeSchemePageItem, GetEvaluateGaugeInputDTO } from "@/api/training/types";
import { FormInstance, FormRules } from "element-plus";

interface PageBaseGauge extends BaseGauge {
  IsDoctor?: boolean;
  GaugeId?: string;
  GaugeName?: string;
}

const formRef = ref<FormInstance>();
const ruleForm = ref<GaugeSchemePageItem>({
  Details: [],
});
const rules = ref<FormRules>({
  Name: [
    {
      type: "string",
      whitespace: true,
      required: true,
      message: "请输入名称",
      trigger: "blur",
    },
  ],
});
const isPreview = inject<Ref<boolean>>("isPreview", ref(false));

const gaugeList = ref<BaseGauge[]>([]);
const gaugeListTotal = ref<number>(0);
const gaugeListLoading = ref<boolean>(false);
const selectedLoading = ref<boolean>(false);
const gaugeListQuery = ref<GetEvaluateGaugeInputDTO>({
  PageIndex: 1,
  PageSize: 10,
  IsEnble: true,
  Type: 0,
  Keyword: "",
});

const handleSubmit = async (): Promise<GaugeSchemePageItem | null> => {
  try {
    await formRef.value?.validate();
    const params = onProcessFormData();
    return params;
  } catch {
    return null;
  }
};

const onProcessFormData = (): GaugeSchemePageItem => {
  const copyData: GaugeSchemePageItem = JSON.parse(JSON.stringify(ruleForm.value));
  const params: GaugeSchemePageItem = {
    IsEnable: copyData.IsEnable,
    Name: copyData.Name,
    OrgId: copyData.OrgId,
    Remark: copyData.Remark,
    Details: copyData.Details,
  };
  if (copyData.Id) {
    params.Id = copyData.Id;
  }
  if (copyData.Details && copyData.Details.length) {
    const details = copyData.Details.map((s) => {
      return {
        Actions: "",
        AssistClassifys: "",
        GaugeId: s.GaugeId,
        GaugeName: s.GaugeName,
        IsDoctor: s.IsDoctor,
        MaxScore: 0,
        MinScore: 0,
      };
    });
    copyData.Details = details;
  }
  return params;
};

const handleProcessingData = async (id: string) => {
  selectedLoading.value = true;
  const res = await Training_Api.getGaugeSchemeDetailById({ id });
  if (res.Type === 200) {
    ruleForm.value = res.Data;
  }
  selectedLoading.value = false;
};

const handleGetGaugeList = async () => {
  gaugeListLoading.value = true;
  const res = await Training_Api.getEvaluateGaugePage(gaugeListQuery.value);
  if (res.Type === 200) {
    const newData = res.Data.Rows.map(
      (item): PageBaseGauge => ({
        ...item,
        GaugeId: item.Id,
        GaugeName: item.Name,
        IsDoctor: false,
      })
    );
    gaugeList.value = newData;
    gaugeListTotal.value = res.Data.Total;
    gaugeListLoading.value = false;
  }
};
const handleFilterGaugeList = (data: { keyword?: string; pageIndex: number }) => {
  gaugeListQuery.value.Keyword = data.keyword;
  gaugeListQuery.value.PageIndex = data.pageIndex;
  handleGetGaugeList();
};

interface Props {
  id: string | null;
}
const props = defineProps<Props>();

watch(
  () => props.id,
  (newVal) => {
    if (newVal) {
      handleProcessingData(newVal);
    }
  },
  { immediate: true }
);
onBeforeMount(() => {
  // 获取量表数据
  handleGetGaugeList();
});

defineExpose({
  handleSubmit,
});
</script>

<style lang="scss" scoped></style>
