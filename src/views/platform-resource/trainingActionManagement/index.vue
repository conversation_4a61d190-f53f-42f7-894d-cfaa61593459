<template>
  <div class="app-container">
    <BaseTableSearchContainer @size-changed="tableResize">
      <template #search>
        <TBSearchContainer :is-show-toggle="true">
          <template #left>
            <el-form :model="queryParams" label-position="right" :inline="true">
              <el-form-item label="类别">
                <el-select
                  v-model="queryParams.Type"
                  placeholder="请选择"
                  clearable
                  :empty-values="[null, undefined, '']"
                  :value-on-clear="() => null"
                >
                  <el-option label="日常生活活动能力训练" :value="1" />
                  <el-option label="治疗性运动训练" :value="0" />
                </el-select>
              </el-form-item>
              <el-form-item label="是否启用">
                <el-select
                  v-model="queryParams.Enable"
                  placeholder="请选择"
                  clearable
                  :empty-values="[null, undefined, '']"
                  :value-on-clear="() => null"
                  style="width: 100px"
                >
                  <el-option label="是" :value="true" />
                  <el-option label="否" :value="false" />
                </el-select>
              </el-form-item>
              <el-form-item label="关键字" prop="Keyword">
                <el-input
                  v-model="queryParams.Keyword"
                  placeholder="标题/编码/拼音码"
                  @keyup.enter="handleQuery"
                />
              </el-form-item>
              <el-form-item label="疾病种类">
                <el-select
                  v-model="queryParams.DiseaseDict"
                  placeholder="请选择"
                  filterable
                  clearable
                  :empty-values="[null, undefined, '']"
                  :value-on-clear="() => null"
                >
                  <el-option
                    v-for="item in diseaseList"
                    :key="item.Id"
                    :label="item.Key"
                    :value="item.Id!"
                  />
                </el-select>
              </el-form-item>
              <el-form-item label="功能障碍">
                <el-select
                  v-model="queryParams.DysfunctionId"
                  placeholder="请选择"
                  filterable
                  clearable
                  :empty-values="[null, undefined, '']"
                  :value-on-clear="() => null"
                >
                  <el-option
                    v-for="item in dysfunctionList"
                    :key="item.Id"
                    :label="item.Key"
                    :value="item.Id!"
                  />
                </el-select>
              </el-form-item>
              <el-form-item label="部位">
                <el-select
                  v-model="queryParams.PartId"
                  placeholder="请选择"
                  filterable
                  clearable
                  :empty-values="[null, undefined, '']"
                  :value-on-clear="() => null"
                >
                  <el-option
                    v-for="item in partList"
                    :key="item.Id"
                    :label="item.Key"
                    :value="item.Id!"
                  />
                </el-select>
              </el-form-item>
              <el-form-item label="设备类型">
                <el-select
                  v-model="queryParams.InstrumentId"
                  placeholder="请选择"
                  filterable
                  clearable
                  :empty-values="[null, undefined, '']"
                  :value-on-clear="() => null"
                >
                  <el-option
                    v-for="item in instrumentList"
                    :key="item.InstrumentId"
                    :label="item.Name"
                    :value="item.InstrumentId"
                  />
                </el-select>
              </el-form-item>
            </el-form>
          </template>
          <template #right>
            <el-button type="primary" icon="search" @click="handleQuery">搜索</el-button>
            <el-button type="primary" @click="handleBatchPush">批量推送</el-button>
            <el-button type="primary" @click="handlePreviewOrEdit(null, true)">新增</el-button>
          </template>
        </TBSearchContainer>
      </template>
      <template #table>
        <el-table
          ref="tableRef"
          v-loading="tableLoading"
          :data="pageData"
          :total="total"
          border
          row-key="ContentId"
          :height="tableFluidHeight"
          highlight-current-row
          style="text-align: center; flex: 1"
          @selection-change="(selection) => handleSelectionChange(selection, 'ContentId')"
        >
          <el-table-column type="selection" width="55" align="center" />
          <el-table-column prop="Code" label="编码" align="center" />
          <el-table-column prop="Name" label="名称" align="center" />
          <el-table-column prop="DiseaseDictName" label="疾病种类" align="center" />
          <el-table-column label="功能障碍" align="center">
            <template #default="scope">
              {{ handleGetDysfunctionName(scope.row.Dysfunction) }}
            </template>
          </el-table-column>
          <el-table-column label="类别" align="center">
            <template #default="scope">
              {{ ["治疗性运动训练", "日常生活活动能力训练"][scope.row.Type] }}
            </template>
          </el-table-column>
          <el-table-column
            prop="CreatedTime"
            label="创建时间"
            align="center"
            :formatter="tableDateFormat"
          />
          <el-table-column label="是否启用" align="center" width="80">
            <template #default="scope">
              {{ scope.row.Enable ? "是" : "否" }}
            </template>
          </el-table-column>
          <el-table-column fixed="right" label="操作" align="center" width="140">
            <template #default="scope">
              <el-button link type="primary" @click="handlePreviewOrEdit(scope.row, true)">
                查看
              </el-button>
              <el-button link type="primary" @click="handlePreviewOrEdit(scope.row, false)">
                编辑
              </el-button>
              <el-button link type="primary" @click="handleDelete(scope.row.ContentId)">
                删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </template>
      <template #pagination>
        <Pagination
          v-if="total > 0"
          v-model:total="total"
          v-model:page="queryParams.PageIndex"
          v-model:limit="queryParams.PageSize"
          @pagination="handleGetTableList"
        />
      </template>
    </BaseTableSearchContainer>
    <el-dialog
      v-model="showDialog"
      :title="dialogTitle"
      width="800"
      destroy-on-close
      :close-on-click-modal="isPreview"
      :close-on-press-escape="isPreview"
    >
      <TrainingContent ref="trainingContentRef" :content-id="contentId" />
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="showDialog = false">取消</el-button>
          <el-button
            v-if="!isPreview"
            type="primary"
            :loading="dialogConfirmLoading"
            @click="handleConfirmDialog"
          >
            确定
          </el-button>
        </div>
      </template>
    </el-dialog>
    <!-- 选择机构 -->
    <el-dialog v-model="showOrgDialog" title="选择机构" width="700px" destroy-on-close>
      <HospitalTransfer
        :loading="orgDialogLoading"
        @cancel="showOrgDialog = false"
        @submit="onConfirmOrganizations"
      />
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import Content_Api from "@/api/content";
import { PageQueryActionUnitParams, ProcessActionUnitInputDTO } from "@/api/content/types";
import { useTableConfig } from "@/hooks/useTableConfig";
import { getDictionaryList, getDictListData, getDysfunctionList } from "@/utils/dict";
import TrainingContent from "./components/TrainingContent.vue";

defineOptions({
  name: "TrainingActionManagement",
});

const queryParams = ref<PageQueryActionUnitParams>({
  PageSize: 20,
  PageIndex: 1,
  Keyword: "",
  DysfunctionId: null,
  Type: null,
  Enable: null,
  PartId: null,
  InstrumentId: null,
  DiseaseDict: null,
  OrganizationId: null,
  UseScope: 0, // 0：公用的 1：私用
});
const diseaseList = ref<ReadDict[]>([]);
const dysfunctionList = ref<ReadDict[]>([]);
const partList = ref<ReadDict[]>([]);
const instrumentList = ref<BaseInstrument[]>([]);
const showOrgDialog = ref<boolean>(false);
const orgDialogLoading = ref<boolean>(false);
provide("instrumentList", instrumentList);
provide("dysfunctionList", dysfunctionList);
provide("partList", partList);
provide("diseaseList", diseaseList);
const trainingContentRef = useTemplateRef("trainingContentRef");
const contentId = ref<string>("");

const showDialog = ref<boolean>(false);
const dialogTitle = ref<string>("");
const isPreview = ref<boolean>(false);
const dialogConfirmLoading = ref<boolean>(false);
provide("isPreview", isPreview);

const {
  tableLoading,
  pageData,
  total,
  tableRef,
  tableDateFormat,
  tableFluidHeight,
  tableResize,
  selectedTableIds,
  handleSelectionChange,
} = useTableConfig<ActionUnit>();

const handleQuery = () => {
  queryParams.value.PageIndex = 1;
  handleGetTableList();
};

const handleBatchPush = () => {
  if (!selectedTableIds.value.length) {
    ElMessage.error("请选择训练动作");
    return;
  }
  showOrgDialog.value = true;
};

const onConfirmOrganizations = (orgIds: string[]) => {
  orgDialogLoading.value = true;
  Content_Api.pushActionItem({
    Ids: selectedTableIds.value,
    OrgIds: orgIds,
  })
    .then((res) => {
      if (res.Type === 200) {
        // 清空选项
        showOrgDialog.value = false;
        selectedTableIds.value = [];
        tableRef.value?.clearSelection();
        ElNotification.success(res.Content!);
        handleGetTableList();
      }
    })
    .finally(() => {
      orgDialogLoading.value = false;
    });
};

const handleDelete = (id: string) => {
  ElMessageBox.confirm("确定删除吗？", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(() => {
    Content_Api.deleteActionUnit({ id }).then((res) => {
      if (res.Type === 200) {
        ElNotification.success(res.Content!);
        handleGetTableList();
      }
    });
  });
};

const handlePreviewOrEdit = async (row: ActionUnit | null, isPreviewState: boolean) => {
  isPreview.value = row ? isPreviewState : false;
  dialogTitle.value = row ? (isPreviewState ? "查看" : "编辑") : "新增";
  contentId.value = row ? row.ContentId : "";
  showDialog.value = true;
};
const handleGetTableList = async () => {
  tableLoading.value = true;
  const res = await Content_Api.pageQueryActionUnit(queryParams.value);
  if (res.Type === 200) {
    pageData.value = res.Data.Data;
    total.value = res.Data.TotalCount;
  }
  tableLoading.value = false;
};

const onGetDiseaseList = async () => {
  const list = await getDictListData("DiseaseDict", {
    PageSize: 9999,
    Key: "",
    IsEnabled: true,
    SortConditions: [{ SortField: "Key", ListSortDirection: 1 }],
  });
  diseaseList.value = list;
};

const onGetDysfunctionList = async () => {
  const list = await getDysfunctionList({
    PageSize: 9999,
    Key: "",
    IsEnabled: null,
    IsPublish: true,
    SortConditions: [{ SortField: "Key", ListSortDirection: 1 }],
  });
  dysfunctionList.value = list;
};

const onGetPartList = async () => {
  const list = await getDictionaryList("PartDict");
  partList.value = list;
};

const handleGetDysfunctionName = (dysfunction: string[]): string => {
  if (dysfunction.length) {
    return dysfunction
      .map((item) => {
        const dysfunctionItem = dysfunctionList.value.find((d) => d.Id === item);
        return dysfunctionItem?.Key;
      })
      .join(",");
  }
  return "";
};

const onGetInstrumentList = async () => {
  const res = await Content_Api.getOriginalInstruments();
  if (res.Type === 200) {
    instrumentList.value = res.Data;
  }
};

const handleConfirmDialog = async () => {
  const params: ProcessActionUnitInputDTO | null = await trainingContentRef.value!.handleSubmit();
  if (!params) return;
  dialogConfirmLoading.value = true;
  const func = params.Id ? Content_Api.updateActionUnit : Content_Api.createActionUnit;
  func(params)
    .then((res) => {
      if (res.Type === 200) {
        ElNotification.success(res.Content!);
        showDialog.value = false;
        handleGetTableList();
      }
    })
    .finally(() => {
      dialogConfirmLoading.value = false;
    });
};

onMounted(() => {
  // 获取疾病列表
  onGetDiseaseList();
  // 获取功能障碍列表
  onGetDysfunctionList();
  // 获取部位列表
  onGetPartList();
  // 获取设备类型列表
  onGetInstrumentList();
});

onActivated(() => {
  handleGetTableList();
});
</script>

<style lang="scss" scoped></style>
