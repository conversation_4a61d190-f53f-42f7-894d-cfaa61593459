<template>
  <div class="app-container">
    <BaseTableSearchContainer @size-changed="tableResize">
      <template #search>
        <div class="flex justify-end mx-10px mt-10px">
          <el-button type="primary" @click="handleAction('add')">添加</el-button>
        </div>
      </template>
      <template #table>
        <el-table
          :ref="kTableRef"
          v-loading="tableLoading"
          :data="pageData"
          border
          fit
          :header-cell-style="{ textAlign: 'center' }"
          :cell-style="{ textAlign: 'center' }"
          show-overflow-tooltip
        >
          <el-table-column prop="Name" label="模板名称" />
          <el-table-column prop="Sort" label="排序" width="100" />
          <el-table-column
            prop="CreatedTime"
            label="创建时间"
            width="180"
            :formatter="dateFormat"
          />
          <el-table-column label="操作" width="180">
            <template #default="{ row }">
              <el-button link type="primary" @click="handleAction('view', row)">查看</el-button>
              <el-button link type="primary" @click="handleAction('edit', row)">编辑</el-button>
              <el-button link type="primary" @click="deleteTemplate(row)">删除</el-button>
            </template>
          </el-table-column>
        </el-table>
        <!-- 分页 -->
      </template>
      <template #pagination>
        <Pagination
          v-if="total > 0"
          v-model:total="total"
          v-model:page="queryParams.PageIndex"
          v-model:limit="queryParams.PageSize"
          @pagination="loadData"
        />
      </template>
    </BaseTableSearchContainer>

    <el-dialog
      v-model="dialogModel.visible"
      :title="dialogTitle"
      :close-on-click-modal="dialogCloseable"
      :close-on-press-escape="dialogCloseable"
      destroy-on-close
    >
      <TemplateModify
        ref="modify"
        :data="dialogModel.data"
        :oper="dialogModel.action"
        @success="
          () => {
            loadData();
            dialogModel.visible = false;
          }
        "
        @close="dialogModel.visible = false"
      />
    </el-dialog>
  </div>
</template>

<script lang="ts" setup>
import Consult_Api from "@/api/consult";
import dayjs from "dayjs";
import TemplateModify from "./components/TemplateModify.vue";
import { useTableConfig } from "@/hooks/useTableConfig";
import { useActionDialog, DialogAction } from "@/hooks/useActionDialog";
import { FollowUpTemplateDTO } from "@/api/consult/types";

const { kTableRef, tableResize, pageData, tableLoading, total } =
  useTableConfig<FollowUpTemplateDTO>();
const { dialogModel, dialogTitle, dialogCloseable } = useActionDialog<FollowUpTemplateDTO>({
  add: "新增模板",
  edit: "编辑模板",
  view: "查看模板",
});

const queryParams = reactive({
  PageIndex: 1,
  PageSize: 20,
});

const dateFormat = (row: any, column: any, value: string) => {
  if (!value) return "";
  return dayjs(value).format("YYYY-MM-DD HH:mm:ss");
};

const deleteTemplate = (data: FollowUpTemplateDTO) => {
  ElMessageBox.confirm("此操作将删除该条数据, 是否继续?", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  })
    .then(async () => {
      const res = await Consult_Api.deleteFollowUpTemplate({ Id: data.StrId });
      if (res.Type != 200) {
        ElMessage.error(res.Content);
        return;
      }
      ElMessage.success(res.Content);
      loadData();
    })
    .catch(() => {});
};

const handleAction = (oper: DialogAction, row?: FollowUpTemplateDTO) => {
  dialogModel.action = oper;
  dialogModel.data = row ?? null;
  dialogModel.visible = true;
};

const loadData = async () => {
  tableLoading.value = true;
  const res = await Consult_Api.getFollowUpTemplatePage({
    PageIndex: queryParams.PageIndex,
    PageSize: queryParams.PageSize,
  });
  tableLoading.value = false;

  if (res.Type != 200) {
    ElMessage.error(res.Content);
    return;
  }

  total.value = res.Data.Total;
  pageData.value = res.Data.Data;
};

onMounted(() => {
  loadData();
});
</script>
