<template>
  <div v-loading="loading">
    <el-form
      ref="formRef"
      :model="formModel"
      :rules="rules"
      label-width="auto"
      :disabled="isDisable"
      @submit.prevent="submit"
    >
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="开始时间" prop="WaitExecDay">
            <el-input
              v-model.number="formModel.WaitExecDay"
              type="number"
              placeholder="请输入"
              :min="0"
            >
              <template #append>天后</template>
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="随访方式" prop="Type">
            <el-select v-model="formModel.Type" placeholder="请选择">
              <el-option label="来院" :value="3" />
              <el-option label="在线随访" :value="2" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-form-item prop="FollowUpPlanDetailInputDtos" label="随访内容" label-position="top">
        <el-form
          v-if="!isDisable"
          ref="contentFormRef"
          :model="contentForm"
          :rules="contentFormRules"
          class="w-full"
        >
          <div class="flex gap-2 w-full mb-18px">
            <div class="w-100px">
              <el-form-item prop="type">
                <el-select v-model="contentForm.type" @change="loadChooseList">
                  <el-option label="量表" :value="1" />
                  <el-option label="宣教" :value="2" />
                  <el-option label="问卷" :value="3" />
                </el-select>
              </el-form-item>
            </div>
            <div class="flex-1">
              <el-form-item prop="contentId">
                <el-select
                  v-model="contentForm.contentId"
                  placeholder="请选择"
                  filterable
                  :loading="chooseListLoading"
                >
                  <el-option v-for="i in chooseList" :key="i.Id" :label="i.Name" :value="i.Id" />
                </el-select>
              </el-form-item>
            </div>

            <el-button type="primary" @click="addContent">添加</el-button>
          </div>
        </el-form>

        <el-table
          :data="formModel.FollowUpPlanDetailInputDtos"
          border
          fit
          min-height="200px"
          :header-cell-style="{ 'text-align': 'center' }"
          :cell-style="{ 'text-align': 'center' }"
          show-overflow-tooltip
        >
          <el-table-column label="类型">
            <template #default="{ row }">
              {{ ["", "量表", "宣教", "问卷"][row.Type] }}
            </template>
          </el-table-column>
          <el-table-column label="名称" prop="RelatedName" />
          <el-table-column v-if="!isDisable" label="操作">
            <template #default="{ row }">
              <el-button link type="primary" @click="deleteContent(row)">删除</el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-form-item>

      <div v-if="!isDisable" class="text-right">
        <el-button @click="emit('close')">取 消</el-button>
        <el-button type="primary" native-type="submit">确 定</el-button>
      </div>
    </el-form>
  </div>
</template>

<script lang="ts" setup>
import { generateUUID } from "@/utils";
import { FormInstance, FormRules } from "element-plus";
import Training_Api from "@/api/training";
import Content_Api from "@/api/content";
import { useUserStore } from "@/store/modules/user";
import { Plan } from "./TemplateModify.vue";

type ContentType = 1 | 2 | 3;

const userStore = useUserStore();

const props = defineProps({
  data: {
    type: Object as PropType<Plan | null>,
    default: () => null,
  },
  oper: {
    type: String as PropType<"view" | "edit" | "add" | null>,
    default: null,
  },
});
const emit = defineEmits<{
  (e: "success", data: Plan): void;
  (e: "close"): void;
}>();

const isDisable = computed(() => props.oper === "view" || props.oper === null);
const loading = ref(false);
const formRef = useTemplateRef<FormInstance>("formRef");
const formModel = ref({
  /** 开始时间 */
  WaitExecDay: null as number | null,
  /** 随访方式 1:来院 2:在线随访 */
  Type: undefined as number | undefined,
  /** 随访内容 */
  FollowUpPlanDetailInputDtos: [] as {
    RelatedId: string;
    RelatedName: string;
    Type: ContentType;
  }[],
});

const rules: FormRules = {
  WaitExecDay: [{ required: true, type: "number", message: "请输入开始时间", trigger: "blur" }],
  Type: [{ required: true, message: "请选择随访方式", trigger: "change" }],
  FollowUpPlanDetailInputDtos: [
    { required: true, type: "array", message: "请添加宣教、量表、问卷", trigger: "change" },
  ],
};

onBeforeMount(() => {
  if (props.data) {
    formModel.value = { ...formModel.value, ...JSON.parse(JSON.stringify(props.data)) };
  }

  loadChooseList();
});

const submit = () => {
  formRef.value?.validate((valid: boolean, fields: any) => {
    if (!valid) {
      console.log(fields);
      const keys = Object.keys(fields);
      if (keys.length > 0) {
        ElMessage.warning(fields[keys[0]][0].message);
      } else {
        ElMessage.warning("请填写完整");
      }
      return;
    }

    const data: Plan = {
      ...props.data,
      _id: props.data?._id || generateUUID(),
      WaitExecDay: formModel.value.WaitExecDay!,
      Type: formModel.value.Type!,
      FollowUpPlanDetailInputDtos: formModel.value.FollowUpPlanDetailInputDtos,
    };

    if (props.oper === "add") {
      data.CreatorId = userStore.userInfo.Id;
      data.CreatorName = userStore.userInfo.Name;
    }

    emit("success", data);
  });
};

const chooseListLoading = ref(false);
/** 内容选择列表 */
const chooseList = ref<{ Id: string; Name: string }[]>([]);
/** 内容表单 */
const contentForm = reactive({
  /** 内容类型 1:量表 2:宣教 3:问卷 */
  type: 1 as ContentType,
  /** 预添加内容id */
  contentId: "",
});
const contentFormRef = useTemplateRef<FormInstance>("contentFormRef");
const contentFormRules: FormRules = {
  type: [{ required: true, message: "请选择类型", trigger: "change" }],
  contentId: [{ required: true, message: "请选择内容", trigger: "change" }],
};

/** 加载内容选择列表 */
const loadChooseList = async () => {
  if (isDisable.value) return;

  chooseListLoading.value = true;
  chooseList.value = [];
  contentForm.contentId = "";

  switch (contentForm.type) {
    case 1:
      await getLBOrWJList(0);
      break;
    case 2:
      await getXJList();
      break;
    case 3:
      await getLBOrWJList(1);
      break;
  }
  chooseListLoading.value = false;
};

/**
 * 获取量表、问卷列表
 * @param type 0:量表 1:问卷
 */
const getLBOrWJList = async (type: number) => {
  const res = await Training_Api.getEvaluateGaugePage({
    IsEnble: true,
    Type: type,
    PageIndex: 1,
    PageSize: 9999,
  });
  if (res.Type != 200) {
    ElMessage.error(res.Content);
    return;
  }

  if (res.Data.Total > 0) {
    chooseList.value = res.Data.Rows.map((v) => ({
      Id: v.Id!,
      Name: v.Name!,
    }));
  }
};
/**
 * 获取宣教列表
 */
const getXJList = async () => {
  const res = await Content_Api.pageQueryContent({
    Enable: true,
    PageIndex: 1,
    PageSize: 9999,
  });
  if (res.Type != 200) {
    ElMessage.error(res.Content);
    return;
  }

  chooseList.value = res.Data.Data.map((v) => ({
    Id: v.ContentId!,
    Name: v.Title!,
  }));
};

const addContent = () => {
  contentFormRef.value?.validate((valid: boolean, fields: any) => {
    if (!valid) {
      return;
    }

    const flag = formModel.value.FollowUpPlanDetailInputDtos.some(
      (v) => v.RelatedId === contentForm.contentId
    );
    if (flag) {
      ElMessage.warning("已存在该条数据");
      return;
    }

    const obj = chooseList.value.filter((v) => v.Id === contentForm.contentId)[0];
    formModel.value.FollowUpPlanDetailInputDtos.push({
      RelatedId: obj.Id,
      RelatedName: obj.Name,
      Type: contentForm.type,
    });
    formRef.value?.validateField("FollowUpPlanDetailInputDtos");
  });
};

const deleteContent = (data: any) => {
  formModel.value.FollowUpPlanDetailInputDtos = formModel.value.FollowUpPlanDetailInputDtos.filter(
    (item) => item.RelatedId !== data.RelatedId
  );
  formRef.value?.validateField("FollowUpPlanDetailInputDtos");
};
</script>
