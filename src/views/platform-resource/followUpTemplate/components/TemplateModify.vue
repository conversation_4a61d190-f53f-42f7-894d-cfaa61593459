<template>
  <div v-loading="loading">
    <el-form
      ref="formRef"
      :model="formModel"
      label-position="right"
      label-width="auto"
      :disabled="isDisable || submitting"
      :rules="rules"
      @submit.prevent="submit"
    >
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="名称:" prop="Name">
            <el-input v-model="formModel.Name" placeholder="请输入模板名称" clearable />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="排序:" prop="Sort">
            <el-input-number v-model="formModel.Sort" placeholder="排序" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-form-item label="备注:" prop="Remark">
        <el-input v-model="formModel.Remark" type="textarea" placeholder="请输入备注" clearable />
      </el-form-item>

      <el-form-item prop="PlanDtos" label-position="top">
        <template #label>
          计划:
          <el-button v-if="!isDisable" type="primary" @click="handlePlanAction('add')">
            添加计划
          </el-button>
        </template>

        <el-table
          :data="formModel.PlanDtos"
          border
          fit
          :header-cell-style="{ textAlign: 'center' }"
          :cell-style="{ textAlign: 'center' }"
          show-overflow-tooltip
        >
          <el-table-column type="index" label="随访计划" width="80" />
          <el-table-column
            label="随访方式"
            prop="Type"
            width="80"
            :formatter="(row, column, value) => (value == 3 ? '来院' : '在线随访')"
          />
          <el-table-column
            label="开始时间"
            prop="WaitExecDay"
            width="80"
            :formatter="(row, column, value) => value + '天后'"
          />
          <el-table-column
            prop="FollowUpPlanDetailInputDtos"
            label="随访内容"
            :formatter="formatPlanContent"
          />
          <el-table-column v-if="!isDisable" label="操作" width="150">
            <template #default="{ row }">
              <el-button link type="primary" @click="handlePlanAction('view', row)">查看</el-button>
              <el-button link type="primary" @click="handlePlanAction('edit', row)">编辑</el-button>
              <el-button link type="primary" @click="deletePlan(row)">删除</el-button>
            </template>
          </el-table-column>
          <el-table-column v-if="isDisable" label="操作" width="150">
            <template #default="{ row }">
              <el-text
                style="cursor: pointer"
                type="primary"
                @click="handlePlanAction('view', row)"
              >
                查看
              </el-text>
            </template>
          </el-table-column>
        </el-table>
      </el-form-item>

      <div v-if="!isDisable" class="text-right">
        <el-button @click="emit('close')">取消</el-button>
        <el-button :loading="submitting" type="primary" native-type="submit">确定</el-button>
      </div>
    </el-form>

    <el-dialog
      v-model="dialogModel.visible"
      :title="dialogTitle"
      :close-on-click-modal="dialogCloseable"
      :close-on-press-escape="dialogCloseable"
      destroy-on-close
    >
      <PlanModify
        ref="modifyRef"
        :data="dialogModel.data"
        :oper="dialogModel.action"
        @success="
          (value) => {
            updatePlan(value);
            dialogModel.visible = false;
          }
        "
        @close="dialogModel.visible = false"
      />
    </el-dialog>
  </div>
</template>

<script lang="ts" setup>
import Consult_Api from "@/api/consult";
import { useActionDialog } from "@/hooks/useActionDialog";
import { FormRules, FormInstance } from "element-plus";

import PlanModify from "./PlanModify.vue";
import {
  FollowUpDetail,
  FollowUpTemplateDTO,
  FollowUpPlan,
  FollowUpTemplateInputDTO,
} from "@/api/consult/types";

type Oper = "add" | "edit" | "view";
export type Plan = FollowUpPlan & {
  _id: string;
  FollowUpPlanDetailInputDtos: FollowUpDetail[];
  WaitExecDay: number;
};

const props = defineProps({
  data: {
    type: Object as PropType<FollowUpTemplateDTO | null>,
    default: () => null,
  },
  oper: {
    type: String as PropType<Oper | null>,
    default: "view",
  },
});

const isDisable = computed(() => props.oper === "view");

const emit = defineEmits(["success", "close"]);

const formRef = useTemplateRef<FormInstance>("formRef");
const formModel = ref({
  Name: "",
  Remark: undefined as string | undefined | null,
  Sort: undefined as number | undefined,
  PlanDtos: [] as Plan[],
});

const rules: FormRules = {
  Name: [{ required: true, message: "请输入模板名称", trigger: "blur" }],
  Remark: [{ required: false, message: "请输入备注", trigger: "blur" }],
  Sort: [{ required: false, type: "number", message: "请输入排序", trigger: "blur" }],
  PlanDtos: [{ required: true, type: "array", message: "请添加计划", trigger: "change" }],
};

const loading = ref(false);

let originalData: FollowUpTemplateDTO | null = null;
const loadData = async () => {
  if (!props.data) return;
  loading.value = true;
  const res = await Consult_Api.getFollowUpTemplate(props.data.StrId);
  loading.value = false;
  if (res.Type !== 200) {
    ElMessage.error(res.Content);
    return;
  }

  originalData = res.Data;

  formModel.value = {
    Name: res.Data.Name,
    Remark: res.Data.Remark,
    Sort: res.Data.Sort,
    PlanDtos:
      res.Data.PlanDetail?.map((v) => {
        const obj: Plan = {
          ...v,
          _id: v.ShowId!,
          WaitExecDay: v.WaitDay!,
          FollowUpPlanDetailInputDtos: v.TempDetail!,
        };
        delete obj.TempDetail;
        delete obj.WaitDay;
        return obj;
      }) || [],
  };
};

onBeforeMount(() => {
  loadData();
});

const submitting = ref(false);

const submit = () => {
  formRef.value?.validate(async (valid: boolean, fields: any) => {
    if (!valid) {
      console.warn(fields);

      const keys = Object.keys(fields);
      if (keys.length > 0) {
        const firstError = keys[0];
        const errorMessage = fields[firstError][0].message;
        ElMessage.warning(errorMessage);
      } else {
        ElMessage.warning("请检查必填项");
      }
      return;
    }

    if (formModel.value.PlanDtos.length === 0) {
      ElMessage.warning("请添加计划");
      return;
    }

    const data: FollowUpTemplateInputDTO = {
      ...originalData,
      ...formModel.value,
      IsTemp: true,
    };
    delete data.PlanDetail;
    submitting.value = true;
    const res = await Consult_Api.addFollowUpTemplate(data);
    submitting.value = false;

    if (res.Type !== 200) {
      ElMessage.error(res.Content);
      return;
    }

    ElNotification.success(res.Content);
    emit("success");
  });
};

const { dialogModel, dialogTitle, dialogCloseable } = useActionDialog<Plan>();
const handlePlanAction = (oper: Oper, data?: Plan | null) => {
  dialogModel.data = data ?? null;
  dialogModel.action = oper;
  dialogModel.visible = true;
};

const formatPlanContent = (_: any, __: any, value?: FollowUpDetail[]) => {
  return value?.map((v) => v.RelatedName).join("、") || "";
};

const updatePlan = (data: Plan) => {
  const index = formModel.value.PlanDtos.findIndex((v) => v._id === data._id);
  if (index > -1) {
    formModel.value.PlanDtos[index] = data;
  } else {
    formModel.value.PlanDtos.push(data);
  }
  formRef.value?.validateField("PlanDtos");
};
const deletePlan = (row: Plan) => {
  formModel.value.PlanDtos = formModel.value.PlanDtos.filter((item) => item._id !== row._id);
  formRef.value?.validateField("PlanDtos");
};
</script>
