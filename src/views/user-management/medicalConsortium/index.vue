<template>
  <el-container class="w-full h-full">
    <el-aside width="230px" class="p-10px">
      <EditTree
        ref="treeRef"
        class="w-full h-full p-10px"
        :data="treeData"
        :props="defaultTreeProps"
        node-key="Id"
        :operations="getTreeNodeOperations"
        :current-node-key="queryParams.ConsortiumId"
        @node-click="onTreeClick"
        @node-contextmenu="onNodeRightClick"
        @add="(e) => onAddOrEditConsortium(true, e)"
        @edit="(e) => onAddOrEditConsortium(false, e)"
        @delete="(e) => onDeleteConsortium(e)"
      />
    </el-aside>
    <el-main class="p-10px!">
      <BaseTableSearchContainer @size-changed="tableResize">
        <!-- 顶部筛选条件 -->
        <template #search>
          <TBSearchContainer>
            <template #left>
              <el-form :model="queryParams" label-position="right" :inline="true" @submit.prevent>
                <el-form-item label="关键字">
                  <el-input
                    v-model="queryParams.Keyword"
                    placeholder="医院名称"
                    prefix-icon="Search"
                    clearable
                    @keyup.enter="handleQuery"
                  />
                </el-form-item>
              </el-form>
            </template>
            <template #right>
              <el-button type="primary" icon="search" @click="handleQuery">搜索</el-button>
              <el-button type="primary" @click="onAddOrganizationConsortium">添加</el-button>
            </template>
          </TBSearchContainer>
        </template>
        <!-- 列表 -->
        <template #table>
          <el-table
            v-loading="tableLoading"
            :data="pageData"
            row-key="Id"
            :header-cell-style="{ textAlign: 'center' }"
            :cell-style="{ textAlign: 'center' }"
            border
            highlight-current-row
          >
            <el-table-column prop="OrganizationName" label="医院名称" show-overflow-tooltip />
            <el-table-column prop="ConsortiumName" label="医联体" />
            <!-- 操作 -->
            <el-table-column fixed="right" label="操作" width="150">
              <template #default="scope">
                <el-button link type="primary" @click="onRemoveItem(scope.row)">移除</el-button>
              </template>
            </el-table-column>
          </el-table>
        </template>
        <!-- 分页 -->
        <template #pagination>
          <Pagination
            v-if="total > 0"
            v-model:total="total"
            v-model:page="queryParams.PageIndex"
            v-model:limit="queryParams.PageSize"
            @pagination="requestTableList"
          />
        </template>
      </BaseTableSearchContainer>
    </el-main>
  </el-container>

  <!-- 添加/编辑医联体 -->
  <el-dialog
    v-model="showConsortiumDialog.isShow"
    :title="showConsortiumDialog.title"
    width="600"
    destroy-on-close
    @close="showConsortiumDialog.isShow = false"
  >
    <ConsortiumForm
      :data="showConsortiumDialog.data"
      @cancel="showConsortiumDialog.isShow = false"
      @submit="onConfirmAddOrEditConsortium"
    />
  </el-dialog>

  <!-- 添加医联体的机构 -->
  <el-dialog
    v-model="showOrganizationConsortiumDialog.isShow"
    title="添加机构"
    width="500"
    destroy-on-close
    @close="showOrganizationConsortiumDialog.isShow = false"
  >
    <OrganizationConsortiumForm
      :consortium-id="queryParams.ConsortiumId!"
      @cancel="showOrganizationConsortiumDialog.isShow = false"
      @submit="onConfirmAddOrganizationConsortium"
    />
  </el-dialog>
</template>

<script setup lang="ts">
import { useTableConfig } from "@/hooks/useTableConfig";
import Passport_Api from "@/api/passport";
import {
  Consortium,
  OrganizationConsortium,
  OrganizationConsortiumListInputDTO,
} from "@/api/passport/types";
import { TreeNodeOperations } from "@/components/EditTree/index.vue";
import ConsortiumForm from "./components/ConsortiumForm.vue";
import OrganizationConsortiumForm from "./components/OrganizationConsortiumForm.vue";

interface ConsortiumNode extends Consortium {
  children?: ConsortiumNode[];
}

// 调试开关
const kEnableDebug = false;
defineOptions({
  name: "MedicalConsortium",
});

const { pageData, tableLoading, total, tableResize } = useTableConfig<OrganizationConsortium>();

// 查询条件
const queryParams = reactive<OrganizationConsortiumListInputDTO>({
  PageIndex: 1,
  PageSize: 20,
});

/** 树数据 */
const treeData = ref<ConsortiumNode[]>([]);
const defaultTreeProps = {
  children: "children",
  label: "Name",
  Id: "Id",
};

/** 树组件实例 */
const treeRef = useTemplateRef("treeRef");

/** 树节点操作选项 */
const treeNodeOperations = ref<TreeNodeOperations>({
  add: false,
  edit: false,
  delete: false,
});

// 编辑/添加医联体弹窗
const showConsortiumDialog = reactive({
  isShow: false,
  title: "",
  data: {} as ConsortiumNode,
});

// 添加医联体机构弹窗
const showOrganizationConsortiumDialog = reactive({
  isShow: false,
});

// 树点击事件
async function onTreeClick(data: ConsortiumNode) {
  kEnableDebug && console.debug("树点击事件", data);

  queryParams.ConsortiumId = data.Id;
  queryParams.PageIndex = 1;
  const r = await requestTableList();
  if (r.Type !== 200) {
    ElMessage.error(r.Content);
  }
}

// 树右键点击事件
function onNodeRightClick(data: ConsortiumNode) {
  kEnableDebug && console.debug("树右键点击事件", data);

  showConsortiumDialog.data = data.Id ? data : {};
}

/** 获取树节点右键操作 */
function getTreeNodeOperations(data: RecoveryMissionType) {
  if (data.Id) {
    // 编辑、删除
    return {
      edit: true,
      delete: true,
    };
  }

  // 添加
  return {
    add: true,
  };
}

// 点击删除医联体
async function onDeleteConsortium(data: Consortium) {
  kEnableDebug && console.debug("点击删除医联体");

  if (!data.Id) {
    ElMessage.error("医联体Id为空");
    return;
  }

  ElMessageBox.confirm("此操作将删除该条数据, 是否继续?", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(async () => {
    const r0 = await Passport_Api.deleteConsortium([data.Id!]);
    if (r0.Type !== 200) {
      ElMessage.error(r0.Content);
      return;
    }

    ElNotification.success("删除成功");

    // 刷新树
    treeRef.value?.remove(data);

    // 删除当前医联体，刷新列表
    if (queryParams.ConsortiumId === data.Id) {
      queryParams.ConsortiumId = undefined;
      queryParams.PageIndex = 1;
      const r = await requestTableList();
      if (r.Type !== 200) {
        ElMessage.error(r.Content);
      }
    }
  });
}

// 点击添加/编辑医联体
function onAddOrEditConsortium(add: boolean, data: ConsortiumNode) {
  kEnableDebug && console.debug("点击添加/编辑医联体", add);

  if (add) {
    showConsortiumDialog.title = "添加医联体";
  } else {
    showConsortiumDialog.title = "编辑医联体";
  }
  showConsortiumDialog.isShow = true;
}

// 确定添加/编辑医联体提交
async function onConfirmAddOrEditConsortium(data: Consortium[]) {
  kEnableDebug && console.debug("确定添加/编辑医联体", data);

  // 提交成功
  showConsortiumDialog.isShow = false;
  ElNotification.success("操作成功");

  // 刷新树
  const r = await requestTreeData();
  if (r.Type !== 200) {
    ElMessage.error(r.Content);
    return;
  }
}

// 点击搜索
async function handleQuery() {
  queryParams.PageIndex = 1;
  const r = await requestTableList();
  if (r.Type !== 200) {
    ElMessage.error(r.Content);
  }
}

// 点击添加医联体机构
function onAddOrganizationConsortium() {
  kEnableDebug && console.debug("点击添加");
  if (!queryParams.ConsortiumId) {
    ElMessage.error("请先选择医联体");
    return;
  }

  showOrganizationConsortiumDialog.isShow = true;
}

// 确定添加医联体机构提交
async function onConfirmAddOrganizationConsortium(data: BaseOrganization[]) {
  kEnableDebug && console.debug("确定添加医联体机构", data);

  // 提交成功
  showOrganizationConsortiumDialog.isShow = false;
  ElNotification.success("添加成功");

  // 刷新列表
  const r = await requestTableList();
  if (r.Type !== 200) {
    ElMessage.error(r.Content);
  }
}

// 移除
async function onRemoveItem(row: OrganizationConsortium) {
  kEnableDebug && console.debug("移除", row);

  if (!row.Id) {
    ElMessage.error("id为空");
    return;
  }

  ElMessageBox.confirm("此操作将删除该条数据, 是否继续?", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(async () => {
    const r0 = await Passport_Api.deleteOrganizationConsortium([row.Id!]);
    if (r0.Type !== 200) {
      ElMessage.error(r0.Content);
      return;
    }

    ElNotification.success("删除成功");
    // 刷新列表
    const r = await requestTableList();
    if (r.Type !== 200) {
      ElMessage.error(r.Content);
    }
  });
}

// 请求树数据
async function requestTreeData() {
  const r = await Passport_Api.getConsortiumList();
  if (r.Type === 200) {
    var list: ConsortiumNode[] = [
      {
        Name: "全部",
        children: r.Data,
      },
    ];
    treeData.value = list;
  }

  return r;
}

// 请求列表数据
async function requestTableList() {
  tableLoading.value = true;
  const r = await Passport_Api.getOrganizationConsortiumList(queryParams);
  tableLoading.value = false;
  if (r.Type === 200) {
    pageData.value = r.Data.Data;
    total.value = r.Data.TotalCount;
  }

  return r;
}

onActivated(async () => {
  tableLoading.value = true;
  const r0 = await requestTreeData();
  if (r0.Type !== 200) {
    tableLoading.value = false;
    ElMessage.error(r0.Content);
    return;
  }

  const r1 = await requestTableList();
  if (r1.Type !== 200) {
    ElMessage.error(r1.Content);
    return;
  }
});
</script>

<style lang="scss" scoped></style>
