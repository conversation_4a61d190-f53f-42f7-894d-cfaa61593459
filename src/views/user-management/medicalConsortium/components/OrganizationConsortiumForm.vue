<template>
  <div v-loading="formLoading" class="p-20px overflow-y-auto h-100px">
    <el-form :ref="kFormRef" :model="formData" :rules="rules" label-width="70px" :inline="true">
      <el-form-item label="医院名称" prop="OrganizationId">
        <KSelect
          v-model="formData.Id"
          :loading="hospitalsLoading"
          :data="hospitalList"
          filterable
          :props="{
            label: 'Name',
            value: 'Id',
          }"
        />
      </el-form-item>
    </el-form>
  </div>
  <!-- 底部按钮 -->
  <div class="flex justify-end">
    <el-button @click="emit('cancel')">取消</el-button>
    <el-button type="primary" @click="onSubmitForm()">确定</el-button>
  </div>
</template>

<script setup lang="ts">
import Passport_Api from "@/api/passport";
import { OrganizationConsortium, OrganizationListInputDTO } from "@/api/passport/types";
import { FormRules, FormInstance } from "element-plus";

const kEnableDebug = false;
const kFormRef = "ruleFormRef";

const props = defineProps<{
  consortiumId: string;
}>();

const emit = defineEmits<{
  cancel: [];
  submit: [BaseOrganization[]];
}>();

onMounted(() => {
  requestHospitalList();
});

// 医院列表
const hospitalList = ref<BaseOrganization[]>([]);
const hospitalsLoading = ref(false);

const formLoading = ref(false);
// 表单实例
const formRef = useTemplateRef<FormInstance>(kFormRef);
// 表单数据
const formData = reactive<BaseOrganization>({});

// 表单验证规则
const rules = reactive<FormRules<OrganizationConsortium>>({
  Id: [{ required: true, message: "请选择医院", trigger: "change" }],
});

// 提交表单
function onSubmitForm() {
  if (!formRef.value) return;

  formRef.value.validate((valid, fields) => {
    if (valid) {
      requestAddOrUpdateData();
    } else {
      kEnableDebug && console.debug("提交失败", fields, formData);
    }
  });
}

// 请求医院列表
async function requestHospitalList() {
  hospitalsLoading.value = true;
  const params: OrganizationListInputDTO = {
    IsEnabled: true,
    Keyword: "",
    DtoTypeName: "QueryOrgListOutputDto4",
    Pageable: false,
    SingleOne: false,
  };
  const r = await Passport_Api.getAllOrganizationList(params);
  if (r.Type === 200) {
    hospitalList.value = r.Data;
  }
  hospitalsLoading.value = false;
}

/** 添加/更新数据 */
async function requestAddOrUpdateData() {
  kEnableDebug && console.debug("requestAddOrUpdateData", formData);

  formLoading.value = true;
  const params: OrganizationConsortium = {
    OrganizationId: formData?.Id,
    ConsortiumId: props.consortiumId,
  };
  const r = await Passport_Api.addOrganizationConsortiums([params]);
  formLoading.value = false;
  if (r.Type === 200) {
    emit("submit", r.Data);
  } else {
    ElMessage.error(r.Content);
  }
}
</script>

<style lang="scss" scoped></style>
