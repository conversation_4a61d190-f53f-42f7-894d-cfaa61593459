<template>
  <div class="h-500px overflow-y-auto p-20px p-t-0px">
    <el-card class="doctor-card">
      <div class="doctor-info">
        <el-avatar :size="64" class="doctor-avatar">
          <el-image :src="formData.HeadImg" alt="医生头像" />
        </el-avatar>
        <div class="info-content">
          <div class="hospital-info">
            <span class="hospital-name">{{ formData.OrgName }}</span>
            <span class="department">{{ formData.DeptName }}</span>
          </div>
          <div class="doctor-detail">
            <span class="title">{{ formData.WorkerTitle }}</span>
            <span class="name">{{ formData.Name }}</span>
            <span class="phone">{{ formData.PhoneNumber }}</span>
          </div>
        </div>
        <div class="recommend-badge">
          <el-switch
            v-model="formData.UserConsultSet!.Recommend"
            class="ml-2"
            inline-prompt
            active-text="推荐"
            inactive-text="暂不推荐"
            :disabled="isPreview"
          />
        </div>
      </div>
    </el-card>
    <el-tabs v-model="activeName" type="card" class="advice-tabs" @tab-click="handleTabsClick">
      <el-tab-pane label="基本信息" name="Base">
        <BaseInfo ref="baseRef" :base-info="baseInfo" />
      </el-tab-pane>
      <el-tab-pane label="商务开发信息" name="Business">
        <Business ref="businessRef" :business-info="businessInfo" />
      </el-tab-pane>
      <el-tab-pane label="当前运行情况" name="Current">
        <Current ref="currentRef" :current-info="currentInfo" />
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script setup lang="ts">
import { dayjs, TabsPaneContext } from "element-plus";
import BaseInfo, { PageBaseData } from "./BaseInfo.vue";
import Business, { PageBusinessData } from "./Business.vue";
import Current, { PageCurrentData } from "./Current.vue";
import { DoctorInfo } from "@/api/consult/types";
const formData = ref<DoctorInfo>({
  DoctorId: "",
  Name: "",
  HeadImg: "",
  OrgId: "",
  OrgName: "",
  DeptName: "",
  PhoneNumber: "",
  WorkerTitle: "",
  DoctorFirstAuthTime: "",
  UserConsultSet: {
    VideoCost: 0,
    RichtextCost: 0,
    IsEnable: false,
    ShowCost: 0,
    UploadToSupervision: false,
    IsTechExchange: false,
    Recommend: false,
    SelfReliance: false,
  },
});
const activeName = ref<string>("Base");
const doctorId = ref<string>("");
provide("doctorId", doctorId);
const isPreview = inject<Ref<boolean>>("isPreview", ref(false));
const baseRef = ref<InstanceType<typeof BaseInfo>>();
const baseInfo = ref<PageBaseData | null>(null);
const businessRef = ref<InstanceType<typeof Business>>();
const businessInfo = ref<PageBusinessData | null>(null);
const currentRef = ref<InstanceType<typeof Current>>();
const currentInfo = ref<PageCurrentData | null>(null);
const handleTabsClick = (tab: TabsPaneContext) => {
  activeName.value = tab.paneName as string;
};
const handleGetDoctorDetail = async (doctorInfo: DoctorInfo | null) => {
  if (!doctorInfo) return null;
  doctorId.value = doctorInfo.DoctorId;
  handleProcessDoctorDetail(doctorInfo);
};
const handleProcessDoctorDetail = (data: DoctorInfo) => {
  if (!data.UserConsultSet) {
    data.UserConsultSet = {
      VideoCost: 0,
      RichtextCost: 0,
      IsEnable: false,
      ShowCost: 0,
      UploadToSupervision: false,
      IsTechExchange: false,
      Recommend: false,
      SelfReliance: false,
    };
  }
  formData.value = data;
  baseInfo.value = handleGetBaseInfo();
  businessInfo.value = handleGetBusinessInfo();
  currentInfo.value = handleGetCurrentInfo();
  formData.value.UserClaims = formData.value.UserClaims?.filter(
    (s) => s.ClaimType !== "Assistant" && s.ClaimType !== "GuideDoctor"
  );
};

const getUserWorkInfo = () => ({
  Personality: formData.value.UserWork?.Personality || "",
  InfoAddress: formData.value.UserWork?.InfoAddress || "",
  IsCertificatesComplete: formData.value.UserWork?.IsCertificatesComplete || false,
});
const getUserClaimsInfo = (claimType: string): any[] => {
  const defaultUserClaims = {
    UserId: doctorId.value,
    ClaimType: "GuideDoctor",
    ClaimValue: "",
    Remark: "",
  };
  return formData.value.UserClaims
    ? formData.value.UserClaims.filter((s) => s.ClaimType === claimType).length > 0
      ? formData.value.UserClaims.filter((s) => s.ClaimType === claimType)
      : [defaultUserClaims]
    : [defaultUserClaims];
};
const handleGetBaseInfo = (): PageBaseData => {
  return {
    UserWork: getUserWorkInfo(),
    UserBill: formData.value.UserBill || {
      Id: "",
      CardNumber: "",
      BankName: "",
      BankCode: "",
      SubBranch: "",
      SubBranchCode: "",
      Province: "",
      ProvinceCode: "",
      City: "",
      CityCode: "",
    },
    UserClaims: getUserClaimsInfo("GuideDoctor"),
    UserConsultSet: {
      SelfReliance: formData.value.UserConsultSet?.SelfReliance,
      IsEnable: formData.value.UserConsultSet?.IsEnable,
      RichtextCost: formData.value.UserConsultSet?.RichtextCost,
      ShowCost: formData.value.UserConsultSet?.ShowCost,
    },
  };
};
const getSettlementInfo = () => {
  const settlement = formData.value.Settlement || {
    MarketingId: null,
    SettlementMode: null,
    PrescriptionSettleRatio1: 0,
    PrescriptionSettleRatio2: 0,
    GuideSettleRatio: 0,
  };
  return {
    MarketingId: settlement.MarketingId || null,
    SettlementMode: settlement.SettlementMode >= 0 ? settlement.SettlementMode : null,
    PrescriptionSettleRatio1: settlement.PrescriptionSettleRatio1 || 0,
    PrescriptionSettleRatio2: settlement.PrescriptionSettleRatio2 || 0,
    GuideSettleRatio: settlement.GuideSettleRatio || 0,
  };
};
const handleGetBusinessInfo = (): PageBusinessData => {
  const defaultProjectIntention = {
    Tag: "",
    Remark: "",
  };
  return {
    UserWork: {
      SourceChannel:
        formData.value.UserWork?.SourceChannel || JSON.stringify(defaultProjectIntention),
      ProjectIntention: formData.value.UserWork?.ProjectIntention,
      Region: formData.value.UserWork?.Region || "",
    },
    Settlement: getSettlementInfo(),
    DoctorFirstAuthTime: formData.value.DoctorFirstAuthTime
      ? dayjs(formData.value.DoctorFirstAuthTime).format("YYYY-MM-DD")
      : "",
  };
};
const handleGetCurrentInfo = (): PageCurrentData => {
  return {
    AssistantFollowRecords: formData.value.AssistantFollowRecords
      ? formData.value.AssistantFollowRecords.sort((a, b) => a.Category - b.Category)
      : [],
    DiagnoseNames: formData.value.DiagnoseNames || [],
    MoItemNames: formData.value.MoItemNames || [],
    UserWork: {
      Questions: formData.value.UserWork?.Questions || "",
    },
  };
};
const processSubmitData = (baseInfo: any, businessInfo: any, currentInfo: any) => {
  const params = {
    ...formData.value,
    UserWork: {
      ...formData.value.UserWork,
      ...baseInfo.UserWork,
      ...businessInfo.UserWork,
      ...currentInfo.UserWork,
    },
    UserBill: {
      ...formData.value.UserBill,
      ...baseInfo.UserBill,
    },
    UserClaims: [...(formData.value.UserClaims || []), ...baseInfo.UserClaims],
    Settlement: {
      ...formData.value.Settlement,
      ...businessInfo.Settlement,
    },
    UserConsultSet: {
      ...formData.value.UserConsultSet,
      ...baseInfo.UserConsultSet,
    },
    AssistantFollowRecords: currentInfo.AssistantFollowRecords || [],
  };

  if (params.DiagnoseNames) delete params.DiagnoseNames;
  if (params.MoItemNames) delete params.MoItemNames;
  if (params.UserClaims) {
    // 2025-07-15 和朗哥沟通 在这里 1、不过滤ClaimValue为空的 2、需要将ClaimValue === GuideDoctor的数据添加一个OnlyOne = true
    // params.UserClaims = params.UserClaims.filter((s) => s.ClaimValue);
  }
  if (!params.UserBill.Id) {
    delete params.UserBill.Id;
  }
  return params;
};
const handleSubmitInfo = async (): Promise<DoctorInfo | null> => {
  const baseInfo = await baseRef.value?.handleSubmitForm();
  const businessInfo = await businessRef.value?.handleFormSubmit();
  const currentInfo = await currentRef.value?.handleFormSubmit();

  if (!baseInfo || !businessInfo || !currentInfo) return null;
  return Promise.resolve(processSubmitData(baseInfo, businessInfo, currentInfo));
};

interface Props {
  doctorInfo: DoctorInfo | null;
}
const props = defineProps<Props>();
watch(
  () => props.doctorInfo,
  (newVal) => {
    handleGetDoctorDetail(newVal);
  },
  { immediate: true }
);
defineExpose({
  handleSubmitInfo,
});
</script>

<style lang="scss" scoped>
.doctor-card {
  border-radius: 8px;
  padding: 10px;
  margin-bottom: 10px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);

  .doctor-info {
    display: flex;
    align-items: center;
    gap: 20px;

    .doctor-avatar {
      flex-shrink: 0;
    }

    .info-content {
      flex: 1;

      .hospital-info {
        margin-bottom: 8px;

        .hospital-name {
          font-size: 16px;
          font-weight: bold;
          margin-right: 12px;
        }

        .department {
          color: #666;
        }
      }

      .doctor-detail {
        display: flex;
        gap: 12px;
        color: #666;

        .title {
          color: #409eff;
        }
      }
    }

    .recommend-badge {
      flex-shrink: 0;
    }
  }
}

.advice-tabs {
  // :deep(.el-tabs__header) {
  //   position: sticky;
  //   top: 94px;
  //   background-color: #fff;
  //   z-index: 999;
  // }

  .tab-content {
    padding: 20px 0;
  }
}
</style>
