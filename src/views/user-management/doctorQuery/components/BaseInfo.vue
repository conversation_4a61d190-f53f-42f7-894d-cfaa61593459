<template>
  <el-form
    ref="formRef"
    :model="formData"
    label-position="right"
    scroll-to-first-error
    :disabled="isPreview"
  >
    <el-form-item label="性格描述" prop="personality">
      <el-input
        v-model="formData.UserWork.Personality"
        type="textarea"
        :rows="3"
        placeholder="描述医生的个人性格、爱好，做事风格等"
      />
    </el-form-item>

    <el-form-item label="客情地址" prop="address">
      <el-input
        v-model="formData.UserWork.InfoAddress"
        type="textarea"
        :rows="3"
        placeholder="请填写"
      />
    </el-form-item>

    <FormItemContainer>
      <el-form-item label="银行卡号" :prop="['UserBill', 'CardNumber']">
        <el-input
          v-model="formData.UserBill.CardNumber"
          placeholder="请输入银行卡号"
          style="width: 180px"
          @blur="onBlurBankAccount"
        />
      </el-form-item>
      <el-form-item label="开户行" :prop="['UserBill', 'BankName']">
        <el-input
          v-model="formData.UserBill.BankName"
          placeholder="请输入开户银行名称"
          style="width: 180px"
          clearable
        />
      </el-form-item>
      <el-form-item label="开户地">
        <el-cascader
          ref="selectorRef"
          v-model="accountOpeningList"
          :props="{
            value: 'Code',
            label: 'Name',
            lazy: true,
            lazyLoad: cascaderLazyLoad,
          }"
          :options="placeAccountOptions"
          @change="onChangePlaceAccount"
        />
      </el-form-item>
      <el-form-item label="开户支行" :prop="['UserBill', 'SubBranchCode']">
        <el-popover placement="bottom" :width="380" :visible="subBranchFocus">
          <template #reference>
            <el-input
              v-model="formData.UserBill.SubBranch"
              placeholder="请输入开户支行"
              style="width: 380px"
              clearable
              :focus="subBranchFocus"
              @focus="subBranchFocus = true"
              @blur="handleSubBranchInputBlur"
              @clear="handleSubBranchClear"
              @mouseenter="handleSubBranchMouseEnter"
              @mouseleave="handleSubBranchMouseLeave"
            >
              <!-- 动态控制 suffix 插槽 -->
              <template v-if="!subBranchClearVisible" #suffix>
                <el-icon><ArrowDown /></el-icon>
              </template>
            </el-input>
          </template>
          <div class="max-h-300px overflow-scroll">
            <div
              v-for="item in openingBankList"
              :key="item.BranchId"
              class="sub-branch-item"
              :class="{
                'sub-branch-selected':
                  formData.UserBill.SubBranchCode === item.BranchId &&
                  formData.UserBill.SubBranch === item.BranchName,
              }"
              @click="onChangeOpeningBank(item.BranchId)"
            >
              {{ item.BranchName }}
            </div>
          </div>
        </el-popover>
      </el-form-item>
    </FormItemContainer>

    <FormItemContainer class="mb-12px">
      <el-form-item
        label="证照是否齐全"
        prop="hasAllCertificates"
        style="margin-bottom: 0 !important"
      >
        <el-radio-group v-model="formData.UserWork.IsCertificatesComplete">
          <el-radio :value="true">是</el-radio>
          <el-radio :value="false">否</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-text link class="mx-1 cursor-pointer" type="primary" @click="handleUpdateCertificate">
        详情
      </el-text>
    </FormItemContainer>

    <el-form-item v-if="!isDoctor" label="指导医生">
      <UserSelect
        ref="userSelectRef"
        v-model="formData.UserClaims[0].ClaimValue"
        :role-types="['doctor']"
        @change="onChangeDoctor"
      />
    </el-form-item>

    <el-form-item label="是否自运行" prop="isSelfOperating">
      <el-radio-group v-model="formData.UserConsultSet.SelfReliance">
        <el-radio :value="true">是</el-radio>
        <el-radio :value="false">否</el-radio>
      </el-radio-group>
    </el-form-item>

    <el-form-item label="是否开启咨询" prop="isConsultationEnabled">
      <el-radio-group v-model="formData.UserConsultSet.IsEnable">
        <el-radio :value="true">是</el-radio>
        <el-radio :value="false">否</el-radio>
      </el-radio-group>
    </el-form-item>

    <FormItemContainer>
      <el-form-item label="价格" :prop="['UserConsultSet', 'RichtextCost']">
        <el-input-number
          v-model="formData.UserConsultSet.RichtextCost"
          :min="0"
          :precision="2"
          :step="0.1"
          style="width: 150px"
        />
      </el-form-item>
      <el-form-item
        label="原价"
        :prop="['UserConsultSet', 'ShowCost']"
        :rules="[
          {
            validator: validateShowCost,
            trigger: 'change',
          },
        ]"
      >
        <el-input-number
          v-model="formData.UserConsultSet.ShowCost"
          :min="formData.UserConsultSet.RichtextCost"
          :precision="2"
          :step="0.1"
          style="width: 150px"
        />
      </el-form-item>
    </FormItemContainer>
  </el-form>
  <el-dialog v-model="showCertificate" title="编辑认证信息" width="40%" destroy-on-close>
    <UserCertificate ref="userCertificateRef" :user-certificate-info="userCertificateInfo" />
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="showCertificate = false">取消</el-button>
        <el-button
          v-if="!isPreview"
          type="primary"
          :loading="dialogConfirmLoading"
          @click="handleUpdateUserCertificate"
        >
          确定
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import UserSelect from "@/components/UserSelect/index.vue";
import Other_Api from "@/api/other";
import type { BankCity, BankProvince, SubBranchInputDTO } from "@/api/other/types";
import type { CascaderInstance, CascaderOption, FormInstance, Resolve } from "element-plus";
import UserCertificate from "./UserCertificate.vue";
import Passport_Api from "@/api/passport";
import type { PageUserCertificate } from "./types";

export interface PageBaseData {
  UserWork: {
    Personality: string; // 性格描述
    InfoAddress: string; // 客情地址
    IsCertificatesComplete: boolean; // 证书是否完整
  };
  UserBill: {
    Id: string;
    CardNumber: string;
    BankName: string;
    BankCode: string;
    SubBranch: string;
    SubBranchCode: string;
    Province: string;
    ProvinceCode: string | number;
    City: string;
    CityCode: string;
  };
  UserConsultSet: {
    SelfReliance: boolean; // 是否自运行
    IsEnable: boolean; // 是否开启咨询
    RichtextCost: number; // 优惠价格
    ShowCost: number; // 原价价格
  };
  UserClaims: {
    UserId: string;
    ClaimType: string;
    ClaimValue: string;
    Remark: string;
    OnlyOne?: boolean;
  }[];
}

const selectorRef = useTemplateRef<CascaderInstance>("selectorRef");
const isPreview = inject<Ref<boolean>>("isPreview", ref(false));
const doctorId = inject<Ref<string>>("doctorId", ref(""));
const isDoctor = inject<Ref<boolean>>("isDoctor", ref(false));
const userSelectRef = ref<InstanceType<typeof UserSelect>>();
const formRef = ref<FormInstance>();
const formData = ref<PageBaseData>({
  UserWork: {
    Personality: "",
    InfoAddress: "",
    IsCertificatesComplete: false,
  },
  UserBill: {
    Id: "",
    CardNumber: "",
    BankName: "",
    BankCode: "",
    SubBranch: "",
    SubBranchCode: "",
    Province: "",
    ProvinceCode: "",
    City: "",
    CityCode: "",
  },
  UserConsultSet: {
    SelfReliance: false,
    IsEnable: false,
    RichtextCost: 0,
    ShowCost: 0,
  },
  UserClaims: [
    {
      UserId: doctorId.value,
      ClaimType: "GuideDoctor",
      ClaimValue: "",
      Remark: "",
    },
  ],
});
const accountOpeningList = ref<number[]>([]);
const placeAccountOptions = ref<CascaderOption[]>([]);
const openingBankList = ref<{ BranchId: string; BranchName: string }[]>([]);
const subBranchParams = ref<SubBranchInputDTO>({
  BankCode: "",
  CityCode: -1,
  PageIndex: 1,
  PageSize: 9999,
  Keyword: "",
});

const subBranchFocus = ref(false); // 开户支行输入框是否聚焦
const subBranchHovered = ref<boolean>(false); // 开户支行输入框是否悬停
const subBranchClearVisible = ref<boolean>(false); // 开户支行输入框是否显示清除按钮
const showCertificate = ref<boolean>(false); // 是否显示认证信息
const dialogConfirmLoading = ref<boolean>(false);
const userCertificateRef = ref<InstanceType<typeof UserCertificate>>();
const userCertificateInfo = ref<PageUserCertificate>({
  UserCertificates: [],
  WorkerTitle: "",
  WorkerType: "",
  UserId: "",
  UpsertUserCertificateMode: 1,
});

// 开户支行输入框失去焦点
const handleSubBranchInputBlur = () => {
  subBranchFocus.value = false;
  updateSubBranchClearVisible();
};

// 开户支行输入框清除
const handleSubBranchClear = () => {
  subBranchFocus.value = false;
  formData.value.UserBill.SubBranch = "";
  formData.value.UserBill.SubBranchCode = "";
};

// 开户支行鼠标进入事件
const handleSubBranchMouseEnter = () => {
  subBranchHovered.value = true;
  updateSubBranchClearVisible();
};

// 开户支行鼠标离开事件
const handleSubBranchMouseLeave = () => {
  subBranchHovered.value = false;
  updateSubBranchClearVisible();
};

// 开户支行输入框更新清除按钮的显示状态
const updateSubBranchClearVisible = () => {
  subBranchClearVisible.value =
    (subBranchHovered.value && !!formData.value.UserBill.SubBranch) ||
    (!subBranchHovered.value && !!formData.value.UserBill.SubBranch && subBranchFocus.value);
};

// 确认修改用户认证信息
const handleUpdateUserCertificate = async () => {
  const params: PageUserCertificate[] | null = await userCertificateRef.value!.handleSubmitInfo();
  if (!params) return;
  dialogConfirmLoading.value = true;
  Passport_Api.upsertAuthenticationData(params)
    .then((res) => {
      if (res.Type === 200) {
        ElNotification.success("操作成功");
        showCertificate.value = false;
      } else {
        ElNotification.error(res.Content);
      }
    })
    .catch((e) => {
      ElNotification.error("操作失败");
    })
    .finally(() => {
      dialogConfirmLoading.value = false;
    });
};

const handleSubmitForm = (): Promise<PageBaseData | null> => {
  return new Promise<PageBaseData | null>((resolve) => {
    formRef.value?.validate((valid: boolean) => {
      if (valid) {
        resolve(formData.value);
      } else {
        resolve(null);
      }
    });
  });
};
const validateShowCost = (rule: any, value: any, callback: any) => {
  if (value !== null && value !== undefined && value < formData.value.UserConsultSet.RichtextCost) {
    callback(new Error("请输入大于等于限时价格"));
  } else {
    callback();
  }
};

const handleUpdateCertificate = async () => {
  const res = await Passport_Api.getDoctorOrganizationAuthentications({ userId: doctorId.value });
  if (res.Type === 200 && res.Data.length) {
    const defaultData = res.Data.filter((s) => s.IsDefault);
    userCertificateInfo.value.UserCertificates = defaultData[0].UserCertificates;
    userCertificateInfo.value.WorkerTitle = defaultData[0].WorkerTitle;
    userCertificateInfo.value.WorkerType = defaultData[0].WorkerType;
  }
  showCertificate.value = true;
};
const cascaderLazyLoad = async (node: any, resolve: Resolve) => {
  const { level, data } = node;
  if (level === 0) {
    const list = (await onGetBankProvinceOrCity("Province")) as BankProvince[];
    let options: CascaderOption[] = [];
    list.forEach((s) => {
      options.push({
        Name: s.ProvinceName,
        Code: s.ProvinceCode,
      });
    });
    resolve(options);
  }
  if (level === 1) {
    const Id = data.Code;
    const list = (await onGetBankProvinceOrCity("City", Id)) as BankCity[];
    let options: CascaderOption[] = [];
    list.forEach((s) => {
      options.push({
        Name: s.CityName,
        Code: s.CityCode,
        leaf: true,
      });
    });
    resolve(options);
  }
};
const onGetBankProvinceOrCity = async (
  type: string,
  Id?: string
): Promise<BankProvince[] | BankCity[]> => {
  const data: { provinceCode?: string } = {};
  if (Id) {
    data.provinceCode = Id;
  }
  if (type === "Province") {
    const res = await Other_Api.getBankProvinceOrCity<BankProvince>(type, data);
    if (res.Type === 200) {
      return res.Data;
    } else {
      return [];
    }
  } else {
    const res = await Other_Api.getBankProvinceOrCity<BankCity>(type, data);
    if (res.Type === 200) {
      return res.Data;
    } else {
      return [];
    }
  }
};
/** 银行卡号失去焦点 */
const onBlurBankAccount = () => {
  const value = formData.value.UserBill.CardNumber;
  accountOpeningList.value = [];
  subBranchParams.value.CityCode = -1;
  formData.value.UserBill.SubBranchCode = "";
  formData.value.UserBill.SubBranch = "";
  formData.value.UserBill.City = "";
  formData.value.UserBill.CityCode = "";
  formData.value.UserBill.Province = "";
  formData.value.UserBill.ProvinceCode = "";
  handleGetBankCardInfo(value);
};
/** 获取银行卡信息 */
const handleGetBankCardInfo = async (value: string) => {
  if (!value) {
    return;
  }
  const res = await Other_Api.getBankInfo({ BankNum: value });
  if (res.Type === 200) {
    formData.value.UserBill.BankCode = res.Data.BankCode;
    formData.value.UserBill.BankName = res.Data.BankName;
    subBranchParams.value.BankCode = res.Data.BankCode;
  }
};
/** 开户地发生改变 */
const onChangePlaceAccount = () => {
  const getCheckedNodes = selectorRef.value?.getCheckedNodes(true);
  formData.value.UserBill.City = getCheckedNodes![0].data!.Name as string;
  formData.value.UserBill.CityCode = getCheckedNodes![0].data!.Code as string;
  formData.value.UserBill.Province = getCheckedNodes![0].parent!.data!.Name as string;
  formData.value.UserBill.ProvinceCode = getCheckedNodes![0].parent!.data!.Code as string;
  formData.value.UserBill.SubBranchCode = "";
  formData.value.UserBill.SubBranch = "";
  subBranchParams.value.CityCode = Number(getCheckedNodes![0].data!.Code);
  onGetOpeningBank();
};
/** 获取开户行 */
const onGetOpeningBank = async () => {
  if (!subBranchParams.value.CityCode) {
    return;
  }
  const res = await Other_Api.getSubBranch(subBranchParams.value);
  if (res.Type === 200) {
    openingBankList.value = res.Data.Rows;
  }
};
/** 开户行发生改变 */
const onChangeOpeningBank = (subBranchCode: string) => {
  formData.value.UserBill.SubBranchCode = subBranchCode;
  formData.value.UserBill.SubBranch =
    openingBankList.value.find((s) => s.BranchId === subBranchCode)?.BranchName || "";
};
/** 指导医生发生改变 */
const onChangeDoctor = () => {
  // 2025-07-15 和朗哥沟通 需要将ClaimValue === GuideDoctor的数据添加一个OnlyOne = true
  formData.value.UserClaims[0].ClaimType = "GuideDoctor";
  formData.value.UserClaims[0].UserId = doctorId.value;
  formData.value.UserClaims[0].OnlyOne = true;
};
const handleProcessData = async (newVal: PageBaseData) => {
  console.log("newVal.UserBill", JSON.parse(JSON.stringify(newVal.UserBill)));
  // 处理开户地和开户行数据
  if (newVal.UserBill) {
    newVal.UserBill.ProvinceCode = Number(newVal.UserBill.ProvinceCode);
    accountOpeningList.value = [
      Number(newVal.UserBill.ProvinceCode),
      Number(newVal.UserBill.CityCode),
    ];
    openingBankList.value = [
      {
        BranchId: newVal.UserBill.SubBranchCode,
        BranchName: newVal.UserBill.SubBranch,
      },
    ];
    subBranchParams.value.BankCode = newVal.UserBill.BankCode;
    subBranchParams.value.CityCode = Number(newVal.UserBill.CityCode);
    onGetOpeningBank();
  }
};

interface Props {
  baseInfo: PageBaseData | null;
}
const props = defineProps<Props>();
watch(
  () => props.baseInfo,
  async (newVal) => {
    if (!newVal) {
      return;
    }
    const copyValue = JSON.parse(JSON.stringify(newVal));
    await handleProcessData(copyValue);
    formData.value = copyValue;
  },
  { immediate: true }
);
defineExpose({
  handleSubmitForm,
});
</script>

<style lang="scss" scoped>
.el-form {
  max-width: 900px;
  margin: 0 auto;
  padding: 20px;
}

:deep(.el-form-item__label) {
  font-weight: 500;
}

.el-input-number {
  width: 100%;
}

.sub-branch-item {
  padding: 8px 2px;
  cursor: pointer;

  &:hover {
    background-color: #f5f7fa;
  }
}
.sub-branch-selected {
  color: #4080ff;
  font-weight: bold;
}
</style>
