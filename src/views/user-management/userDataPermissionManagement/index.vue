<template>
  <div class="app-container">
    <BaseTableSearchContainer @size-changed="tableResize">
      <template #search>
        <TBSearchContainer>
          <template #left>
            <el-form :model="queryParams" label-position="right" :inline="true">
              <el-form-item label="角色" prop="RoleId">
                <el-select
                  v-model="queryParams.RoleId"
                  placeholder="请选择"
                  clearable
                  :empty-values="[null, undefined, '']"
                  :value-on-clear="() => null"
                  class="w-130px!"
                >
                  <el-option
                    v-for="item in roleList"
                    :key="item.Id"
                    :label="item.Name"
                    :value="item.Id!"
                  />
                </el-select>
              </el-form-item>
              <el-form-item label="关键字" prop="Keyword">
                <el-input
                  v-model="queryParams.Keyword"
                  placeholder="姓名/电话号码"
                  @keyup.enter="handleQuery"
                />
              </el-form-item>
            </el-form>
          </template>
          <template #right>
            <el-button type="primary" icon="search" @click="handleQuery">搜索</el-button>
            <el-button type="primary" @click="handleBatchBind">批量绑定</el-button>
          </template>
        </TBSearchContainer>
      </template>
      <template #table>
        <el-table
          ref="tableRef"
          v-loading="tableLoading"
          :data="pageData"
          :total="total"
          border
          row-key="UserId"
          :height="tableFluidHeight"
          highlight-current-row
          style="text-align: center; flex: 1"
          @selection-change="handleSelectionChange"
        >
          <el-table-column type="selection" reserve-selection width="55" align="center" />
          <el-table-column label="角色" align="center">
            <template #default="scope">
              {{ scope.row.RoleName }}
            </template>
          </el-table-column>
          <el-table-column label="姓名" align="center">
            <template #default="scope">
              {{ scope.row.UserName }}
            </template>
          </el-table-column>
          <el-table-column label="手机号" align="center">
            <template #default="scope">
              {{ scope.row.PhoneNumber }}
            </template>
          </el-table-column>
          <el-table-column label="数据权限" align="center">
            <template #default="scope">
              {{ scope.row.OperateScopeOrgName }}
            </template>
          </el-table-column>
          <el-table-column fixed="right" label="操作" align="center" width="100">
            <template #default="scope">
              <el-button link type="primary" size="small" @click="handlePreviewOrEdit(scope.row)">
                编辑权限
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </template>
      <template #pagination>
        <Pagination
          v-if="total > 0"
          v-model:total="total"
          v-model:page="queryParams.PageInput.PageIndex"
          v-model:limit="queryParams.PageInput.PageSize"
          @pagination="handleGetTableList"
        />
      </template>
    </BaseTableSearchContainer>
    <el-dialog
      v-model="showDialog"
      title="绑定"
      width="500"
      destroy-on-close
      :close-on-press-escape="isPreview"
      :close-on-click-modal="isPreview"
    >
      <OperateScopeContent ref="operateScopeContentRef" :user-ids="selectedTableIds" />
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="showDialog = false">取消</el-button>
          <el-button type="primary" :loading="dialogConfirmLoading" @click="handleConfirm">
            确定
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import Passport_Api from "@/api/passport";
import { OperateScopeUserInputDTO, OperateScopeUserItem } from "@/api/passport/types";
import { useTableConfig } from "@/hooks/useTableConfig";
import OperateScopeContent from "./components/OperateScopeContent.vue";

defineOptions({
  name: "UserDataPermissionManagement",
});

const queryParams = ref<OperateScopeUserInputDTO>({
  RoleId: null,
  Keyword: "",
  PageInput: {
    PageIndex: 1,
    PageSize: 20,
  },
});

const operateScopeContentRef = useTemplateRef("operateScopeContentRef");

const roleList = ref<BaseRole[]>([]);

const isPreview = ref<boolean>(false);
provide("isPreview", isPreview);

const showDialog = ref<boolean>(false);

const {
  tableLoading,
  pageData,
  total,
  tableRef,
  tableFluidHeight,
  tableResize,
  dialogConfirmLoading,
  selectedTableIds,
} = useTableConfig<OperateScopeUserItem>();

const handleQuery = () => {
  queryParams.value.PageInput.PageIndex = 1;
  handleGetTableList();
};

const handleBatchBind = () => {
  console.log(selectedTableIds.value);
  if (!selectedTableIds.value.length) {
    ElMessage.warning("请选择要批量绑定的用户");
    return;
  }
  showDialog.value = true;
  selectedTableIds.value = selectedTableIds.value;
};

const handleSelectionChange = (selection: OperateScopeUserItem[]) => {
  selectedTableIds.value = selection.map((item) => item.UserId!);
};

const handleConfirm = () => {
  const params = operateScopeContentRef.value?.handleSubmit();
  dialogConfirmLoading.value = true;
  if (!params) {
    Passport_Api.delOperateScopes(selectedTableIds.value)
      .then((res) => {
        if (res.Type === 200) {
          ElMessage.success(res.Content!);
          selectedTableIds.value = [];
          tableRef.value?.clearSelection();
          showDialog.value = false;
          handleGetTableList();
        } else {
          ElMessage.error(res.Content!);
        }
      })
      .finally(() => {
        dialogConfirmLoading.value = false;
      });
    return;
  }
  Passport_Api.setOperateScopes(params)
    .then((res) => {
      if (res.Type === 200) {
        ElMessage.success(res.Content!);
        selectedTableIds.value = [];
        tableRef.value?.clearSelection();
        showDialog.value = false;
        handleGetTableList();
      } else {
        ElMessage.error(res.Content!);
      }
    })
    .finally(() => {
      dialogConfirmLoading.value = false;
    });
};

const handlePreviewOrEdit = async (row: OperateScopeUserItem) => {
  isPreview.value = true;
  selectedTableIds.value = [row.UserId!];
  showDialog.value = true;
};
const handleGetTableList = async () => {
  tableLoading.value = true;
  const res = await Passport_Api.getOperateScopeUserList(queryParams.value);
  if (res.Type === 200) {
    pageData.value = res.Data.Rows;
    total.value = res.Data.Total;
  }
  tableLoading.value = false;
};

const getRoleList = async () => {
  const res = await Passport_Api.read({});
  if (res.Type === 200) {
    roleList.value = res.Data.filter(
      (v) =>
        v.RoleType === "promoter" ||
        v.RoleType === "sales" ||
        v.RoleType === "assistant" ||
        v.RoleType === "externalSeller" ||
        v.RoleType === "internetHospitalAdmin"
    );
  }
};

onMounted(() => {
  getRoleList();
});

onActivated(() => {
  handleGetTableList();
});
</script>

<style lang="scss" scoped></style>
