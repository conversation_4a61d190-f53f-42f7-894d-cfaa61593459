<template>
  <div class="h-600px overflow-auto relative">
    <el-input
      v-model="filterText"
      style="position: sticky; left: 0; top: 0; z-index: 99"
      placeholder="输入关键字进行过滤"
    />
    <el-tree
      ref="tree"
      :props="{
        label: 'Name',
        isLeaf: 'IsLeaf',
      }"
      :load="loadNode"
      lazy
      show-checkbox
      :filter-node-method="filterNode"
      node-key="Id"
      :default-checked-keys="defaultCheckedKeys"
      @check-change="handleCheckChange"
    />
  </div>
</template>

<script setup lang="ts">
import Passport_Api from "@/api/passport";
import { OperateScopeUserItem } from "@/api/passport/types";

interface PagesOrganization extends BaseOrganization {
  Flag?: string;
}
interface PagesUserProfile extends BaseUserProfile {
  Flag?: string;
  IsLeaf?: boolean;
  OrgId?: string;
}

// 用户操作范围数据项类型定义
interface UserScopeDataItem extends OperateScopeUserItem {
  UserId: string;
}

const filterText = ref<string>("");
const tree = ref<InstanceType<typeof ElTree>>();
const defaultCheckedKeys = ref<string[]>([]);
const allHospitalList = ref<PagesOrganization[]>([]);
const selectTreeData = ref<OperateScopeUserItem[]>([]);
const loadNode = async (node: any, resolve: any) => {
  if (node.level === 0) {
    const org = await onGetOrganizationList();
    return resolve(org);
  }
  if (node.level === 1) {
    const orgId = node.data.Id;
    // 获取机构下面的所有医生、治疗师、护士
    const doc = await onGetDoctorList(orgId);
    return resolve(doc);
  }
};

const filterNode = (value: string, data: any) => {
  if (!value) return true;
  return data.Name.indexOf(value) !== -1;
};

const onGetOrganizationList = async () => {
  const res = await Passport_Api.getAllOrganizationList({
    DtoTypeName: "QueryOrgDtoForDropDownList",
    IsEnabled: true,
  });
  if (res.Type === 200) {
    const newData = res.Data.map((s): PagesOrganization => {
      return {
        ...s,
        Flag: "hospital",
      };
    });
    allHospitalList.value = newData || [];
    return newData || [];
  }
};

const onGetDoctorList = async (orgId: string) => {
  const res = await Passport_Api.getUserProfile({
    IsEnabled: true,
    OrgIds: [orgId],
    RoleTypes: ["nurse", "doctor", "therapist"],
    SingleOne: false,
    DtoTypeName: "QueryUserOutputDto3",
    Pageable: true,
    PageSize: 99999,
  });
  if (res.Type === 200) {
    const newData = res.Data.Row.map((s): PagesUserProfile => {
      return {
        ...s,
        Flag: "doctor",
        IsLeaf: true,
        OrgId: orgId,
      };
    });
    return newData || [];
  }
};

// 处理医院选择逻辑
const handleHospitalCheck = (data: any, checked: boolean, indeterminate: boolean) => {
  if (checked) {
    // 全选医院
    const index = selectTreeData.value.findIndex((v) => v.RelationId === data.Id && v.Type === 2);
    if (index === -1) {
      selectTreeData.value.push({
        RelationId: data.Id,
        Type: 2,
      });
    }
  } else {
    // 取消选择医院
    const index = selectTreeData.value.findIndex((v) => v.RelationId === data.Id && v.Type === 2);
    if (index > -1) {
      selectTreeData.value.splice(index, 1);
      // 处理子节点选中状态
      if (indeterminate) {
        handleIndeterminateState(data.Id);
      }
    }
  }
};

// 处理医院半选状态的子节点
const handleIndeterminateState = (orgId: string) => {
  const childSelect = tree.value?.getCheckedNodes(true, true);
  if (childSelect && childSelect.length > 0) {
    childSelect.forEach((s) => {
      if (s.OrgId === orgId) {
        selectTreeData.value.push({
          RelationId: s.Id,
          Type: 1,
          OrgId: s.OrgId,
        });
      }
    });
  }
};

// 处理医生选择逻辑
const handleDoctorCheck = (data: any, checked: boolean) => {
  if (checked) {
    selectTreeData.value.push({
      RelationId: data.Id,
      Type: 1,
      OrgId: data.OrgId,
    });
  } else {
    const index = selectTreeData.value.findIndex((v) => v.RelationId === data.Id);
    if (index > -1) {
      selectTreeData.value.splice(index, 1);
    }
  }
};

// 处理最终数据统一逻辑
const processFinalData = () => {
  const OrgTypeOneList = selectTreeData.value.filter((s) => s.Type === 2);
  if (OrgTypeOneList.length > 0) {
    const OrgTypeOneIdMap = OrgTypeOneList.map((s) => s.RelationId);
    const type2Data = selectTreeData.value.filter((item) => item.Type === 2);
    const type1Data = selectTreeData.value.filter(
      (item) => item.Type === 1 && !OrgTypeOneIdMap.includes(item.OrgId)
    );
    selectTreeData.value = [...type2Data, ...type1Data];
  }
};

const handleCheckChange = (data: any, checked: boolean, indeterminate: boolean) => {
  const flag = data.Flag;

  if (flag === "hospital") {
    handleHospitalCheck(data, checked, indeterminate);
  } else if (flag === "doctor") {
    handleDoctorCheck(data, checked);
  }

  // 最终做统一的处理
  processFinalData();
};

const handleSubmit = (): UserScopeDataItem[] | null => {
  if (!selectTreeData.value.length) {
    return null;
  }

  let userScopeDataList: UserScopeDataItem[] = [];
  console.log("selectTreeData.value", selectTreeData.value);

  // 为每个操作范围项和用户ID生成用户范围数据
  selectTreeData.value.forEach((scopeItem) => {
    props.userIds.forEach((userId) => {
      userScopeDataList.push({
        ...scopeItem,
        UserId: userId,
      });
    });
  });

  // 去重处理：移除重复的用户范围数据项
  userScopeDataList = userScopeDataList.reduce(
    (accumulator: UserScopeDataItem[], currentItem: UserScopeDataItem) => {
      const index = accumulator.findIndex(
        (existingItem: UserScopeDataItem) =>
          existingItem.Type === currentItem.Type &&
          existingItem.UserId === currentItem.UserId &&
          existingItem.RelationId === currentItem.RelationId
      );
      if (index === -1) {
        accumulator.push(currentItem);
      }
      return accumulator;
    },
    []
  );

  console.log("userScopeDataList", userScopeDataList);
  return userScopeDataList;
};

const handleProcessingData = async (userIds: string[]) => {
  await onGetOrganizationList();
  if (userIds.length > 1) return;
  const res = await Passport_Api.getOperateScopeList({
    UserId: userIds[0],
  });
  if (res.Type === 200 && res.Data.length) {
    selectTreeData.value = res.Data;
    onEchoingData(res.Data);
  }
};

const onEchoingData = (data: OperateScopeUserItem[]) => {
  console.log("allHospitalList.value", allHospitalList.value);
  const selectKeys: string[] = [];
  data.forEach((v) => {
    allHospitalList.value.forEach((s) => {
      if (v.Type === 1) {
        if (s.Id === v.OrgId) {
          selectKeys.push(v.RelationId!);
          nextTick(() => {
            var node = tree.value?.getNode(v.OrgId!);
            if (node) {
              node.indeterminate = true;
            }
          });
        }
      }
      if (v.Type === 2) {
        if (s.Id === v.RelationId) {
          selectKeys.push(v.RelationId!);
        }
      }
    });
  });
  defaultCheckedKeys.value = selectKeys;
};

interface Props {
  userIds: string[];
}
const props = defineProps<Props>();

watch(
  () => props.userIds,
  (newVal) => {
    if (newVal && newVal.length) {
      handleProcessingData(newVal);
    }
  },
  { immediate: true }
);
watch(
  () => filterText.value,
  (newVal) => {
    tree.value?.filter(newVal);
  }
);

defineExpose({
  handleSubmit,
});
</script>

<style lang="scss" scoped></style>
