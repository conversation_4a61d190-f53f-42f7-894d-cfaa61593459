<template>
  <el-form
    ref="formRef"
    :model="formData"
    :rules="rules"
    :label-width="100"
    label-position="right"
    scroll-to-first-error
    :disabled="isPreview"
  >
    <FormItemContainer>
      <el-form-item label="远程会诊" prop="IsTechExchange">
        <el-switch v-model="formData.IsTechExchange" />
      </el-form-item>
      <el-form-item label="双向转诊" prop="IsReferral">
        <el-switch v-model="formData.IsReferral" />
      </el-form-item>
      <el-form-item label="是否治疗点" prop="IsTreatment">
        <el-switch v-model="formData.IsTreatment" />
      </el-form-item>
      <el-form-item label="治疗预约" prop="IsTreatmentBooking">
        <el-switch v-model="formData.IsTreatmentBooking" />
      </el-form-item>
    </FormItemContainer>
    <el-form-item label="工作时间" prop="Work">
      <el-input v-model="formData.Work" type="textarea" maxlength="500" show-word-limit />
    </el-form-item>
    <el-form-item label="预约说明" prop="TreatmentBookingDescribe">
      <el-input
        v-model="formData.TreatmentBookingDescribe"
        type="textarea"
        maxlength="500"
        show-word-limit
      />
    </el-form-item>
    <el-form-item label="机构介绍" prop="Remark">
      <WangEditor v-model="formData.Remark" />
    </el-form-item>
  </el-form>
</template>

<script setup lang="ts">
import { FormRules } from "element-plus";

export interface OpenPrescriptionType {
  IsTechExchange?: boolean;
  IsReferral?: boolean;
  IsTreatment?: boolean;
  IsTreatmentBooking?: boolean;
  Work?: string;
  Remark?: string;
  TreatmentBookingDescribe?: string;
}
const isPreview = inject<Ref<boolean>>("isPreview", ref(false));
const formRef = useTemplateRef("formRef");
const formData = ref<OpenPrescriptionType>({
  IsTechExchange: false,
  IsReferral: false,
  IsTreatment: false,
  IsTreatmentBooking: false,
  Work: "",
  Remark: "",
  TreatmentBookingDescribe: "",
});
const rules = reactive<FormRules>({});

const handleSubmitForm = (): Promise<OpenPrescriptionType | null> => {
  return new Promise<OpenPrescriptionType | null>((resolve) => {
    formRef.value?.validate((valid: boolean) => {
      if (valid) {
        resolve(formData.value);
      } else {
        resolve(null);
      }
    });
  });
};

interface Props {
  openPrescription: OpenPrescriptionType | null;
}
const props = defineProps<Props>();
watch(
  () => props.openPrescription,
  async (newVal) => {
    if (!newVal) {
      return;
    }
    const copyValue = JSON.parse(JSON.stringify(newVal));
    formData.value = copyValue;
  },
  { immediate: true }
);
defineExpose({
  handleSubmitForm,
});
</script>

<style lang="scss" scoped></style>
