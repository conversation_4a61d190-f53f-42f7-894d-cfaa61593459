<template>
  <!-- 多层诱饵字段策略，用于更彻底地混淆浏览器自动填充识别 -->
  <div style="position: absolute; top: -9999px; left: -9999px; pointer-events: none; opacity: 0">
    <input :name="fakeFieldName1" type="text" :autocomplete="randomAutoComplete1" tabindex="-1" />
    <input
      :name="fakeFieldName2"
      type="password"
      :autocomplete="randomAutoComplete2"
      tabindex="-1"
    />
    <input :name="fakeFieldName3" type="email" autocomplete="email" tabindex="-1" />
  </div>

  <!-- 可见但偏移的诱饵字段 -->
  <div style="position: relative; height: 0; overflow: hidden">
    <input
      type="text"
      autocomplete="username"
      style="position: absolute; top: -100px"
      tabindex="-1"
    />
    <input
      type="password"
      autocomplete="current-password"
      style="position: absolute; top: -100px"
      tabindex="-1"
    />
  </div>

  <el-form
    ref="formRef"
    :model="formData"
    :rules="rules"
    label-width="100px"
    scroll-to-error
    :disabled="isPreview"
    autocomplete="off"
  >
    <!-- 基本信息行 -->
    <el-row :gutter="20">
      <el-col :span="12">
        <el-form-item label="姓名" prop="Name">
          <el-input v-model="formData.Name" type="text" />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="用户名" prop="UserName">
          <el-input
            :id="dynamicUserNameId"
            ref="usernameRef"
            v-model="formData.UserName"
            type="text"
            :name="dynamicUserNameField"
            :autocomplete="userNameAutoComplete"
            readonly
            @focus="handleInputFocus"
            @input="handleUserNameInput"
          />
        </el-form-item>
      </el-col>
    </el-row>

    <!-- 昵称和编码行 -->
    <el-row :gutter="20">
      <el-col :span="12">
        <el-form-item label="昵称">
          <el-input v-model="formData.NickName" type="text" />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="编码">
          <el-input v-model="formData.Code" type="text" />
        </el-form-item>
      </el-col>
    </el-row>

    <!-- 性别和生日行 -->
    <el-row :gutter="20">
      <el-col :span="12">
        <el-form-item label="性别" prop="Sex">
          <el-select v-model="formData.Sex" placeholder="性别" style="width: 100%">
            <el-option
              v-for="item in sexDictList"
              :key="item.Key"
              :label="item.Key"
              :value="item.Value!"
            />
          </el-select>
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="生日">
          <el-date-picker
            v-model="formData.Birthday"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
            type="date"
            placeholder="选择日期"
            style="width: 100%"
          />
        </el-form-item>
      </el-col>
    </el-row>

    <!-- 手机号行 -->
    <el-row :gutter="20">
      <el-col :span="12">
        <el-form-item label="手机号" prop="PhoneNumber">
          <el-input v-model="formData.PhoneNumber" type="text" />
        </el-form-item>
      </el-col>
      <el-col v-if="!formData.Id" :span="12">
        <el-form-item label="密码" prop="Password">
          <el-input
            :id="dynamicPasswordId"
            ref="passwordRef"
            v-model="formData.Password"
            type="password"
            show-password
            :name="dynamicPasswordField"
            :autocomplete="passwordAutoComplete"
            readonly
            @focus="handleInputFocus"
            @input="handlePasswordInput"
          />
        </el-form-item>
      </el-col>
    </el-row>

    <!-- 开关状态行 -->
    <el-row :gutter="20">
      <el-col :span="12">
        <el-form-item label="是否启用">
          <el-switch v-model="formData.IsEnabled" />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="是否锁定">
          <el-switch v-model="formData.IsLocked" />
        </el-form-item>
      </el-col>
    </el-row>

    <!-- 图片上传区域 -->
    <el-row :gutter="20">
      <el-col :span="12">
        <el-form-item label="头像">
          <SingleImageUpload v-model="formData.HeadImg" />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="微信二维码" label-width="120px">
          <SingleImageUpload v-model="formData.UserExternalIdentify.WeChatQrCode" />
        </el-form-item>
      </el-col>
    </el-row>
    <el-row v-if="!formData.UserWork?.QrCode" :gutter="20">
      <el-form-item label="头像">
        <QRCode
          v-model="formData.UserWork!.QrCode!"
          :value="`https://oss-biz.kangfx.com?docId=${formData.Id}`"
        />
      </el-form-item>
    </el-row>
  </el-form>
</template>

<script setup lang="ts">
import { CreateUserInputDTO } from "@/api/passport/types";
import { FormInstance, FormRules } from "element-plus";

// 生成随机字符串的工具函数
const generateRandomString = (length: number = 8): string => {
  const chars = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
  let result = "";
  for (let i = 0; i < length; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  return result;
};

// 动态生成的字段名和属性
const dynamicUserNameField = ref(`field_${generateRandomString()}`);
const dynamicUserNameId = ref(`input_${generateRandomString()}`);
const dynamicPasswordField = ref(`field_${generateRandomString()}`);
const dynamicPasswordId = ref(`input_${generateRandomString()}`);

// 诱饵字段的随机名称和属性
const fakeFieldName1 = ref(`fake_${generateRandomString()}`);
const fakeFieldName2 = ref(`fake_${generateRandomString()}`);
const fakeFieldName3 = ref(`fake_${generateRandomString()}`);

// 随机的autocomplete值
const userNameAutoComplete = ref(`nope-${generateRandomString(4)}`);
const passwordAutoComplete = ref(`new-password-${generateRandomString(4)}`);
const randomAutoComplete1 = ref(`random-${generateRandomString(6)}`);
const randomAutoComplete2 = ref(`random-${generateRandomString(6)}`);

const formData = ref<CreateUserInputDTO>({
  Id: "",
  Name: "",
  UserName: "",
  NickName: "",
  Sex: "",
  Birthday: "",
  Code: "",
  PhoneNumber: "",
  Password: "",
  IsEnabled: true,
  IsLocked: false,
  HeadImg: "",
  UserExternalIdentify: {
    WeChatQrCode: "",
  },
  UserWork: {
    QrCode: "",
  },
});

const formRef = ref<FormInstance>();
const usernameRef = ref();
const passwordRef = ref();
const isPreview = inject("isPreview") as Ref<boolean>;
const sexDictList = inject("sexDictList") as Ref<ReadDict[]>;

// 检测自动填充并清空字段值
const detectAndClearAutoFill = () => {
  // 检测用户名字段是否被自动填充
  if (usernameRef.value?.input && usernameRef.value.input.value && !formData.value.UserName) {
    usernameRef.value.input.value = "";
    formData.value.UserName = "";
  }

  // 检测密码字段是否被自动填充
  if (passwordRef.value?.input && passwordRef.value.input.value && !formData.value.Password) {
    passwordRef.value.input.value = "";
    formData.value.Password = "";
  }
};

// 处理用户名输入事件
const handleUserNameInput = (value: string) => {
  // 如果检测到可能的自动填充（值突然出现但不是用户手动输入）
  nextTick(() => {
    if (value && !formData.value.UserName) {
      // 清空可能的自动填充值
      formData.value.UserName = "";
      if (usernameRef.value?.input) {
        usernameRef.value.input.value = "";
      }
    }
  });
};

// 处理密码输入事件
const handlePasswordInput = (value: string) => {
  // 如果检测到可能的自动填充（值突然出现但不是用户手动输入）
  nextTick(() => {
    if (value && !formData.value.Password) {
      // 清空可能的自动填充值
      formData.value.Password = "";
      if (passwordRef.value?.input) {
        passwordRef.value.input.value = "";
      }
    }
  });
};

// 处理输入框焦点事件，移除 readonly 属性以允许用户输入
const handleInputFocus = (event: FocusEvent) => {
  const target = event.target as HTMLInputElement;
  if (target && target.hasAttribute("readonly")) {
    target.removeAttribute("readonly");

    // 清空可能的自动填充值
    nextTick(() => {
      detectAndClearAutoFill();
    });
  }
};

// 多阶段防自动填充初始化
const initAntiAutoFill = () => {
  // 第一阶段：立即清空（500ms）
  setTimeout(() => {
    detectAndClearAutoFill();
  }, 500);

  // 第二阶段：移除readonly（1000ms）
  setTimeout(() => {
    if (usernameRef.value?.input) {
      usernameRef.value.input.removeAttribute("readonly");
    }
    if (passwordRef.value?.input) {
      passwordRef.value.input.removeAttribute("readonly");
    }
    detectAndClearAutoFill();
  }, 1000);

  // 第三阶段：最终检查和清空（1500ms）
  setTimeout(() => {
    detectAndClearAutoFill();

    // 更新动态属性以进一步混淆
    dynamicUserNameField.value = `field_${generateRandomString()}`;
    dynamicPasswordField.value = `field_${generateRandomString()}`;
    userNameAutoComplete.value = `nope-${generateRandomString(4)}`;
    passwordAutoComplete.value = `new-password-${generateRandomString(4)}`;
  }, 1500);
};

// 组件挂载后的防自动填充处理
onMounted(() => {
  nextTick(() => {
    // 初始化防自动填充机制
    initAntiAutoFill();

    // 添加定期检查机制
    const intervalId = setInterval(() => {
      detectAndClearAutoFill();
    }, 200);

    // 5秒后停止定期检查
    setTimeout(() => {
      clearInterval(intervalId);
    }, 5000);
  });
});

const rules = reactive<FormRules>({
  Name: [{ required: true, message: "请输入名字", trigger: "blur" }],
  UserName: [
    { required: true, message: "请输入用户名", trigger: "blur" },
    {
      pattern: /^[a-zA-Z0-9]+$/,
      message: "用户名只能包含英文字母和数字",
      trigger: "blur",
    },
  ],
  PhoneNumber: [
    { required: true, message: "请输入手机号", trigger: "blur" },
    {
      pattern: /^1[3-9]\d{9}$/,
      message: "请输入正确的手机号格式",
      trigger: "blur",
    },
  ],
  Password: [
    { required: true, message: "请输入密码", trigger: "blur" },
    {
      min: 6,
      message: "密码至少需要6位",
      trigger: "blur",
    },
  ],
});

const handleSubmit = async (): Promise<CreateUserInputDTO | null> => {
  if (!formRef.value) return null;
  try {
    await formRef.value.validate();
    return formData.value;
  } catch (error) {
    return null;
  }
};

interface Props {
  userInfo: CreateUserInputDTO | null;
}
const props = defineProps<Props>();
watch(
  () => props.userInfo,
  async (newVal) => {
    if (!newVal) {
      return;
    }
    const copyValue = JSON.parse(JSON.stringify(newVal));
    if (!copyValue.UserWork) {
      copyValue.UserWork = {
        QrCode: "",
      };
    }
    formData.value = copyValue;
  },
  { immediate: true }
);
defineExpose({
  handleSubmit,
});
</script>

<style lang="scss" scoped>
.el-form {
  .el-row {
    margin-bottom: 8px;
  }

  .el-form-item {
    margin-bottom: 18px;
  }

  .el-divider {
    margin: 24px 0 16px;

    :deep(.el-divider__text) {
      font-weight: 500;
      color: #606266;
    }
  }

  // 确保开关组件在表单项中垂直居中
  .el-form-item__content {
    .el-switch {
      margin-top: 2px;
    }
  }
}
</style>
