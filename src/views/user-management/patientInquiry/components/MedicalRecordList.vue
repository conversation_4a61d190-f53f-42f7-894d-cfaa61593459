<template>
  <div v-loading="loading" class="overflow-y-auto h-550px">
    <el-table v-loading="loading" :data="tableData" border highlight-current-row>
      <el-table-column label="就诊时间" prop="InDate" align="center" />
      <el-table-column label="就诊科室" prop="DepartmentName" align="center" />
      <el-table-column label="诊断" prop="DiagnoseDesc" align="center" />
      <el-table-column label="接诊医生" prop="DoctorName" width="100" align="center" />
      <el-table-column label="来源" width="100" align="center">
        <template #default="{ row }">
          {{ row.IsSelfBuild ? "患者自建" : "在线问诊" }}
        </template>
      </el-table-column>
      <el-table-column label="操作" width="100" align="center" fixed="right">
        <template #default="{ row }">
          <el-button link type="primary" @click="onViewMedicalRecord(row)">查看</el-button>
        </template>
      </el-table-column>
    </el-table>
  </div>

  <!-- 查看病历详情 -->
  <el-dialog
    v-model="showDetailDialog.isShow"
    title="病历详情"
    width="900"
    destroy-on-close
    @close="showDetailDialog.isShow = false"
  >
    <MedicalRecordDetail :data="showDetailDialog.data" @cancel="showDetailDialog.isShow = false" />
  </el-dialog>
</template>

<script setup lang="ts">
import Record_Api from "@/api/record";
import dayjs from "dayjs";
import MedicalRecordDetail from "./MedicalRecordDetail.vue";
import { VisitInfo } from "@/api/record/types";

const kEnableDebug = false;
const props = defineProps<{
  patientId: string;
  orgId?: string;
}>();

interface TableDataItem extends VisitInfo {
  DiagnoseDesc?: string;
}

const showDetailDialog = reactive({
  isShow: false,
  data: {} as VisitInfo,
});

onMounted(() => {
  requestMedicalRecord();
});

const loading = ref(false);
const tableData = ref<TableDataItem[]>([]);

// 查看病历
function onViewMedicalRecord(row: TableDataItem) {
  kEnableDebug && console.debug("查看病历", row);

  if (!row.Id) {
    ElMessage.error("病历id为空");
    return;
  }

  showDetailDialog.data = row;
  showDetailDialog.isShow = true;
}

// 请求病历列表
async function requestMedicalRecord() {
  loading.value = true;
  const r = await Record_Api.getVisitsByUserId({
    pageIndex: 1,
    pageSize: 999,
    userId: props.patientId,
    organizationId: props.orgId,
  });

  if (r.Type !== 200) {
    loading.value = false;
    ElMessage.error(r.Content);
    return;
  }

  tableData.value = r.Data.map((item: VisitInfo) => {
    return {
      ...item,
      InDate: dayjs(item.InDate).format("YYYY-MM-DD HH:mm"),
      DiagnoseDesc: item.VisitDiagnoses?.filter(
        (k) => k.DiagnoseTypeName?.includes("诊断") && k.DiagnoseName
      )
        .map((e) => e.DiagnoseName!)
        .join(","),
    };
  });
  loading.value = false;
}
</script>

<style lang="scss" scoped></style>
