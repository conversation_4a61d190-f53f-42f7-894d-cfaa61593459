<template>
  <el-form ref="formRef" :model="info" :rules="rules" :inline="true" label-width="130px">
    <el-form-item label="裸眼视力最小值" required>
      <el-input-number v-model.number="info.SuggestRule!.LYSLZXZ" class="w-150px!" />
    </el-form-item>
    <el-form-item label="裸眼视力最大值" required>
      <el-input-number v-model="info.SuggestRule!.LYSLZDZ" class="w-150px!" />
    </el-form-item>
    <br />
    <el-form-item label="等效球镜度最小值" required>
      <el-input-number v-model.number="info.SuggestRule!.DXQJDZXZ" class="w-150px!" />
    </el-form-item>
    <el-form-item label="等效球镜度最大值" required>
      <el-input-number v-model="info.SuggestRule!.DXQJDZDZ" class="w-150px!" />
    </el-form-item>
    <br />
    <el-form-item label="结论" prop="Name">
      <el-input v-model="info.Name" class="w-150px!" />
    </el-form-item>
    <br />
    <el-form-item label="建议" prop="SuggestContent">
      <WangEditor v-model="info.SuggestContent" height="300px" />
    </el-form-item>
  </el-form>
</template>

<script setup lang="ts">
import Content_Api from "@/api/content";
import { SuggestLevelItem } from "@/api/content/types";
import { FormInstance, FormRules } from "element-plus";

const info = ref<SuggestLevelItem>({
  SuggestRule: {
    LYSLZXZ: 0,
    LYSLZDZ: 0,
    DXQJDZXZ: 0,
    DXQJDZDZ: 0,
  },
  Name: "",
  FootArchName: "",
  FootArchNormal: false,
  SuggestContent: "",
});

const handleSubmit = async (): Promise<SuggestLevelItem | null> => {
  try {
    await formRef.value!.validate();
    info.value.Type = 2; // Type:1 脊椎筛查   2 视光筛查
    const copyData = JSON.parse(JSON.stringify(info.value));
    if (copyData.Id) {
      copyData.StrId = copyData.Id;
      delete copyData.Id;
    }
    return copyData;
  } catch {
    return null;
  }
};

const validateFootArchRanges = (rule: any, value: any, callback: any) => {
  if (!value || value.length === 0) {
    return callback(new Error("请至少设置一个足弓范围"));
  }
  for (let i = 0; i < value.length; i++) {
    const errorMessage = isValidRangeItem(value[i], i);
    if (errorMessage) {
      return callback(new Error(errorMessage));
    }
  }

  if (value.length > 1) {
    const sortedRanges = value.slice().sort((a: any, b: any) => a.StartPoint - b.StartPoint);
    for (let i = 1; i < sortedRanges.length; i++) {
      if (sortedRanges[i].StartPoint < sortedRanges[i - 1].EndPoint) {
        return callback(new Error("足弓范围区间存在重叠"));
      }
    }
  }
  callback();
};
const formRef = ref<FormInstance>();
const rules = reactive<FormRules>({
  Name: [
    {
      type: "string",
      required: true,
      message: "请输入结论",
      trigger: "blur",
    },
  ],
  SuggestContent: [
    {
      type: "string",
      required: true,
      message: "请输入建议",
      trigger: "blur",
    },
  ],
});

const isValidRangeItem = (item: any, itemIndex: number) => {
  if (item.StartPoint === null || item.StartPoint === undefined) {
    return `第 ${itemIndex + 1} 个足弓范围的起始点不能为空`;
  }
  if (item.EndPoint === null || item.EndPoint === undefined) {
    return `第 ${itemIndex + 1} 个足弓范围的结束点不能为空`;
  }
  if (typeof item.StartPoint !== "number" || typeof item.EndPoint !== "number") {
    return `第 ${itemIndex + 1} 个足弓范围的起止点必须是数字`;
  }
  if (item.StartPoint > item.EndPoint) {
    return `第 ${itemIndex + 1} 个足弓范围的起始点不能大于结束点`;
  }
  return null;
};
const handleProcessingData = async (id: string) => {
  const res = await Content_Api.querySuggestLevelByIds([id]);
  if (res.Type === 200) {
    info.value = res.Data[0];
  }
};

interface Props {
  id: string;
}
const props = defineProps<Props>();

watch(
  () => props.id,
  (newVal) => {
    if (newVal) {
      handleProcessingData(newVal);
    }
  },
  { immediate: true }
);

defineExpose({
  handleSubmit,
});
</script>

<style lang="scss" scoped></style>
