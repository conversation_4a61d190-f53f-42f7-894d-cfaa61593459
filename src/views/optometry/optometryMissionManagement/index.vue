<template>
  <el-container class="w-full h-full">
    <!-- 左侧目录 -->
    <el-aside width="230px" class="p-10px">
      <EditTree
        ref="treeRef"
        :data="treeList"
        :props="defaultTreeProps"
        node-key="Id"
        :current-node-key="queryParams.levelId"
        :operations="getTreeNodeOperations"
        @add="(e) => onAddOrEditTreeNode(e, true)"
        @edit="(e) => onAddOrEditTreeNode(e, false)"
        @delete="(e) => onDeleteTreeNode(e)"
        @node-click="onTreeClick"
        @node-contextmenu="onNodeRightClick"
      />
    </el-aside>
    <el-main class="p-10px!">
      <BaseTableSearchContainer @size-changed="tableResize">
        <!-- 顶部筛选条件 -->
        <template #search>
          <TBSearchContainer>
            <template #left>
              <div v-if="queryParams.levelId" class="flex items-center">
                <div
                  v-for="item in suggestTypeList"
                  :key="item.Id"
                  :class="[
                    'px-12px py-6px mr-8px cursor-pointer border border-solid rounded-t-4px transition-all flex items-center',
                    editableTabsValue === item.Id
                      ? 'bg-blue-500 text-white border-blue-500'
                      : 'bg-gray-100 text-gray-700 border-gray-300 hover:bg-gray-200',
                  ]"
                  @click="handleTabClick(item.Id || '')"
                >
                  {{ item.Name }}
                  <el-icon
                    class="ml-8px text-gray-400 hover:text-red-500 cursor-pointer transition-colors duration-200"
                    size="14"
                    @click.stop="onDeleteSuggestType(item.Id || '', item.Name || '')"
                  >
                    x
                  </el-icon>
                </div>
                <div
                  v-if="!showAddInput"
                  class="px-12px py-6px cursor-pointer border border-solid border-gray-300 rounded-t-4px bg-gray-50 hover:bg-gray-100 transition-all"
                  @click="showAddInputBox"
                >
                  <i class="i-ep-plus mr-4px" />
                  添加
                </div>
                <div
                  v-else
                  class="px-12px py-6px border border-solid border-blue-500 rounded-t-4px bg-white"
                >
                  <el-input
                    ref="addInputRef"
                    v-model="addInputValue"
                    size="small"
                    placeholder="回车添加"
                    class="w-120px"
                    @keyup.enter="handleAddConfirm"
                    @blur="handleAddCancel"
                  />
                </div>
              </div>
              <div v-else />
            </template>
          </TBSearchContainer>
        </template>
        <template #searchTable>
          <TBSearchContainer>
            <template #left>
              <el-form :model="queryParams" label-position="right" :inline="true" @submit.prevent>
                <el-form-item>
                  <el-input
                    v-model="queryParams.keywords"
                    placeholder="标题/作者名称"
                    clearable
                    @keyup.enter="handleQuery"
                  />
                </el-form-item>
              </el-form>
            </template>
            <template #right>
              <el-button type="primary" @click="handleQuery">搜索</el-button>
              <el-button type="primary" @click="handleAddMission">添加</el-button>
            </template>
          </TBSearchContainer>
        </template>
        <!-- 列表 -->
        <template #table>
          <el-table
            :ref="kTableRef"
            v-loading="tableLoading"
            :data="pageData"
            :total="total"
            row-key="Id"
            :height="tableFluidHeight"
            :header-cell-style="{ textAlign: 'center' }"
            :cell-style="{ textAlign: 'center' }"
            border
            highlight-current-row
          >
            <el-table-column prop="Title" label="标题" align="center" />
            <el-table-column prop="Diseases" label="疾病种类" align="center" />
            <el-table-column prop="Author" label="作者" align="center" />
            <el-table-column fixed="right" label="操作" width="80" align="center">
              <template #default="scope">
                <el-button link type="danger" size="small" @click="onDeleteMission(scope.row.Id)">
                  删除
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </template>
        <!-- 分页 -->
        <template #pagination>
          <Pagination
            v-if="total > 0"
            v-model:total="total"
            v-model:page="queryParams.pageIndex"
            v-model:limit="queryParams.pageSize"
            @pagination="onGetTableList"
          />
        </template>
      </BaseTableSearchContainer>
    </el-main>
    <el-dialog
      v-model="showDataDialog.isShow"
      :title="showDataDialog.title"
      width="1200"
      destroy-on-close
    >
      <RecoveryMission ref="recoveryMissionRef" :is-preview="true" />
      <template #footer>
        <el-button @click="showTreeNodeDialog.isShow = false">取消</el-button>
        <el-button type="primary" :loading="submitLoading" @click="handleAddMissionData">
          确定
        </el-button>
      </template>
    </el-dialog>
    <el-dialog
      v-model="showTreeNodeDialog.isShow"
      :title="showTreeNodeDialog.title"
      width="700"
      destroy-on-close
      draggable
      :close-on-click-modal="false"
      :close-on-press-escape="false"
    >
      <AbnormalContent :id="showTreeNodeDialog.id" ref="abnormalContentRef" />
      <template #footer>
        <el-button @click="showTreeNodeDialog.isShow = false">取消</el-button>
        <el-button type="primary" :loading="submitLoading" @click="handleAddTreeNodeData">
          确定
        </el-button>
      </template>
    </el-dialog>
  </el-container>
</template>

<script setup lang="ts">
import { useUserStore } from "@/store";
const userStore = useUserStore();
import { useTableConfig } from "@/hooks/useTableConfig";
import Content_Api from "@/api/content";
import { SuggestInfoInputDTO, SuggestLevelItem, SuggestTypeItem } from "@/api/content/types";
import RecoveryMission from "@/views/platform-resource/recoveryMissionManagement/index.vue";
import AbnormalContent from "./components/AbnormalContent.vue";

interface PageSuggestLevelItem extends SuggestLevelItem {
  Children?: PageSuggestLevelItem[];
  ShowName?: string;
}

/** 调试开关 */
const kEnableDebug = true;
defineOptions({
  name: "OptometryMissionManagement",
});

const { kTableRef, pageData, tableLoading, tableFluidHeight, total, tableResize } =
  useTableConfig<any>();
/** 查询条件 */
const queryParams = reactive<SuggestInfoInputDTO>({
  keywords: "",
  levelId: "",
  pageIndex: 1,
  pageSize: 20,
  typeId: "",
});
/** 点击搜索 */
function handleQuery() {
  queryParams.pageIndex = 1;
  onGetTableList();
}

/** 查看/添加/编辑弹窗 */
const showDataDialog = reactive({
  isShow: false,
  title: "",
  disabled: false,
  data: {} as any, // 查看/添加/编辑详情
});

const suggestTypeList = ref<SuggestTypeItem[]>([]);
const editableTabsValue = ref<string>("");
const submitLoading = ref<boolean>(false);
const recoveryMissionRef = useTemplateRef("recoveryMissionRef");
const abnormalContentRef = useTemplateRef("abnormalContentRef");

/** 添加输入框相关状态 */
const showAddInput = ref<boolean>(false);
const addInputValue = ref<string>("");
const addInputRef = useTemplateRef("addInputRef");

/** 标签页点击事件 */
function handleTabClick(tabId: string) {
  editableTabsValue.value = tabId;
  queryParams.typeId = tabId;
  queryParams.pageIndex = 1;
  switchTabAndLoadData();
}

/** 切换标签并加载数据 */
const switchTabAndLoadData = async () => {
  // 获取数据列表，不重置当前激活的标签
  onGetTableList();
};

/** 显示添加输入框 */
function showAddInputBox() {
  showAddInput.value = true;
  addInputValue.value = "";
  nextTick(() => {
    addInputRef.value?.focus();
  });
}

const handleAddMission = () => {
  if (!queryParams.typeId) {
    ElMessage.warning("请先选择上方的分类");
    return;
  }
  showDataDialog.title = "添加";
  showDataDialog.isShow = true;
};

const onDeleteMission = (id: string) => {
  ElMessageBox.confirm("确定删除吗？", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(() => {
    Content_Api.deleteSuggestInfo({ Id: id }).then((res) => {
      if (res.Type === 200) {
        ElMessage.success("删除成功");
        onGetTableList();
      } else {
        ElMessage.error(res.Content || "删除失败");
      }
    });
  });
};

/** 删除建议类型 */
const onDeleteSuggestType = (id: string, name: string) => {
  ElMessageBox.confirm(`确定删除"${name}"类型吗？`, "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(() => {
    Content_Api.deleteSuggestType({ Id: id }).then((res) => {
      if (res.Type === 200) {
        ElMessage.success("删除成功");
        // 如果删除的是当前激活的标签页，需要重新获取类型列表并重置到第一个标签页
        if (editableTabsValue.value === id) {
          // 清空当前激活标签，让 requestTableList 重新设置默认标签
          editableTabsValue.value = "";
          requestTableList();
        } else {
          // 只刷新类型列表，不改变当前选中的标签页
          getSuggestTypeList();
        }
      } else {
        ElMessage.error(res.Content || "删除失败");
      }
    });
  });
};

/** 确认添加 */
async function handleAddConfirm() {
  const typeName = addInputValue.value?.trim();
  if (!typeName) {
    ElMessage.warning("请输入建议类型名称");
    return;
  }

  try {
    await addSuggestTypeApi(typeName);
    handleAddCancel(); // 添加成功后关闭输入框
  } catch (error) {
    // 保持输入框状态，让用户可以重试
    kEnableDebug && console.error("添加失败", error);
  }
}

/** 取消添加 */
function handleAddCancel() {
  showAddInput.value = false;
  addInputValue.value = "";
}

/** 调用添加建议类型接口 */
async function addSuggestTypeApi(typeName: string) {
  try {
    // TODO: 调用具体的API接口
    const params = {
      Name: typeName,
      LevelId: queryParams.levelId, // 使用当前选中的级别ID
    };
    kEnableDebug && console.debug("准备调用添加接口", params);
    const res = await Content_Api.insertOrUpdateSuggestType(params);
    if (res.Type === 200) {
      ElMessage.success("添加成功");
      // 重新获取数据，并设置新添加的类型为激活状态
      await getSuggestTypeList();
      // 查找新添加的类型并设置为激活
      const newType = suggestTypeList.value.find((item) => item.Name === typeName);
      if (newType && newType.Id) {
        editableTabsValue.value = newType.Id;
        queryParams.typeId = newType.Id;
        onGetTableList();
      }
    } else {
      ElMessage.error(res.Content || "添加失败");
    }
  } catch (error) {
    kEnableDebug && console.error("添加建议类型失败:", error);
    ElMessage.error("添加失败，请稍后重试");
  }
}
/** 初始化时请求列表数据（包含设置默认标签） */
const requestTableList = async () => {
  // 获取类型
  try {
    const res = await Content_Api.querySuggestType({
      pageIndex: 1,
      pageSize: 999,
      levelId: queryParams.levelId,
    });
    suggestTypeList.value = res.Data.Data;
    editableTabsValue.value = suggestTypeList.value[0].Id ?? "";
    queryParams.typeId = editableTabsValue.value;
    // 获取数据列表
    onGetTableList();
  } catch (error) {
    pageData.value = [];
    total.value = 0;
  }
};

/** 获取建议类型列表（不修改当前激活标签） */
const getSuggestTypeList = async () => {
  try {
    const res = await Content_Api.querySuggestType({
      pageIndex: 1,
      pageSize: 999,
      levelId: queryParams.levelId,
    });
    suggestTypeList.value = res.Data.Data;
    // 检查当前选中的标签页是否还存在
    const currentTypeExists = suggestTypeList.value.some(
      (item) => item.Id === editableTabsValue.value
    );
    if (!currentTypeExists && suggestTypeList.value.length > 0) {
      // 如果当前选中的标签页不存在了，切换到第一个
      editableTabsValue.value = suggestTypeList.value[0].Id ?? "";
      queryParams.typeId = editableTabsValue.value;
    }
  } catch (error) {
    kEnableDebug && console.error("获取建议类型列表失败:", error);
  }
};

const onGetTableList = async () => {
  tableLoading.value = true;
  const res = await Content_Api.querySuggestInfo(queryParams);
  if (res.Type === 200) {
    pageData.value = res.Data.Data;
    total.value = res.Data.Total;
  }
  tableLoading.value = false;
};

/** 编辑/添加左侧树节点弹窗 */
const showTreeNodeDialog = reactive({
  isShow: false,
  title: "",
  id: "",
});

/** 左侧树列表数据结构 */
const treeList = ref<any[]>([]);
const defaultTreeProps = reactive({
  children: "Children",
  label: "ShowName",
});

/** 树点击事件 */
async function onTreeClick(data: PageSuggestLevelItem) {
  if (!data.Id) {
    return;
  }
  queryParams.levelId = data.Id;
  queryParams.pageIndex = 1;
  editableTabsValue.value = "";
  queryParams.typeId = "";
  requestTableList();
}

/** 树右键点击事件 */
function onNodeRightClick(data: PageSuggestLevelItem) {}

/** 获取树节点右键操作 */
function getTreeNodeOperations(data: PageSuggestLevelItem) {
  if (data.ShowName === "全部") {
    return {
      add: true,
    };
  }

  return {
    edit: true,
    delete: true,
  };
}

/** 点击删除左侧树数据 */
async function onDeleteTreeNode(data: SuggestLevelItem) {
  ElMessageBox.confirm("确定删除吗？", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(() => {
    Content_Api.deleteSuggestLevel({ id: data.Id! }).then((res) => {
      if (res.Type === 200) {
        ElMessage.success("删除成功");
        requestTreeData();
      } else {
        ElMessage.error(res.Content || "删除失败");
      }
    });
  });
}

/** 点击添加/编辑左侧树数据 */
function onAddOrEditTreeNode(data: SuggestLevelItem, add: boolean) {
  console.log("点击添加/编辑左侧树数据11", add, data);
  kEnableDebug && console.debug("点击添加/编辑左侧树数据", add, data);
  if (add) {
    showTreeNodeDialog.id = "";
    showTreeNodeDialog.title = "添加类别";
    showTreeNodeDialog.isShow = true;
    return;
  }
  console.log("点击添加/编辑左侧树数据22", add, data);
  showTreeNodeDialog.id = data.Id!;
  showTreeNodeDialog.title = "编辑类别";
  showTreeNodeDialog.isShow = true;
}

/** 请求左侧树列表数据 */
const requestTreeData = async () => {
  try {
    const res = await Content_Api.querySuggestLevel({
      type: 2,
      pageIndex: 1,
      pageSize: 1000,
    });

    if (res.Type === 200 && res.Data?.Data) {
      const treeData: PageSuggestLevelItem[] = [
        {
          ShowName: "全部",
          Id: "",
          Children: [],
        },
      ];

      res.Data.Data.forEach((item: PageSuggestLevelItem) => {
        item.ShowName = item.Name;

        item.Children = [];

        // 添加到根节点的子节点列表
        treeData[0].Children?.push(item);
      });

      treeList.value = treeData;
      queryParams.levelId = treeData[0].Children?.[0].Id ?? "";
      requestTableList();
    } else {
      ElMessage.error("获取建议级别数据失败");
      treeList.value = [];
    }
  } catch (error) {
    ElMessage.error("请求数据失败，请稍后重试");
    treeList.value = [];
  }
};

const handleAddTreeNodeData = async () => {
  const data = await abnormalContentRef.value?.handleSubmit();
  if (!data) {
    return;
  }
  submitLoading.value = true;
  Content_Api.insertOrUpdateSuggestLevel(data)
    .then((res) => {
      if (res.Type === 200) {
        ElMessage.success(res.Content);
        requestTreeData();
        showTreeNodeDialog.isShow = false;
      } else {
        ElMessage.error(res.Content || "添加失败");
      }
    })
    .finally(() => {
      submitLoading.value = false;
    });
};

const handleAddMissionData = () => {
  const data = recoveryMissionRef.value?.handleSubmit();
  if (!data?.length) {
    return;
  }
  const params = {
    LevelId: queryParams.levelId,
    TypeId: queryParams.typeId,
    RecoveryMissionIds: data,
    CreatorId: userStore.userInfo.Id,
    CreatorName: userStore.userInfo.Name,
  };
  submitLoading.value = true;
  Content_Api.insertSuggestInfo(params)
    .then((res) => {
      if (res.Type === 200) {
        ElMessage.success("添加成功");
        recoveryMissionRef.value?.clearSelection();
        showDataDialog.isShow = false;
        onGetTableList();
      } else {
        ElMessage.error(res.Content || "添加失败");
      }
    })
    .finally(() => {
      submitLoading.value = false;
    });
};

onActivated(() => {
  requestTreeData();
});
</script>

<style lang="scss" scoped></style>
