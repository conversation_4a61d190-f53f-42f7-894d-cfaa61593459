<template>
  <el-container class="w-full h-full">
    <el-aside width="230px" class="p-10px">
      <!-- <el-tree
        class="w-full h-full p-10px overflow-auto"
        :data="leftTreeData"
        :loading="treeLoading"
        :props="defaultProps"
        default-expand-all
        highlight-current
        @node-click="handleNodeClick"
        @node-contextmenu="handleNodeRightClick"
      /> -->
      <EditTree
        ref="treeRef"
        :data="leftTreeData"
        :props="defaultProps"
        node-key="Id"
        :current-node-key="queryParams.OrganizationId"
        :operations="getTreeNodeOperations"
        default-expand-all
        @add="(e) => onAddOrEditTreeNode(e, true)"
        @edit="(e) => onAddOrEditTreeNode(e, false)"
        @delete="(e) => onDeleteTreeNode(e)"
        @node-click="handleNodeClick"
        @node-contextmenu="handleNodeRightClick"
      />
    </el-aside>
    <el-main class="p-10px">
      <BaseTableSearchContainer @size-changed="tableResize">
        <template #search>
          <TBSearchContainer>
            <template #left><div /></template>
            <template #right>
              <el-button type="primary" @click="handlePreviewOrEdit(null, false)">添加</el-button>
            </template>
          </TBSearchContainer>
        </template>
        <template #table>
          <el-table
            ref="tableRef"
            v-loading="tableLoading"
            :data="pageData"
            :total="total"
            border
            row-key="Id"
            :height="tableFluidHeight"
            highlight-current-row
            style="flex: 1; text-align: center"
          >
            <el-table-column label="科室" align="center">
              <template #default="scope">
                {{ scope.row.Doctor.DepartmentName }}
              </template>
            </el-table-column>
            <el-table-column label="姓名" align="center">
              <template #default="scope">
                {{ scope.row.Doctor.Name }}
              </template>
            </el-table-column>
            <el-table-column label="手机号" align="center">
              <template #default="scope">
                {{ scope.row.Doctor.PhoneNumber }}
              </template>
            </el-table-column>
            <el-table-column label="职称" align="center">
              <template #default="scope">
                {{ scope.row.Doctor.WorkerTitle }}
              </template>
            </el-table-column>
            <el-table-column label="擅长" align="center" show-overflow-tooltip>
              <template #default="scope">
                {{ scope.row.Doctor.Skilled }}
              </template>
            </el-table-column>
            <el-table-column label="是否开启服务" align="center">
              <template #default="scope">
                {{ scope.row.IsEnable ? "是" : "否" }}
              </template>
            </el-table-column>
            <el-table-column fixed="right" label="操作" width="100" align="center">
              <template #default="scope">
                <el-button
                  link
                  type="danger"
                  size="small"
                  @click="handleDelete(scope.row.ConsortiumUserId)"
                >
                  删除
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </template>
        <template #pagination>
          <Pagination
            v-if="total > 0"
            v-model:total="total"
            v-model:page="queryParams.PageIndex"
            v-model:limit="queryParams.PageSize"
            @pagination="handleGetTableList"
          />
        </template>
      </BaseTableSearchContainer>
    </el-main>
    <el-dialog
      v-model="showDialog.user"
      title="添加"
      width="700px"
      destroy-on-close
      :close-on-click-modal="false"
      :close-on-press-escape="false"
    >
      <MemberUserContent ref="memberUserContentRef" :org-id="queryParams.OrganizationId" />
      <template #footer>
        <el-button @click="showDialog.user = false">取消</el-button>
        <el-button type="primary" :loading="submitLoading" @click="handleSubmit">确定</el-button>
      </template>
    </el-dialog>
    <el-dialog
      v-model="showTreeNodeDialog.isShow"
      :title="showTreeNodeDialog.title"
      width="700px"
      destroy-on-close
      :close-on-click-modal="false"
      :close-on-press-escape="false"
    >
      <ConsortiumContent
        ref="consortiumContentRef"
        :detail-info="showTreeNodeDialog.data"
        :consortium-id="consortiumId"
      />
      <template #footer>
        <el-button @click="showTreeNodeDialog.isShow = false">取消</el-button>
        <el-button type="primary" :loading="submitLoading" @click="handleConsortiumSubmit">
          确定
        </el-button>
      </template>
    </el-dialog>
  </el-container>
</template>

<script setup lang="ts">
import { useTableConfig } from "@/hooks/useTableConfig";
import Passport_Api from "@/api/passport";
import { OrganizationConsortium, SetConsortiumUserInputDTO } from "@/api/passport/types";
import Consult_Api from "@/api/consult";
import { ConsortiumUserInputDTO, DoctorInfo } from "@/api/consult/types";
import MemberUserContent from "./components/MemberUserContent.vue";
import ConsortiumContent from "./components/ConsortiumContent.vue";

interface PageOrganizationConsortium extends OrganizationConsortium {
  Children?: PageOrganizationConsortium[];
}

interface PageDialogShow {
  user: boolean;
}
const leftTreeData = ref<OrganizationConsortium[]>([]);
const defaultProps = {
  children: "Children",
  label: "OrganizationName",
};
const showDialog = ref<PageDialogShow>({
  user: false,
});

defineOptions({
  name: "OptometryMemberManagement",
});

const queryParams = ref<ConsortiumUserInputDTO>({
  ConsortiumId: "",
  PageIndex: 1,
  PageSize: 20,
});
let orgConsortiumId = "";
const consortiumId = ref<string>("");

const isPreview = ref<boolean>(false);
provide("isPreview", isPreview);

const { tableLoading, pageData, total, tableRef, tableFluidHeight, tableResize } =
  useTableConfig<DoctorInfo>();

const memberUserContentRef = useTemplateRef("memberUserContentRef");
const consortiumContentRef = useTemplateRef("consortiumContentRef");
const submitLoading = ref<boolean>(false);
/** 编辑/添加左侧树节点弹窗 */
const showTreeNodeDialog = reactive({
  isShow: false,
  title: "",
  data: {} as OrganizationConsortium,
});
const handlePreviewOrEdit = async (row: DoctorInfo | null, isPreviewState: boolean) => {
  if (!row && !queryParams.value.OrganizationId) {
    ElMessage.warning("请在左侧选择医院");
    return;
  }
  isPreview.value = row ? isPreviewState : false;
  showDialog.value.user = true;
};
const handleNodeClick = (row: PageOrganizationConsortium) => {
  queryParams.value.OrganizationId = row.OrganizationId || null;
  orgConsortiumId = row.Id || "";
  handleGetTableList();
};
const handleNodeRightClick = () => {};

const handleGetTableList = async () => {
  tableLoading.value = true;
  const res = await Consult_Api.getConsortiumUserList(queryParams.value);
  if (res.Type === 200) {
    pageData.value = res.Data.Data;
    total.value = res.Data.TotalCount;
  }
  tableLoading.value = false;
};

/** 获取树节点右键操作 */
function getTreeNodeOperations(data: PageOrganizationConsortium) {
  if (data.OrganizationName === "全部") {
    return {
      add: true,
    };
  }

  return {
    edit: true,
    delete: true,
  };
}
/** 点击添加/编辑左侧树数据 */
function onAddOrEditTreeNode(data: OrganizationConsortium, add: boolean) {
  if (add) {
    showTreeNodeDialog.data = {};
    showTreeNodeDialog.title = "添加医联体";
    showTreeNodeDialog.isShow = true;
    return;
  }

  showTreeNodeDialog.data = data;
  showTreeNodeDialog.title = "编辑医联体";
  showTreeNodeDialog.isShow = true;
}
/** 点击删除左侧树数据 */
async function onDeleteTreeNode(data: OrganizationConsortium) {
  ElMessageBox.confirm("确定删除该医联体成员吗？", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(async () => {
    Passport_Api.deleteOrganizationConsortium([data.Id!]).then((res) => {
      if (res.Type === 200) {
        ElMessage.success("删除成功");
        handleGetLeftTreeData();
        queryParams.value.OrganizationId = null;
        handleGetTableList();
      } else {
        ElMessage.error(res.Content);
      }
    });
  });
}

const handleSubmit = () => {
  const data = memberUserContentRef.value?.handleSubmit();
  if (!data?.length) {
    return;
  }
  let params: SetConsortiumUserInputDTO[] = [];
  data.forEach((item) => {
    params.push({
      UserId: item,
      ConsortiumId: consortiumId.value,
      OrgId: queryParams.value.OrganizationId,
      OrganizationConsortiumId: orgConsortiumId,
    });
  });
  Passport_Api.setConsortiumUsers(params)
    .then((res) => {
      if (res.Type === 200) {
        ElMessage.success("添加成功");
        showDialog.value.user = false;
        memberUserContentRef.value?.clearSelectedTable();
        handleGetTableList();
      } else {
        ElMessage.error(res.Content);
      }
    })
    .finally(() => {
      submitLoading.value = false;
    });
};

const handleConsortiumSubmit = async () => {
  const data = await consortiumContentRef.value?.handleSubmit();
  if (!data) {
    return;
  }
  submitLoading.value = true;
  Passport_Api.addOrganizationConsortiums([data])
    .then((res) => {
      if (res.Type === 200) {
        ElMessage.success(res.Content!);
        handleGetLeftTreeData();
        showTreeNodeDialog.isShow = false;
      } else {
        ElMessage.error(res.Content);
      }
    })
    .finally(() => {
      submitLoading.value = false;
    });
};

const handleDelete = (id: string) => {
  ElMessageBox.confirm("确定删除该成员吗？", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(async () => {
    const res = await Passport_Api.deleteConsortiumUsers([id]);
    if (res.Type === 200) {
      ElMessage.success("删除成功");
      handleGetTableList();
    } else {
      ElMessage.error(res.Content);
    }
  });
};
const handleGetLeftTreeData = async () => {
  const resData = await Passport_Api.getConsortiumList({
    Type: 2,
  });
  consortiumId.value = resData.Data[0].Id!;
  queryParams.value.ConsortiumId = consortiumId.value;
  const res = await Passport_Api.getOrganizationConsortiumList({
    PageIndex: 1,
    PageSize: 1000,
    ConsortiumId: resData.Data[0].Id,
  });
  if (res.Type === 200) {
    const treeData: PageOrganizationConsortium[] = [
      {
        OrganizationName: "全部",
        Id: "",
        Children: [],
      },
    ];
    res.Data.Data.forEach((item: PageOrganizationConsortium) => {
      var Children: PageOrganizationConsortium[] = [];
      res.Data.Data.forEach((item1) => {
        if (item1.ConsortiumId === item.Id) {
          if (item.Children === undefined) {
            Children.push(item1);
          }
        }
      });
      item.Children = Children;
      treeData[0].Children?.push(item);
    });
    leftTreeData.value = treeData;
  }
};

onActivated(async () => {
  await handleGetLeftTreeData();
  handleGetTableList();
});
</script>
<style scoped lang="scss"></style>
