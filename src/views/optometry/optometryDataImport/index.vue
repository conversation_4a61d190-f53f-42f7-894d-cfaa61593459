<template>
  <div v-loading="loadingPage" style="padding: 12px; overflow: hidden; overflow-x: auto">
    <el-steps direction="vertical" :active="2" space="150">
      <el-step title="步骤 1">
        <template #description>
          <p style="color: black">按模板所示修改好csv文件待传</p>
          <el-button type="primary" @click="downloadFile">下载模板</el-button>
        </template>
      </el-step>
      <el-step title="步骤 2">
        <template #description style="max-height: 500px">
          <p style="color: black">上传.csv文件结尾的文件</p>
          <el-upload
            ref="upload"
            class="upload-demo"
            :before-upload="handleBeforeUpload"
            :http-request="handleFileUpload"
            :show-file-list="true"
            :on-change="handleUploadChange"
            :on-exceed="handleExceed"
            :on-remove="handleRemove"
            accept=".csv"
            :auto-upload="false"
            :limit="1"
          >
            <el-button type="primary">点击上传CSV文件</el-button>
            <template #tip>
              <div class="el-upload__tip">只能上传CSV文件，且不超过10MB</div>
            </template>
          </el-upload>
          <span v-if="tableData.length > 0">预览默认只会显示前面3条数据</span>
          <br />
          <el-button
            v-if="tableData.length"
            :loading="loadingReport"
            type="primary"
            @click="handleFileUploadClick"
          >
            确定上传
          </el-button>
          <el-table
            v-if="tableData.length > 0"
            v-loading="false"
            :data="tableData"
            element-loading-text="Loading"
            border
            fit
            :header-cell-style="{ 'text-align': 'center' }"
            highlight-current-row
          >
            <el-table-column
              v-for="(title, index) in titleData"
              :key="index"
              :label="title"
              align="center"
            >
              <template #default="scope">
                {{ scope.row["index" + index] }}
              </template>
            </el-table-column>
          </el-table>
        </template>
      </el-step>
    </el-steps>
  </div>
</template>

<script setup lang="ts">
import * as Papa from "papaparse";
import { ref } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import type {
  UploadRawFile,
  UploadRequestOptions,
  UploadFile,
  UploadFiles,
  UploadUserFile,
} from "element-plus";
import FileAPI from "@/api/file";
import Supplier_Qingpai_Api from "@/api/supplier-qingpai";

/** 组件选项定义 */
defineOptions({
  name: "OptometryDataImport",
});

/** 接口定义 */
interface TableRowData {
  [key: string]: string;
}

interface FileReportResponse {
  Data: {
    TotalCount: number;
    SuccessCount: number;
    FailCount: number;
    ErrorDetail?: Record<string, string>;
  };
}

/** Papa.parse 结果类型定义 */
interface PapaParseResult {
  data: string[][];
  errors: any[];
  meta: any;
}

/** 响应式数据 */
const isUpload = ref<boolean>(false);
const tableData = ref<TableRowData[]>([]);
const titleData = ref<string[]>([]);
const loadingPage = ref<boolean>(false);
const loadingReport = ref<boolean>(false);
const upload = useTemplateRef("upload");

/**
 * 下载CSV模板文件
 */
function downloadFile(): void {
  const fileUrl = "/mb.csv"; // 替换为实际的文件路径
  const link = document.createElement("a");
  link.href = fileUrl;
  link.download = "数据模板.csv"; // 自定义下载文件名称
  link.click();
}

/**
 * 文件上传前校验
 * @param file - 待上传的文件
 */
function handleBeforeUpload(file: UploadRawFile): boolean {
  // 校验文件类型
  const isCSV = file.type === "text/csv" || file.name.toLowerCase().endsWith(".csv");
  if (!isCSV) {
    ElMessage.error("只能上传CSV格式的文件!");
    return false;
  }

  // 校验文件大小 (10MB)
  const isLt10M = file.size / 1024 / 1024 < 10;
  if (!isLt10M) {
    ElMessage.error("上传文件大小不能超过 10MB!");
    return false;
  }

  return true;
}

function handleFileUploadClick(): void {
  upload.value?.submit();
}

/**
 * 自定义文件上传处理
 * @param options - 上传选项
 */
async function handleFileUpload(options: UploadRequestOptions): Promise<void> {
  const file = options.file;
  try {
    // 首先解析CSV文件内容用于预览
    // 然后上传文件到服务器
    const formData = new FormData();
    formData.append("file", file);
    loadingReport.value = true;
    const response = await FileAPI.upload(formData);
    if (response.Type === 200) {
      const fileUrl = response.Data.HostSetting.External + response.Data.PathSetting.Path;
      await uploadSuccess(fileUrl);
    } else {
      throw new Error("文件上传失败");
    }
  } catch (error: any) {
    ElMessage.error("文件处理失败: " + error.message);
  } finally {
    loadingReport.value = false;
  }
}

/**
 * 处理文件上传变更事件 - 解析CSV文件内容用于预览
 * @param uploadFile - Element Plus 上传文件对象
 * @param uploadFiles - 上传文件列表
 */
function handleUploadChange(uploadFile: UploadFile, uploadFiles: UploadFiles): void {
  // 获取原始文件对象
  const file = uploadFile.raw;
  if (!file) {
    ElMessage.error("无法获取文件内容");
    return;
  }

  Papa.parse(file, {
    encoding: "gb2312",
    complete(results: PapaParseResult) {
      console.log("results", results); // 这个是csv文件的数据
      const data: string[][] = [];
      // 遍历csv文件中的数据，存放到data中 方法不唯一，可自己更改
      const forEachCount = results.data.length > 4 ? 4 : results.data.length;
      for (let i = 0; i < forEachCount; i++) {
        data.push(results.data[i] as string[]);
      }
      const copyData: string[][] = JSON.parse(JSON.stringify(data));
      // 获取表格表头数据
      titleData.value = copyData[0] || [];
      copyData.shift();
      // 获取具体数据
      const showTableData: TableRowData[] = [];
      copyData.forEach((row: string[], index: number) => {
        const obj: TableRowData = {};
        row &&
          row.forEach((cell: string, cellIndex: number) => {
            obj["index" + cellIndex] = cell;
          });
        showTableData[index] = obj;
      });
      console.log("showTableData", showTableData);
      tableData.value = showTableData;
    },
  });
}

/**
 * 设置上传图片状态
 * @param state - 上传状态
 */
function SetUploadState(state: boolean): void {
  isUpload.value = state;
}

/**
 * 文件上传成功处理 - 调用后端API解析文件
 * @param fileUrl - 上传成功的文件URL
 */
async function uploadSuccess(fileUrl: string): Promise<void> {
  console.log("上传的文件URL:", fileUrl);

  ElMessage.success("上传成功，等待解析文件");
  loadingPage.value = true;

  try {
    // TODO: 替换为实际的FileReport API调用
    // 这里需要根据实际的API接口进行调整
    Supplier_Qingpai_Api.fileReport({ Url: fileUrl }).then((res) => {
      let ErrorDetail = "";
      if (res.Data.ErrorDetail) {
        for (const key in res.Data.ErrorDetail) {
          ErrorDetail += key + res.Data.ErrorDetail[key];
        }
      }
      const obj = res.Data;
      ElMessageBox.alert(
        `识别条数${obj.TotalCount}条,成功新增${obj.SuccessCount}条,失败${
          obj.FailCount
        }条,失败理由：${ErrorDetail || "无"}`,
        "系统提示",
        {
          confirmButtonText: "确定",
          type: "warning",
        }
      );
      tableData.value = [];
      // 清空el-upload组件的文件列表
      upload.value?.clearFiles();
    });
  } catch (error: any) {
    ElMessage.error("操作失败: " + error.message);
  } finally {
    loadingPage.value = false;
  }
}

/**
 * 模拟文件报告API - 需要替换为实际的API调用
 * 原始代码中的 FileReport({ Url }) 需要根据实际API进行实现
 * @param url - 文件URL
 * @returns Promise<FileReportResponse>
 */
async function mockFileReport(url: string): Promise<FileReportResponse> {
  // TODO: 替换为实际的API调用
  // 例如：return request.post('/api/file/report', { Url: url });
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve({
        Data: {
          TotalCount: 100,
          SuccessCount: 95,
          FailCount: 5,
          ErrorDetail: {
            "第6行: ": "数据格式错误",
            "第12行: ": "必填字段缺失",
          },
        },
      });
    }, 2000);
  });
}

const handleExceed = (files: File[], fileList: UploadUserFile[]) => {
  if (fileList.length > 0) {
    ElMessage.error("最多上传一个，请删除之后出现添加");
  }
};

const handleRemove = (file: UploadUserFile, fileList: UploadUserFile[]) => {
  tableData.value = [];
};
</script>

<style lang="scss" scoped>
:deep(.el-step__description) {
  padding-right: 0 !important;
}

:deep(.el-upload-list__item) {
  width: 200px !important;
}

.upload-demo {
  margin: 10px 0;
}
</style>
