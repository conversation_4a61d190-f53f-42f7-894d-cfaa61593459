<template>
  <div class="app-container">
    <BaseTableSearchContainer @size-changed="tableResize">
      <template #search>
        <TBSearchContainer :is-show-toggle="true">
          <template #left>
            <el-form :model="queryParams" label-position="right" :inline="true">
              <el-form-item label="时间">
                <el-date-picker
                  v-model="timeRange"
                  unlink-panels
                  type="daterange"
                  range-separator="至"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                  format="YYYY-MM-DD"
                  :clearable="false"
                  value-format="YYYY-MM-DD"
                  style="width: 250px"
                  :shortcuts="datePickerShortcuts"
                />
              </el-form-item>
              <el-form-item label="学校类型">
                <el-select
                  v-model="queryParams.SchoolCategory"
                  class="w100"
                  placeholder="请选择"
                  :empty-values="['', undefined, null]"
                  :value-on-clear="() => null"
                  @change="onSchoolCategory"
                >
                  <el-option
                    v-for="(item, index) in topSelectOptions.schoolCategoryList"
                    :key="index"
                    :label="item.SchoolCategory"
                    :value="item.SchoolCategory!"
                  />
                </el-select>
              </el-form-item>
              <el-form-item label="学校">
                <el-select
                  v-model="queryParams.SchoolName"
                  class="w100"
                  placeholder="请选择"
                  :empty-values="['', undefined, null]"
                  :value-on-clear="() => null"
                  :disabled="!queryParams.SchoolCategory"
                  @change="onSchoolName"
                >
                  <el-option
                    v-for="(item, index) in topSelectOptions.schoolNameList"
                    :key="index"
                    :label="item.SchoolCategory"
                    :value="item.SchoolCategory!"
                  />
                </el-select>
              </el-form-item>
              <el-form-item label="年级">
                <el-select
                  v-model="queryParams.Grade"
                  class="w100"
                  placeholder="请选择"
                  :disabled="!queryParams.SchoolName"
                  :empty-values="['', undefined, null]"
                  :value-on-clear="() => null"
                >
                  <el-option
                    v-for="(item, index) in topSelectOptions.gradeList"
                    :key="index"
                    :label="item.SchoolCategory"
                    :value="item.SchoolCategory!"
                  />
                </el-select>
              </el-form-item>
            </el-form>
          </template>
          <template #right>
            <el-button type="primary" icon="search" @click="handleQuery">搜索</el-button>
          </template>
        </TBSearchContainer>
      </template>
      <template #table>
        <el-table
          ref="tableRef"
          v-loading="tableLoading"
          :data="pageData"
          border
          :height="tableFluidHeight"
          highlight-current-row
          style="flex: 1; text-align: center"
          show-summary
          :summary-method="getSummaries"
        >
          <el-table-column label="名称" width="150" align="center">
            <template #default="scope">
              {{ onGetShowName(scope.row) }}
            </template>
          </el-table-column>
          <el-table-column prop="ReportCount" label="筛查报告数量" align="center" />
          <el-table-column prop="CheckedCount" label="已查看人数" align="center" />
          <el-table-column prop="UnCheckedCount" label="未查看人数" align="center" />
          <el-table-column prop="CheckedRate" label="查询率" align="center">
            <template #default="scope">
              {{ calculateAmount([scope.row.CheckedRate, 100], "*").toFixed(2) + "%" }}
            </template>
          </el-table-column>
        </el-table>
      </template>
    </BaseTableSearchContainer>
  </div>
</template>

<script setup lang="ts">
import dayjs from "dayjs";
import { useTableConfig } from "@/hooks/useTableConfig";
import { useDateRangePicker } from "@/hooks/useDateRangePicker";
import {
  CheckedReportInputDTO,
  CheckedReportItem,
  SchoolInfoInputDTO,
  SchoolInfoItem,
} from "@/api/supplier-qingpai/types";
import Supplier_Qingpai_Api from "@/api/supplier-qingpai";
import { calculateAmount } from "@/utils";
const { datePickerShortcuts } = useDateRangePicker();

defineOptions({
  name: "OptometryReportQuery",
});

interface PageSelectOption {
  schoolCategoryList: SchoolInfoItem[];
  schoolNameList: SchoolInfoItem[];
  gradeList: SchoolInfoItem[];
}

const queryParams = ref<CheckedReportInputDTO>({
  BeginTime: dayjs().format("YYYY-MM-01 00:00:00"),
  EndTime: dayjs().format("YYYY-MM-DD 23:59:59"),
  SchoolName: null,
  SchoolCategory: null,
  Grade: null,
});

const topQuery = ref<SchoolInfoInputDTO>({});

const topSelectOptions = ref<PageSelectOption>({
  schoolCategoryList: [],
  schoolNameList: [],
  gradeList: [],
});

const timeRange = ref<[string, string]>([
  dayjs().format("YYYY-MM-01"),
  dayjs().format("YYYY-MM-DD"),
]);

const { tableLoading, pageData, tableRef, tableFluidHeight, tableResize } =
  useTableConfig<CheckedReportItem>();

const handleQuery = () => {
  handleGetTableList();
};

const onGetShowName = (row: CheckedReportItem): string | undefined => {
  const copyData = JSON.parse(JSON.stringify(queryParams.value));
  if (copyData.Grade) {
    return row.Class;
  } else if (copyData.SchoolName) {
    return row.Grade;
  } else if (copyData.SchoolCategory) {
    return row.SchoolName;
  } else if (!copyData.SchoolCategory) {
    return row.SchoolCategory;
  } else {
    return undefined;
  }
};
const handleGetTableList = async () => {
  tableLoading.value = true;
  const res = await Supplier_Qingpai_Api.getCheckedReport(queryParams.value);
  if (res.Type === 200) {
    pageData.value = res.Data;
  }
  tableLoading.value = false;
};

/**
 * 表格汇总方法
 * @param param 包含列配置和数据的参数对象
 * @returns 汇总行数据数组
 */
const getSummaries = (param: { columns: any[]; data: CheckedReportItem[] }): string[] => {
  const { columns, data } = param;
  const sums: string[] = [];

  columns.forEach((column, index) => {
    if (index === 0) {
      sums[index] = "合计";
      return;
    }

    // 获取当前列的所有数值
    const values = data.map(
      (item) => Number(item[column.property as keyof CheckedReportItem]) || 0
    );

    // 计算总和
    const sum = values.reduce((prev, current) => prev + current, 0);

    // 特殊处理查询率列（第4列，索引为4）
    if (index === 4) {
      // 查询率 = 已查看人数 / 筛查报告数量
      const checkedCount = Number(sums[2]) || 0; // 已查看人数
      const reportCount = Number(sums[1]) || 0; // 筛查报告数量

      if (reportCount > 0) {
        sums[index] = ((checkedCount / reportCount) * 100).toFixed(2) + "%";
      } else {
        sums[index] = "0.00%";
      }
      return;
    }

    sums[index] = sum.toString();
  });

  return sums;
};

const handleGetList = async (type: string) => {
  const res = await Supplier_Qingpai_Api.getSchoolInfo(topQuery.value);
  if (res.Type === 200) {
    topSelectOptions.value[type as keyof PageSelectOption] = res.Data;
  }
};

const onSchoolCategory = (e: string) => {
  queryParams.value.SchoolName = null;
  queryParams.value.Grade = null;
  if (e) {
    topQuery.value.SchoolCategory = e;
    handleGetList("SchoolNameList");
  }
};

const onSchoolName = (e: string) => {
  queryParams.value.Grade = null;
  if (e) {
    topQuery.value.SchoolName = e;
    handleGetList("GradeList");
  }
};

watch(timeRange, (newVal) => {
  queryParams.value.BeginTime = dayjs(newVal[0]).format("YYYY-MM-DD 00:00:00");
  queryParams.value.EndTime = dayjs(newVal[1]).format("YYYY-MM-DD 23:59:59");
});

onBeforeMount(() => {
  // 获取数据
  handleGetList("schoolCategoryList");
});

onActivated(() => {
  handleGetTableList();
});
</script>

<style lang="scss" scoped></style>
