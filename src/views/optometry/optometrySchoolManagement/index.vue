<template>
  <div class="app-container">
    <BaseTableSearchContainer @size-changed="tableResize">
      <template #search>
        <TBSearchContainer>
          <template #left>
            <el-form :model="queryParams" label-position="right" :inline="true">
              <el-form-item label="是否启用">
                <el-select
                  v-model="queryParams.IsEnable"
                  placeholder="请选择"
                  clearable
                  :empty-values="[null, undefined, '']"
                  :value-on-clear="() => null"
                  style="width: 120px"
                >
                  <el-option label="是" :value="true" />
                  <el-option label="否" :value="false" />
                </el-select>
              </el-form-item>
              <el-form-item label="关键字" prop="Keyword">
                <el-input
                  v-model="queryParams.Keyword"
                  placeholder="学校名称/编码/拼音码"
                  style="width: 200px"
                  @keyup.enter="handleQuery"
                />
              </el-form-item>
            </el-form>
          </template>
          <template #right>
            <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
            <el-button type="primary" @click="handleAdd">新增</el-button>
          </template>
        </TBSearchContainer>
      </template>
      <template #table>
        <el-table
          ref="tableRef"
          v-loading="tableLoading"
          :data="pageData"
          :total="total"
          border
          row-key="Id"
          :height="tableFluidHeight"
          highlight-current-row
          style="flex: 1; text-align: center"
        >
          <el-table-column prop="Name" label="学校名称" align="center" />
          <el-table-column prop="Code" label="学校编码" align="center" />
          <el-table-column prop="PYM" label="拼音码" align="center" />
          <el-table-column prop="IsEnable" label="是否启用" align="center">
            <template #default="scope">
              <el-switch v-model="scope.row.IsEnable" @change="handleChangeIsEnable(scope.row)" />
            </template>
          </el-table-column>
          <el-table-column fixed="right" label="操作" align="center">
            <template #default="scope">
              <el-button link type="primary" @click="handlePreviewOrEdit(scope.row, true)">
                查看
              </el-button>
              <el-button link type="primary" @click="handlePreviewOrEdit(scope.row, false)">
                编辑
              </el-button>
              <el-button link type="primary" @click="handleDelete(scope.row)">删除</el-button>
            </template>
          </el-table-column>
        </el-table>
      </template>
      <template #pagination>
        <Pagination
          v-if="total > 0"
          v-model:total="total"
          v-model:page="queryParams.PageIndex"
          v-model:limit="queryParams.PageSize"
          @pagination="handleGetTableList"
        />
      </template>
    </BaseTableSearchContainer>
    <el-dialog
      v-model="showDialog"
      :title="dialogTitle"
      width="600"
      destroy-on-close
      :close-on-press-escape="isPreview"
      :close-on-click-modal="isPreview"
    >
      <SchoolContent ref="schoolContentRef" :detail-info="currentItem" />
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="showDialog = false">取消</el-button>
          <el-button
            v-if="!isPreview"
            type="primary"
            :loading="dialogConfirmLoading"
            @click="handleSubmit"
          >
            确定
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { useTableConfig } from "@/hooks/useTableConfig";
import Appointment_Api from "@/api/appointment";
import { type SchoolItem, type SchoolQueryInputDTO } from "@/api/appointment/types";
import SchoolContent from "./components/SchoolContent.vue";

defineOptions({
  name: "OptometrySchoolManagement",
});

const queryParams = ref<SchoolQueryInputDTO>({
  IsEnable: null,
  Keyword: "",
  PageIndex: 1,
  PageSize: 20,
});

const isPreview = ref<boolean>(false);
provide("isPreview", isPreview);

const showDialog = ref<boolean>(false);
const dialogTitle = ref<string>("");
const currentItem = ref<SchoolItem | null>(null);
const schoolContentRef = useTemplateRef("schoolContentRef");

const {
  tableLoading,
  pageData,
  total,
  tableRef,
  tableDateFormat,
  tableFluidHeight,
  tableResize,
  dialogConfirmLoading,
} = useTableConfig<SchoolItem>();

const handleQuery = () => {
  queryParams.value.PageIndex = 1;
  handleGetTableList();
};

const handleAdd = () => {
  isPreview.value = false;
  dialogTitle.value = "新增学校";
  currentItem.value = null;
  showDialog.value = true;
};

const handlePreviewOrEdit = async (row: SchoolItem, isPreviewState: boolean) => {
  isPreview.value = isPreviewState;
  dialogTitle.value = isPreviewState ? "查看学校" : "编辑学校";
  currentItem.value = row;
  showDialog.value = true;
};

const handleChangeIsEnable = async (row: SchoolItem) => {
  const updatedSchool: SchoolItem = {
    ...row,
    IsEnable: row.IsEnable,
  };

  const res = await Appointment_Api.saveSchool(updatedSchool);
  if (res.Type === 200) {
    ElNotification({
      title: "成功",
      message: res.Content,
      type: "success",
    });
  } else {
    ElMessage.error(res.Content);
    // 失败时刷新数据以恢复开关状态
    handleGetTableList();
  }
};

const handleDelete = async (row: SchoolItem) => {
  ElMessageBox.confirm("确定删除这个学校吗？", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(async () => {
    const res = await Appointment_Api.deleteSchool({ Id: row.Id! });
    if (res.Type === 200) {
      ElNotification({
        title: "成功",
        message: "删除成功",
        type: "success",
      });
      handleGetTableList();
    } else {
      ElMessage.error(res.Content);
    }
  });
};

const handleSubmit = async () => {
  const params = await schoolContentRef.value?.handleSubmit();
  if (!params) return;

  dialogConfirmLoading.value = true;
  try {
    const res = await Appointment_Api.saveSchool(params as SchoolItem);
    if (res.Type === 200) {
      ElNotification({
        title: "成功",
        message: (params as SchoolItem).Id ? "编辑成功" : "添加成功",
        type: "success",
      });
      showDialog.value = false;
      handleGetTableList();
    } else {
      ElMessage.error(res.Content);
    }
  } catch (err) {
    console.log(err);
  } finally {
    dialogConfirmLoading.value = false;
  }
};

const handleGetTableList = async () => {
  tableLoading.value = true;
  try {
    const params: SchoolQueryInputDTO = {
      IsEnable: queryParams.value.IsEnable,
      Keyword: queryParams.value.Keyword || "",
      PageIndex: queryParams.value.PageIndex,
      PageSize: queryParams.value.PageSize,
    };

    const res = await Appointment_Api.querySchoolPage(params);
    if (res.Type === 200) {
      pageData.value = res.Data.Data;
      total.value = res.Data.TotalCount;
    } else {
      ElMessage.error(res.Content);
    }
  } catch (err: any) {
    ElMessage.error(err.message || "获取数据失败");
  } finally {
    tableLoading.value = false;
  }
};

onActivated(() => {
  handleGetTableList();
});
</script>

<style lang="scss" scoped></style>
