<template>
  <el-form
    ref="formRef"
    :model="form"
    :rules="rules"
    label-position="right"
    label-width="80px"
    :disabled="isPreview"
  >
    <el-form-item label="学校名称" prop="Name">
      <el-input
        v-model="form.Name"
        placeholder="请输入学校名称"
        maxlength="50"
        show-word-limit
        @blur="generatePinyin"
      />
    </el-form-item>
    <el-form-item label="学校编码" prop="Code">
      <el-input v-model="form.Code" placeholder="请输入学校编码" maxlength="20" show-word-limit />
    </el-form-item>
    <el-form-item label="拼音码" prop="PYM">
      <el-input v-model="form.PYM" placeholder="请输入拼音码" maxlength="50" show-word-limit />
    </el-form-item>
    <el-form-item label="是否启用">
      <el-switch v-model="form.IsEnable" />
    </el-form-item>
  </el-form>
</template>

<script setup lang="ts">
import { type SchoolItem } from "@/api/appointment/types";
import { chineseToPinyin } from "@/utils/pinyin-helper";
import { type FormInstance, type FormRules } from "element-plus";
import { useUserStore } from "@/store";
const userStore = useUserStore();

interface Props {
  detailInfo: SchoolItem | null;
}

const props = defineProps<Props>();

const formRef = ref<FormInstance>();

const form = ref<SchoolItem>({
  Name: "",
  Code: "",
  PYM: "",
  IsEnable: true,
  CreatorId: userStore.userInfo.Id,
  Creator: userStore.userInfo.Name,
});

const rules = reactive<FormRules<SchoolItem>>({
  Name: [
    { required: true, message: "请输入学校名称", trigger: "blur" },
    { min: 1, max: 50, message: "长度在 1 到 50 个字符", trigger: "blur" },
  ],
  Code: [
    { required: true, message: "请输入学校编码", trigger: "blur" },
    { min: 1, max: 20, message: "长度在 1 到 20 个字符", trigger: "blur" },
  ],
  PYM: [
    { required: true, message: "请输入拼音码", trigger: "blur" },
    { min: 1, max: 50, message: "长度在 1 到 50 个字符", trigger: "blur" },
  ],
});

const isPreview = inject("isPreview") as Ref<boolean>;

/**
 * 自动生成拼音码
 */
const generatePinyin = () => {
  if (form.value.Name && !form.value.PYM) {
    form.value.PYM = chineseToPinyin(form.value.Name);
  }
};

/**
 * 处理传入的编辑数据
 */
const handleProcessingData = (info: SchoolItem) => {
  form.value.Id = info.Id;
  form.value.Name = info.Name || "";
  form.value.Code = info.Code || "";
  form.value.PYM = info.PYM || "";
  form.value.IsEnable = info.IsEnable ?? true;
  form.value.CreatorId = info.CreatorId;
  form.value.Creator = info.Creator;
  form.value.UpdatedTime = info.UpdatedTime;
  form.value.UpdaterId = userStore.userInfo.Id;
};

/**
 * 表单提交验证
 */
const handleSubmit = async (): Promise<SchoolItem | null> => {
  try {
    await formRef.value!.validate();
    const copyData: SchoolItem = { ...form.value };
    return copyData;
  } catch {
    return null;
  }
};

// 监听传入数据的变化
watch(
  () => props.detailInfo,
  (newVal) => {
    if (newVal) {
      handleProcessingData(newVal);
    }
  },
  { immediate: true }
);

defineExpose({
  handleSubmit,
});
</script>

<style lang="scss" scoped></style>
