<template>
  <div v-loading="true" class="wechat-page" />
</template>

<script setup lang="ts">
const path = ref("");
// 按钮点击处理函数
const handleButtonClick = () => {
  // 方法内容暂时为空
  setTimeout(() => {
    window.location.href = `https://wxaurl.cn/${path.value}`;
  }, 200);
};
onMounted(() => {
  if (!window.location.pathname.includes("/")) {
    return;
  }
  path.value = window.location.pathname.split("/")[1];
  document.title = "康易行小程序";
  handleButtonClick();
});
</script>

<style lang="scss" scoped>
.wechat-page {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  padding: 20px;
  overflow: hidden;
  background-color: var(--el-bg-color);

  // 添加背景渐变效果
  &::before {
    position: absolute;
    inset: 0;
    pointer-events: none;
    content: "";
    background: linear-gradient(
      135deg,
      rgb(64 158 255 / 3%) 0%,
      rgb(64 158 255 / 1%) 50%,
      transparent 100%
    );
  }

  // 移动端适配
  @media (width <= 768px) {
    padding: 15px;
  }
}

.content-container {
  position: relative;
  z-index: 1;
  display: flex;
  flex-direction: column;
  gap: 30px;
  align-items: center;
  width: 100%;
  max-width: 400px;

  // 添加入场动画
  animation: fadeInUp 0.6s ease-out;

  // 移动端间距调整
  @media (width <= 768px) {
    gap: 25px;
    max-width: 100%;
  }
}

.logo {
  width: auto;
  max-width: 200px;
  height: auto;
  max-height: 120px;
  object-fit: contain;
  filter: drop-shadow(0 2px 4px rgb(0 0 0 / 10%));
  transition: all 0.3s ease;

  // 添加hover效果
  &:hover {
    filter: drop-shadow(0 4px 8px rgb(0 0 0 / 15%));
    transform: scale(1.05);
  }

  // 平板端
  @media (width >= 768px) and (width <= 1024px) {
    max-width: 250px;
    max-height: 150px;
  }

  // 桌面端
  @media (width >= 1024px) {
    max-width: 300px;
    max-height: 180px;
  }
}

.action-button {
  min-width: 150px;
  height: 48px;
  font-size: 16px;
  font-weight: 500;
  border-radius: 24px;
  box-shadow: 0 2px 8px rgb(64 158 255 / 30%);
  transition: all 0.3s ease;

  &:hover {
    box-shadow: 0 4px 12px rgb(64 158 255 / 40%);
    transform: translateY(-2px);
  }

  &:active {
    box-shadow: 0 2px 8px rgb(64 158 255 / 30%);
    transform: translateY(0);
  }

  // 移动端按钮适配
  @media (width <= 768px) {
    width: 100%;
    max-width: 280px;
    height: 44px;
    font-size: 15px;
  }
}

// 入场动画
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// 暗黑主题适配
:deep(.el-button--primary) {
  color: var(--el-color-white);
  background-color: var(--el-color-primary);
  border-color: var(--el-color-primary);

  &:hover {
    background-color: var(--el-color-primary-light-3);
    border-color: var(--el-color-primary-light-3);
  }
}

html.dark {
  .wechat-page {
    &::before {
      background: linear-gradient(
        135deg,
        rgb(64 158 255 / 5%) 0%,
        rgb(64 158 255 / 2%) 50%,
        transparent 100%
      );
    }
  }

  .logo {
    filter: drop-shadow(0 2px 4px rgb(255 255 255 / 10%));

    &:hover {
      filter: drop-shadow(0 4px 8px rgb(255 255 255 / 20%));
    }
  }

  .action-button {
    box-shadow: 0 2px 8px rgb(64 158 255 / 20%);

    &:hover {
      box-shadow: 0 4px 12px rgb(64 158 255 / 30%);
    }

    &:active {
      box-shadow: 0 2px 8px rgb(64 158 255 / 20%);
    }
  }
}
</style>
