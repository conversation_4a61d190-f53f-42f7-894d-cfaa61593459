<template>
  <div class="app-container">
    <BaseTableSearchContainer @size-changed="tableResize">
      <template #search>
        <TBSearchContainer :is-show-toggle="true">
          <template #left>
            <el-form :model="queryParams" label-position="right" :inline="true">
              <el-form-item label="评价时间">
                <el-date-picker
                  v-model="timeRange"
                  unlink-panels
                  type="daterange"
                  range-separator="至"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                  format="YYYY-MM-DD"
                  :clearable="false"
                  value-format="YYYY-MM-DD"
                  style="width: 250px"
                />
              </el-form-item>
              <el-form-item label="服务类型" prop="ConsultWay">
                <el-select
                  v-model="queryParams.ConsultWay"
                  placeholder="请选择"
                  clearable
                  :empty-values="[null, undefined, '']"
                  :value-on-clear="() => null"
                  class="w-120px!"
                >
                  <el-option label="在线问诊" :value="1" />
                  <el-option label="在线咨询" :value="2" />
                </el-select>
              </el-form-item>
              <el-form-item label="医院" prop="OrgId">
                <HospitalSelect v-model="queryParams.OrgId" />
              </el-form-item>
              <el-form-item label="医生/治疗师" prop="DoctorId">
                <UserSelect
                  v-model="queryParams.DoctorId"
                  :org-ids="queryParams.OrgId ? [queryParams.OrgId] : null"
                  :role-types="
                    queryParams.ConsultWay
                      ? [['', 'doctor', 'therapist'][Number(queryParams.ConsultWay)]]
                      : ['doctor', 'therapist']
                  "
                />
              </el-form-item>
              <el-form-item label="关键字" prop="Keyword">
                <el-input
                  v-model="queryParams.Keyword"
                  placeholder="患者名称/就诊号"
                  @keyup.enter="handleQuery"
                />
              </el-form-item>
            </el-form>
          </template>
          <template #right>
            <el-button type="primary" icon="search" @click="handleQuery">搜索</el-button>
            <el-button
              type="primary"
              :disabled="!pageData.length"
              :loading="exportLoading"
              @click="handleExportExcel"
            >
              导出
            </el-button>
          </template>
        </TBSearchContainer>
      </template>
      <template v-if="appStore.device !== DeviceEnum.MOBILE" #searchTable>
        <div class="chart-section">
          <!-- 图表控制栏 -->
          <div class="chart-control-bar">
            <div class="chart-control-left" />
            <div class="chart-control-right">
              <el-button
                size="small"
                :icon="isChartCollapsed ? ArrowDown : ArrowUp"
                class="collapse-btn"
                @click="toggleChartCollapse"
              >
                {{ isChartCollapsed ? "展开图表" : "收起图表" }}
              </el-button>
            </div>
          </div>

          <!-- 图表内容区域 -->
          <el-collapse-transition>
            <div v-show="!isChartCollapsed" class="chart-content-area">
              <div class="search-table-container">
                <div class="score-info-container">
                  <div
                    v-for="(item, index) in scoreInfo"
                    :key="index"
                    class="layout-chart-box"
                    :style="'background-color:' + item.BackgroundColor"
                  >
                    <div class="score-name">{{ item.Name }}</div>
                    <div class="score-count">{{ item.Count }}(条)</div>
                  </div>
                </div>
                <div ref="chartContainer" class="layout-chart-right" />
              </div>
            </div>
          </el-collapse-transition>
        </div>
      </template>
      <template #table>
        <el-table
          ref="tableRef"
          v-loading="tableLoading"
          :data="pageData"
          :total="total"
          border
          row-key="Id"
          :height="tableFluidHeight"
          highlight-current-row
          style="flex: 1; text-align: center"
        >
          <el-table-column
            prop="VisitNo"
            label="就诊号"
            width="160"
            show-overflow-tooltip
            align="center"
          />
          <el-table-column label="服务类型" width="140" show-overflow-tooltip align="center">
            <template #default="scope">
              {{ ["", "在线问诊", "在线咨询"][scope.row.ConsultWay] }}
            </template>
          </el-table-column>
          <el-table-column label="就诊人" width="140" show-overflow-tooltip align="center">
            <template #default="scope">
              {{ scope.row.UserName }} {{ scope.row.Sex }} {{ scope.row.Age }}
            </template>
          </el-table-column>
          <el-table-column
            prop="VisitTime"
            label="就诊时间"
            width="140"
            :formatter="tableDateFormat"
            align="center"
          />
          <el-table-column
            prop="DoctorName"
            label="医生/治疗师"
            width="140"
            align="center"
            show-overflow-tooltip
          />
          <el-table-column prop="OrganizationName" label="医院" align="center" width="140" />
          <el-table-column
            prop="Created"
            label="提交时间"
            align="center"
            width="140"
            show-overflow-tooltip
            :formatter="tableDateFormat"
          />
          <el-table-column
            label="满意度"
            align="center"
            width="160"
            :sortable="true"
            sort-by="AverageScore"
          >
            <template #default="scope">
              <el-rate
                v-model="scope.row.AverageScore"
                disabled
                show-score
                text-color="#ff9900"
                :max="5"
              />
            </template>
          </el-table-column>
          <el-table-column label="标签" align="center" show-overflow-tooltip width="160">
            <template #default="scope">
              <el-tag v-for="tag in scope.row.SatisfactionTags" :key="tag" type="success">
                {{ tag }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column
            prop="OtherAdvice"
            label="评价内容"
            align="center"
            show-overflow-tooltip
          />
        </el-table>
      </template>
      <template #pagination>
        <Pagination
          v-if="total > 0"
          v-model:total="total"
          v-model:page="queryParams.PageIndex"
          v-model:limit="queryParams.PageSize"
          @pagination="handleGetTableList"
        />
      </template>
    </BaseTableSearchContainer>
  </div>
</template>

<script setup lang="ts">
import * as echarts from "echarts";
import dayjs from "dayjs";
import { useUserStore, useAppStore } from "@/store";
import { ArrowDown, ArrowUp } from "@element-plus/icons-vue";
const userStore = useUserStore();
const appStore = useAppStore();
import { useTableConfig } from "@/hooks/useTableConfig";
import { EvaItem, GetEvaInputDTO, ScoreInfoItem } from "@/api/satisfactory/types";
import Satisfactory_Api from "@/api/satisfactory";
import { ExportEnum } from "@/enums/Other";
import { exportExcel } from "@/utils/serviceUtils";
import { PageScoreInfoItem } from "./types";
import { EChartsType } from "echarts";
import { DeviceEnum } from "@/enums/DeviceEnum";

defineOptions({
  name: "ServiceFeedback",
});

const queryParams = ref<GetEvaInputDTO>({
  PageIndex: 1,
  PageSize: 20,
  StartDate: dayjs().format("YYYY-MM-01 00:00:00"),
  EndDate: dayjs().format("YYYY-MM-DD 23:59:59"),
  OrgId: null,
  DoctorId: null,
  ConsultWay: null,
  Keyword: "",
  LoginUserId: userStore.userInfo.Id,
  Scopeable: 1,
});

const scoreInfo = ref<PageScoreInfoItem[]>([]);
const chartContainer = ref<HTMLDivElement>();
let chart: EChartsType | undefined;

// 图表显示控制
const isChartCollapsed = ref<boolean>(false);

const timeRange = ref<[string, string]>([
  dayjs().format("YYYY-MM-01"),
  dayjs().format("YYYY-MM-DD"),
]);

// 切换图表折叠状态
const toggleChartCollapse = () => {
  isChartCollapsed.value = !isChartCollapsed.value;
};

const {
  tableLoading,
  pageData,
  total,
  tableRef,
  exportLoading,
  tableDateFormat,
  tableFluidHeight,
  tableResize,
} = useTableConfig<unknown>();

const handleQuery = () => {
  queryParams.value.PageIndex = 1;
  handleGetTableList();
};
const handleGetTableList = async () => {
  tableLoading.value = true;
  const res = await Satisfactory_Api.getEva(queryParams.value);
  if (res.Type === 200) {
    pageData.value = res.Data?.Data || [];
    total.value = res.Data?.Total || 0;
    onGetChartData(res.Data.TagAndScore);
    tableLoading.value = false;
  }
};

const onGetChartData = (data: EvaItem["TagAndScore"]) => {
  if (data?.ScoreInfo) {
    const newData = data.ScoreInfo.map((e: ScoreInfoItem): PageScoreInfoItem => {
      return {
        ...e,
        Name: `${e.Name}(${e.Score}星)`,
        BackgroundColor: onGetBackgroundColor(e.Score!),
      };
    });
    scoreInfo.value = newData.filter((s) => s.Type === 0);
  }
  if (appStore.device !== DeviceEnum.MOBILE) {
    initPieChart();
  }
};
const onGetBackgroundColor = (score: number): string => {
  if (score === 5) {
    return "#FF5656";
  } else if (score === 1) {
    return "#3aa1ff";
  } else if (score === 2) {
    return "#00D699";
  } else if (score === 3) {
    return "#8680D8";
  } else if (score === 4) {
    return "#FFAF1F";
  }
  return "";
};

const handleExportExcel = async () => {
  const params = {
    ServiceExportCode: "SatisfactionSurveyReport", //后端
    ExportWay: ExportEnum.ServiceInvoke, // O:Redash, 1：后端
    ExecutingParams: queryParams.value,
    FileName: `就诊服务评价-${Date.now()}.xlsx`,
  };
  exportLoading.value = true;
  try {
    await exportExcel(params);
  } finally {
    exportLoading.value = false;
  }
};

const initPieChart = () => {
  const data: { value: number; name: string }[] = [];
  scoreInfo.value.forEach((e) => {
    data.push({ value: e.Count!, name: e.Name! });
  });
  try {
    chart!.setOption({
      color: ["#FF5656", "#FFAF1F", "#8680D8", "#00D699", "#3aa1ff"],
      tooltip: {
        trigger: "item",

        formatter: "{a} <br/>{b} : {c} ({d}%)",
      },
      legend: {
        orient: "vertical",
        bottom: "bottom",
        data: ["非常满意", "满意", "一般", "不满意", "非常不满意"],
      },
      series: [
        {
          name: "占比",
          type: "pie",
          radius: "40%",
          center: ["50%", "50%"],
          data,
          color: ["#FF5656", "#FFAF1F", "#8680D8", "#00D699", "#3aa1ff"],
          itemStyle: {
            emphasis: {
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowColor: "rgba(0, 0, 0, 0.5)",
            },
          },
        },
      ],
    });
  } catch (error) {
    console.error("Error setting options for Chart1:", error);
  }
};

const initCharts = () => {
  // 使用辅助函数初始化每个图表
  chart = initSingleChart(chart, chartContainer, "chart");
};
// 辅助函数用于初始化单个图表
const initSingleChart = (
  chartInstance: EChartsType | undefined,
  containerRef: Ref<HTMLDivElement | undefined>,
  chartName: string
) => {
  if (containerRef.value && !chartInstance) {
    if (containerRef.value.offsetWidth > 0 && containerRef.value.offsetHeight > 0) {
      try {
        return echarts.init(containerRef.value);
      } catch (error) {
        console.error(`Failed to initialize ${chartName}:`, error);
      }
    }
  }
  return chartInstance;
};

watch(timeRange, (newVal) => {
  queryParams.value.StartDate = dayjs(newVal[0]).format("YYYY-MM-DD 00:00:00");
  queryParams.value.EndDate = dayjs(newVal[1]).format("YYYY-MM-DD 23:59:59");
});

onMounted(async () => {
  await nextTick();
  initCharts();
});

onActivated(() => {
  handleGetTableList();
});
</script>

<style lang="scss" scoped>
// 图表区域整体样式
.chart-section {
  overflow: hidden;

  // 暗黑模式支持
  html.dark & {
    background-color: var(--el-bg-color-overlay);
    border-color: var(--el-border-color);
  }
}

// 图表控制栏样式
.chart-control-bar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px 12px;
  background-color: var(--el-fill-color-extra-light);
  border-bottom: 1px solid var(--el-border-color-lighter);

  // 暗黑模式支持
  html.dark & {
    background-color: var(--el-fill-color-dark);
    border-bottom-color: var(--el-border-color-darker);
  }

  .chart-control-left {
    display: flex;
    gap: 8px;
    align-items: center;

    .chart-title {
      font-size: 13px;
      font-weight: 500;
      color: var(--el-text-color-primary);
    }
  }

  .chart-control-right {
    display: flex;
    gap: 8px;
    align-items: center;

    .collapse-btn {
      margin-left: 4px;
    }
  }
}

// 图表内容区域
.chart-content-area {
  padding: 8px;
}

.search-table-container {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  padding: 12px;
  background-color: var(--el-bg-color);
  border: 1px solid var(--el-border-color-light);
  border-radius: 8px;

  // 暗黑模式支持
  html.dark & {
    background-color: var(--el-bg-color-overlay);
    border-color: var(--el-border-color);
  }
}

.score-info-container {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.layout-chart-box {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-width: 80px;
  padding: 8px 12px;
  border-radius: 6px;
  box-shadow: 0 2px 4px rgb(0 0 0 / 10%);
  transition: all 0.3s ease;

  &:hover {
    box-shadow: 0 4px 8px rgb(0 0 0 / 15%);
    transform: translateY(-2px);
  }

  // 暗黑模式下的阴影调整
  html.dark & {
    box-shadow: 0 2px 4px rgb(0 0 0 / 30%);

    &:hover {
      box-shadow: 0 4px 8px rgb(0 0 0 / 40%);
    }
  }
}

.score-name {
  margin-bottom: 4px;
  font-size: 12px;
  font-weight: 500;
  line-height: 1.2;
  color: #fff;
  text-align: center;
  text-shadow: 0 1px 2px rgb(0 0 0 / 30%);
}

.score-count {
  font-size: 14px;
  font-weight: 600;
  line-height: 1;
  color: #fff;
  text-align: center;
  text-shadow: 0 1px 2px rgb(0 0 0 / 30%);
}

.layout-chart-right {
  flex-shrink: 0;
  width: 240px;
  height: 240px;
  border-radius: 8px;

  html.dark & {
    background-color: var(--el-bg-color-overlay);
    border-color: var(--el-border-color);
  }
}

// 响应式设计
@media (width <= 1200px) {
  .chart-control-bar {
    padding: 6px 10px;

    .chart-control-right {
      gap: 6px;
    }
  }
}

@media (width <= 768px) {
  .chart-control-bar {
    flex-direction: column;
    gap: 8px;
    align-items: flex-start;

    .chart-control-right {
      justify-content: space-between;
      width: 100%;
    }
  }

  .search-table-container {
    flex-direction: column;
    gap: 12px;
  }

  .layout-chart-right {
    width: 100%;
    height: 240px;
  }

  .score-info-container {
    justify-content: center;
  }
}

@media (width <= 480px) {
  .chart-control-bar {
    .chart-title {
      font-size: 12px;
    }
  }
}
</style>
