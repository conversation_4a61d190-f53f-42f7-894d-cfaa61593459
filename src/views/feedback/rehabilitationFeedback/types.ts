import { type EvaItemData, type ScoreInfoItem } from "@/api/satisfactory/types";

export interface PageScoreInfoItem extends ScoreInfoItem {
  BackgroundColor?: string;
}
export interface PageEvaItemData extends EvaItemData {
  Satisfaction?: number;
  TherapeuticEffect?: number;
  ServiceQuality?: number;
}
export interface RequiredScoreInfoItem extends Omit<Required<ScoreInfoItem>, "Type"> {
  Type: string;
  BackgroundColor?: string;
  setOption?: any;
}
