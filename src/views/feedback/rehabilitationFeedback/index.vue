<template>
  <div class="app-container">
    <BaseTableSearchContainer @size-changed="tableResize">
      <template #search>
        <TBSearchContainer :is-show-toggle="true">
          <template #left>
            <el-form :model="queryParams" label-position="right" :inline="true">
              <el-form-item label="评价时间">
                <el-date-picker
                  v-model="timeRange"
                  unlink-panels
                  type="daterange"
                  range-separator="至"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                  :shortcuts="datePickerShortcuts"
                  format="YYYY-MM-DD"
                  :clearable="false"
                  value-format="YYYY-MM-DD"
                  style="width: 250px"
                />
              </el-form-item>
              <el-form-item label="医院" prop="OrgId">
                <HospitalSelect v-model="queryParams.OrgId" />
              </el-form-item>
              <el-form-item label="关键字" prop="Keyword">
                <el-input
                  v-model="queryParams.Keyword"
                  placeholder="患者名称/就诊号"
                  @keyup.enter="handleQuery"
                />
              </el-form-item>
            </el-form>
          </template>
          <template #right>
            <el-button type="primary" icon="search" @click="handleQuery">搜索</el-button>
            <el-button
              type="primary"
              :disabled="!pageData.length"
              :loading="exportLoading"
              @click="handleExportExcel"
            >
              导出
            </el-button>
          </template>
        </TBSearchContainer>
      </template>
      <template v-if="appStore.device !== DeviceEnum.MOBILE && scoreInfo.length" #searchTable>
        <div class="chart-section">
          <!-- 图表控制栏 -->
          <div class="chart-control-bar">
            <div class="chart-control-left">
              <span class="chart-title">数据统计图表</span>
              <el-tag v-if="scoreInfo.length" type="info" size="small">
                {{ scoreInfo.length }}个图表
              </el-tag>
            </div>
            <div class="chart-control-right">
              <el-button-group size="small">
                <el-button
                  :type="chartDisplayMode === 'compact' ? 'primary' : ''"
                  title="紧凑模式"
                  @click="chartDisplayMode = 'compact'"
                >
                  <el-icon><Grid /></el-icon>
                </el-button>
                <el-button
                  :type="chartDisplayMode === 'expanded' ? 'primary' : ''"
                  title="展开模式"
                  @click="chartDisplayMode = 'expanded'"
                >
                  <el-icon><FullScreen /></el-icon>
                </el-button>
              </el-button-group>
              <el-button
                size="small"
                :icon="isChartCollapsed ? ArrowDown : ArrowUp"
                class="collapse-btn"
                @click="toggleChartCollapse"
              >
                {{ isChartCollapsed ? "展开图表" : "收起图表" }}
              </el-button>
            </div>
          </div>

          <!-- 图表内容区域 -->
          <el-collapse-transition>
            <div v-show="!isChartCollapsed" class="chart-content-area">
              <div
                class="score-info-container"
                :class="{
                  'compact-mode': chartDisplayMode === 'compact',
                  'expanded-mode': chartDisplayMode === 'expanded',
                }"
              >
                <div v-for="item in scoreInfo" :key="item.Type" class="chart-item">
                  <div class="score-name">{{ item.Type }}</div>
                  <ECharts
                    :options="item.setOption"
                    :width="chartDisplayMode === 'compact' ? '160px' : '220px'"
                    :height="chartDisplayMode === 'compact' ? '160px' : '200px'"
                  />
                </div>
              </div>
            </div>
          </el-collapse-transition>
        </div>
      </template>
      <template #table>
        <el-table
          ref="tableRef"
          v-loading="tableLoading"
          :data="pageData"
          :total="total"
          border
          row-key="Id"
          :height="tableFluidHeight"
          highlight-current-row
          style="flex: 1; text-align: center"
        >
          <el-table-column
            prop="VisitNo"
            label="就诊号"
            width="160"
            show-overflow-tooltip
            align="center"
          />
          <el-table-column label="患者" width="140" show-overflow-tooltip align="center">
            <template #default="scope">
              {{ scope.row.UserName }} {{ scope.row.Sex }} {{ scope.row.Age }}
            </template>
          </el-table-column>
          <el-table-column prop="DctName" label="治疗团队" width="160" align="center" />
          <el-table-column prop="OrganizationName" label="医院" width="140" align="center" />
          <el-table-column
            prop="Created"
            label="提交时间"
            align="center"
            width="140"
            show-overflow-tooltip
            :formatter="tableDateFormat"
          />
          <el-table-column label="满意度" width="160" align="center">
            <template #default="scope">
              <el-rate
                v-model="scope.row.Satisfaction"
                disabled
                show-score
                text-color="#ff9900"
                :max="5"
              />
            </template>
          </el-table-column>
          <el-table-column label="治疗效果" width="160" align="center">
            <template #default="scope">
              <el-rate
                v-model="scope.row.TherapeuticEffect"
                disabled
                show-score
                text-color="#ff9900"
                :max="5"
              />
            </template>
          </el-table-column>
          <el-table-column label="服务质量" width="160" align="center">
            <template #default="scope">
              <el-rate
                v-model="scope.row.ServiceQuality"
                disabled
                show-score
                text-color="#ff9900"
                :max="5"
              />
            </template>
          </el-table-column>
          <el-table-column
            label="标签"
            align="center"
            prop="SatisfactionTagsString"
            show-overflow-tooltip
            width="160"
          />
          <el-table-column
            prop="OtherAdvice"
            label="评价内容"
            align="center"
            show-overflow-tooltip
          />
        </el-table>
      </template>
      <template #pagination>
        <Pagination
          v-if="total > 0"
          v-model:total="total"
          v-model:page="queryParams.PageIndex"
          v-model:limit="queryParams.PageSize"
          @pagination="handleGetTableList"
        />
      </template>
    </BaseTableSearchContainer>
  </div>
</template>

<script setup lang="ts">
import dayjs from "dayjs";
import { useUserStore, useAppStore } from "@/store";
const userStore = useUserStore();
const appStore = useAppStore();
import { useTableConfig } from "@/hooks/useTableConfig";
import { GetEvaInputDTO, SatisfactionSurveyItem, ScoreInfoItem } from "@/api/satisfactory/types";
import Satisfactory_Api from "@/api/satisfactory";
import { ExportEnum } from "@/enums/Other";
import { exportExcel } from "@/utils/serviceUtils";
import { PageEvaItemData, RequiredScoreInfoItem } from "./types";
import { DeviceEnum } from "@/enums/DeviceEnum";
import { listGroupByKey } from "@/utils";
import { useDateRangePicker } from "@/hooks/useDateRangePicker";
import { ArrowDown, ArrowUp, Grid, FullScreen } from "@element-plus/icons-vue";

const { datePickerShortcuts } = useDateRangePicker();

// 扩展 GroupedItem 接口以包含图表选项
interface GroupedItemWithChart<T> extends GroupedItem<T> {
  setOption?: any;
}

defineOptions({
  name: "RehabilitationFeedback",
});

const queryParams = ref<GetEvaInputDTO>({
  PageIndex: 1,
  PageSize: 20,
  StartDate: dayjs().format("YYYY-MM-01 00:00:00"),
  EndDate: dayjs().format("YYYY-MM-DD 23:59:59"),
  OrgId: null,
  DoctorId: null,
  ConsultWay: null,
  Keyword: "",
  LoginUserId: userStore.userInfo.Id,
  Scopeable: 1,
  Type: 3,
});

const scoreInfo = ref<any[]>([]);
let scoreTypeMap: string[] = ["", "满意度", "治疗效果", "服务质量"];

// 图表显示控制
const isChartCollapsed = ref<boolean>(false);
const chartDisplayMode = ref<"compact" | "expanded">("compact");

const timeRange = ref<[string, string]>([
  dayjs().format("YYYY-MM-01"),
  dayjs().format("YYYY-MM-DD"),
]);

// 切换图表折叠状态
const toggleChartCollapse = () => {
  isChartCollapsed.value = !isChartCollapsed.value;
};

const {
  tableLoading,
  pageData,
  total,
  tableRef,
  exportLoading,
  tableDateFormat,
  tableFluidHeight,
  tableResize,
} = useTableConfig<PageEvaItemData>();

const handleQuery = () => {
  queryParams.value.PageIndex = 1;
  handleGetTableList();
};
const handleGetTableList = async () => {
  tableLoading.value = true;
  const res = await Satisfactory_Api.getEva(queryParams.value);
  if (res.Type === 200) {
    const newData =
      res.Data?.Data?.map((e) => {
        return {
          ...e,
          Satisfaction: getScoreType(e.SatisfactionSurveyDetails, 1),
          TherapeuticEffect: getScoreType(e.SatisfactionSurveyDetails, 2),
          ServiceQuality: getScoreType(e.SatisfactionSurveyDetails, 3),
        };
      }) || [];
    pageData.value = newData;
    total.value = res.Data?.Total || 0;
    onInitPieCharts(res.Data.TagAndScore?.ScoreInfo?.filter((v) => v.Type) || []);
    tableLoading.value = false;
  }
};

/**
 * 初始化饼图数据
 * @param list 评分信息列表
 */
const onInitPieCharts = (list: ScoreInfoItem[] | undefined) => {
  // 数据验证
  if (!list) {
    return;
  }

  // 数据预处理：标准化数据格式并过滤无效数据
  const normalizedList = normalizeScoreInfoList(list);

  // 按类型分组
  const groupedList = listGroupByKey<RequiredScoreInfoItem & { Type: string }, "Type">(
    normalizedList,
    "Type"
  );

  // 处理分组数据：设置类型名称、背景色和图表配置
  const processedGroups = processGroupedData(groupedList);

  // 更新响应式数据
  scoreInfo.value = processedGroups;
};

/**
 * 标准化评分信息列表数据
 * @param list 原始评分信息列表
 * @returns 标准化后的数据列表
 */
const normalizeScoreInfoList = (list: ScoreInfoItem[]) => {
  return list
    .map((item) => ({
      ...item,
      Type: item.Type?.toString() || "",
      Name: item.Name || "",
      Score: item.Score || 0,
      Count: item.Count || 0,
    }))
    .filter((item): item is RequiredScoreInfoItem & { Type: string } => item.Type !== "");
};

/**
 * 根据评分设置背景颜色
 * @param score 评分值
 * @returns 对应的背景颜色
 */
const getScoreBackgroundColor = (score: number): string => {
  const colorMap: Record<number, string> = {
    1: "#3aa1ff", // 蓝色
    2: "#00D699", // 绿色
    3: "#8680D8", // 紫色
    4: "#FFAF1F", // 橙色
    5: "#FF5656", // 红色
  };
  return colorMap[score] || "";
};

/**
 * 创建饼图配置选项
 * @param data 图表数据
 * @returns ECharts 配置选项
 */
const createPieChartOption = (
  data: Array<{ value: number; name: string; itemStyle: { color: string } }>
) => {
  return {
    tooltip: {
      trigger: "item",
    },
    legend: {
      show: false,
    },
    series: [
      {
        name: "占比",
        type: "pie",
        radius: "60%",
        center: ["50%", "50%"],
        data,
        itemStyle: {
          emphasis: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: "rgba(0, 0, 0, 0.5)",
          },
        },
      },
    ],
  };
};

/**
 * 处理分组数据：设置类型名称、背景色和图表配置
 * @param groupedList 分组后的数据列表
 * @returns 处理完成的分组数据
 */
const processGroupedData = (
  groupedList: GroupedItemWithChart<RequiredScoreInfoItem & { Type: string }>[]
) => {
  return groupedList.map((group) => {
    // 设置类型名称
    group.Type = scoreTypeMap[Number(group.Type)];

    // 为每个数据项设置背景颜色
    group.Data.forEach((item) => {
      item.BackgroundColor = getScoreBackgroundColor(item.Score);
    });

    // 创建图表数据
    const chartData = group.Data.map((item) => ({
      value: item.Count,
      name: item.Name,
      itemStyle: {
        color: item.BackgroundColor!,
      },
    }));

    // 设置图表配置
    group.setOption = createPieChartOption(chartData);

    return group;
  });
};

const getScoreType = (list: SatisfactionSurveyItem[] | undefined, type: number): number => {
  if (!list) {
    return 0;
  }
  const filterList = list.filter((v) => v.Type === type);
  if (filterList.length > 0) {
    return filterList[0].Score!;
  } else {
    return 0;
  }
};
const handleExportExcel = async () => {
  const params = {
    ServiceExportCode: "SatisfactionSurveyReport", //后端
    ExportWay: ExportEnum.ServiceInvoke, // O:Redash, 1：后端
    ExecutingParams: queryParams.value,
    FileName: `康复服务评价-${Date.now()}.xlsx`,
  };
  exportLoading.value = true;
  try {
    await exportExcel(params);
  } finally {
    exportLoading.value = false;
  }
};

watch(timeRange, (newVal) => {
  queryParams.value.StartDate = dayjs(newVal[0]).format("YYYY-MM-DD 00:00:00");
  queryParams.value.EndDate = dayjs(newVal[1]).format("YYYY-MM-DD 23:59:59");
});

onActivated(() => {
  handleGetTableList();
});
</script>

<style lang="scss" scoped>
// 图表区域整体样式
.chart-section {
  overflow: hidden;

  // 暗黑模式支持
  html.dark & {
    background-color: var(--el-bg-color-overlay);
    border-color: var(--el-border-color);
  }
}

// 图表控制栏样式
.chart-control-bar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px 12px;
  background-color: var(--el-fill-color-extra-light);
  border-bottom: 1px solid var(--el-border-color-lighter);

  // 暗黑模式支持
  html.dark & {
    background-color: var(--el-fill-color-dark);
    border-bottom-color: var(--el-border-color-darker);
  }

  .chart-control-left {
    display: flex;
    gap: 8px;
    align-items: center;

    .chart-title {
      font-size: 13px;
      font-weight: 500;
      color: var(--el-text-color-primary);
    }
  }

  .chart-control-right {
    display: flex;
    gap: 8px;
    align-items: center;

    .collapse-btn {
      margin-left: 4px;
    }
  }
}

// 图表内容区域
.chart-content-area {
  padding: 8px;
}

.score-info-container {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  transition: all 0.3s ease;

  // 紧凑模式
  &.compact-mode {
    gap: 6px;

    .chart-item {
      min-width: 140px;
    }
  }

  // 展开模式
  &.expanded-mode {
    gap: 12px;

    .chart-item {
      min-width: 200px;
    }
  }
}

// 单个图表项样式
.chart-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 8px;
  cursor: pointer;
  transition: all 0.2s ease;

  // 暗黑模式下的样式
  html.dark & {
    background-color: var(--el-fill-color-darker);
    border-color: var(--el-border-color-darker);

    &:hover {
      background-color: var(--el-fill-color-dark);
      border-color: var(--el-border-color);
      box-shadow: 0 2px 8px rgb(0 0 0 / 20%);
    }
  }
}

.score-name {
  margin-bottom: 4px;
  font-size: 12px;
  font-weight: 500;
  line-height: 1.2;
  color: var(--el-text-color-primary);
  text-align: center;
  white-space: nowrap;

  // 暗黑模式支持
  html.dark & {
    color: var(--el-text-color-primary);
  }
}

// 响应式设计
@media (width <= 1200px) {
  .chart-control-bar {
    padding: 6px 10px;

    .chart-control-right {
      gap: 6px;
    }
  }

  .score-info-container {
    &.expanded-mode {
      gap: 8px;

      .chart-item {
        min-width: 180px;
      }
    }
  }
}

@media (width <= 768px) {
  .chart-control-bar {
    flex-direction: column;
    gap: 8px;
    align-items: flex-start;

    .chart-control-right {
      justify-content: space-between;
      width: 100%;
    }
  }

  .score-info-container {
    gap: 6px;

    .chart-item {
      min-width: 120px;
      padding: 6px;
    }

    &.compact-mode .chart-item {
      min-width: 110px;
    }

    &.expanded-mode .chart-item {
      min-width: 140px;
    }
  }

  .score-name {
    margin-bottom: 3px;
    font-size: 11px;
  }
}

@media (width <= 480px) {
  .score-info-container {
    flex-direction: column;
    align-items: center;

    .chart-item {
      width: 100%;
      min-width: unset;
      max-width: 200px;
    }
  }
}
</style>
