<template>
  <div v-loading="formLoading" class="p-20px overflow-y-auto h-600px">
    <el-form
      :ref="kFormRef"
      :model="formData"
      :rules="rules"
      label-width="auto"
      :disabled="props.disabled"
    >
      <div class="flex justify-between">
        <el-form-item label="来源:" prop="Source">
          <el-text>{{ formData.Source }}</el-text>
        </el-form-item>
        <el-form-item label="反馈类型:" prop="Type">
          <el-text>{{ formData.Type }}</el-text>
        </el-form-item>
        <el-form-item label="手机号" prop="Phone">
          <el-text>{{ formData.Phone }}</el-text>
        </el-form-item>
      </div>
      <el-form-item label="创建时间:" prop="CreatedTime">
        <el-text>{{ formData.CreatedTime }}</el-text>
      </el-form-item>
      <el-form-item label="问题描述:" prop="Description">
        <el-text>{{ formData.Description }}</el-text>
      </el-form-item>
      <el-form-item label="截图:">
        <el-image
          v-for="(item, index) in imageList"
          :key="index"
          class="w-100px h-100px mx-5px cursor-pointer"
          :src="item"
          :preview-src-list="imageList"
          :initial-index="index"
          fit="cover"
        />
      </el-form-item>
      <el-form-item label="处理方式" prop="Result">
        <el-input
          v-model="formData.Result"
          type="textarea"
          :autosize="{ minRows: 4, maxRows: 10 }"
          show-word-limit
          :maxlength="200"
          placeholder="请输入处理方式"
          clearable
        />
      </el-form-item>
    </el-form>
  </div>
  <!-- 底部按钮 -->
  <div class="flex justify-end">
    <el-button @click="emit('cancel')">取消</el-button>
    <el-button v-if="!props.disabled" type="primary" @click="onSubmitForm()">确定</el-button>
  </div>
</template>

<script setup lang="ts">
import { FormRules, FormInstance, dayjs } from "element-plus";
import { ProcessFeedbackParams, UserFeedback } from "@/api/satisfactory/types";
import Satisfactory_Api from "@/api/satisfactory";
import { sourceData } from "../constant";

const kEnableDebug = false;
const kFormRef = "ruleFormRef";

interface FormData extends Omit<UserFeedback, "Source"> {
  Source?: string;
}

defineOptions({
  name: "FeedbackForm",
});

const props = defineProps<{
  id: string;
  disabled: boolean;
}>();

const emit = defineEmits<{
  cancel: [];
  submit: [];
}>();

const formLoading = ref(false);
/** 表单实例 */
const formRef = useTemplateRef<FormInstance>(kFormRef);
/** 表单数据 */
const formData = ref<FormData>({});

/** 截图列表 */
const imageList = ref<string[]>([]);

/** 表单验证规则 */
const rules = reactive<FormRules<UserFeedback>>({
  Result: [{ required: true, message: "请输入处理方式", trigger: "blur" }],
});

/** 提交表单 */
function onSubmitForm() {
  if (!formRef.value) return;

  formRef.value.validate((valid, fields) => {
    if (valid) {
      requestUpdateData();
    } else {
      kEnableDebug && console.debug("提交失败", fields);
    }
  });
}

/** 请求提交表单数据 */
async function requestUpdateData() {
  kEnableDebug && console.debug("提交表单数据", formData);

  formLoading.value = true;
  const params: ProcessFeedbackParams = {
    Id: formData.value.Id!,
    Result: formData.value.Result!,
  };
  const r = await Satisfactory_Api.processFeedback(params);
  formLoading.value = false;
  if (r.Type === 200) {
    emit("submit");
  } else {
    ElMessage.error(r.Content);
  }
}

/** 加载数据 */
async function loadData() {
  formLoading.value = true;
  const r = await Satisfactory_Api.loadFeedbackById(props.id);
  formLoading.value = false;
  if (r.Type != 200) {
    ElMessage.error(r.Content);
  } else {
    formData.value = {
      ...r.Data,
      Source: sourceData.find((item) => item.value === r.Data.Source)?.label ?? "",
      Type: r.Data.Type === "0" ? "用户未反馈" : r.Data.Type,
      CreatedTime: r.Data.CreatedTime
        ? dayjs(r.Data.CreatedTime).format("YYYY-MM-DD HH:mm:ss")
        : "",
    };
    imageList.value = JSON.parse(r.Data.Screenshot ?? "[]");
  }
}

onMounted(() => {
  loadData();
});
</script>

<style lang="scss" scoped></style>
