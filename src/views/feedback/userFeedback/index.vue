<template>
  <div class="app-container">
    <BaseTableSearchContainer @size-changed="tableResize">
      <!-- 顶部筛选条件 -->
      <template #search>
        <TBSearchContainer :is-show-toggle="true">
          <template #left>
            <el-form :model="queryParams" label-position="right" :inline="true">
              <el-form-item label="来源">
                <KSelect
                  v-model="queryParams.Source"
                  :data="sourceData"
                  :show-all="true"
                  @change="handleSourceChange"
                />
              </el-form-item>
              <el-form-item label="反馈类型">
                <KSelect
                  v-model="queryParams.Type"
                  :data="typeData"
                  :props="{ label: 'Key', value: 'Value' }"
                  :loading="typeLoading"
                  :show-all="true"
                />
              </el-form-item>
              <el-form-item label="状态">
                <KSelect
                  v-model="queryParams.State"
                  :data="[
                    {
                      label: '未读',
                      value: 2,
                    },
                    {
                      label: '已读未处理',
                      value: 0,
                    },
                    {
                      label: '已处理',
                      value: 1,
                    },
                  ]"
                  :show-all="true"
                />
              </el-form-item>
              <el-form-item label="关键字">
                <el-input
                  v-model="queryParams.Keyword"
                  placeholder="手机号/问题描述"
                  clearable
                  @keyup.enter="handleQuery"
                />
              </el-form-item>
            </el-form>
          </template>
          <template #right>
            <el-button type="primary" icon="search" @click="handleQuery">搜索</el-button>
            <el-button type="primary" @click="onSetRead">设为已读</el-button>
          </template>
        </TBSearchContainer>
      </template>
      <!-- 列表 -->
      <template #table>
        <el-table
          v-loading="tableLoading"
          :data="pageData"
          :total="total"
          row-key="Id"
          :height="tableFluidHeight"
          :header-cell-style="{ textAlign: 'center' }"
          :cell-style="{ textAlign: 'center' }"
          border
          highlight-current-row
          @selection-change="(selection) => handleSelectionChange(selection, 'Id')"
        >
          <el-table-column type="selection" width="55" />
          <el-table-column prop="Source" label="来源" :formatter="sourceFormat" width="180" />
          <el-table-column label="状态" width="120">
            <template #default="scope">
              <el-text>{{ ["已读未处理", "已处理", "未读"][scope.row.State ?? 0] }}</el-text>
            </template>
          </el-table-column>
          <el-table-column prop="Phone" label="手机号" width="150" />
          <el-table-column
            prop="CreatedTime"
            label="创建时间"
            width="180"
            :formatter="tableDateFormat"
          />
          <el-table-column
            prop="Description"
            label="问题描述"
            min-width="100"
            show-overflow-tooltip
          />
          <el-table-column fixed="right" label="操作" width="180">
            <template #default="scope">
              <el-button link type="primary" @click="onPreview(scope.row)">查看</el-button>
              <el-button
                link
                type="primary"
                :disabled="scope.row.Result"
                @click="onProcess(scope.row)"
              >
                处理
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </template>
      <!-- 分页 -->
      <template #pagination>
        <Pagination
          v-if="total > 0"
          v-model:total="total"
          v-model:page="queryParams.PageIndex"
          v-model:limit="queryParams.PageSize"
          @pagination="requestTableList"
        />
      </template>
    </BaseTableSearchContainer>
  </div>

  <!-- 编辑/查看 -->
  <el-dialog
    v-model="showDataDialog.isShow"
    :title="showDataDialog.title"
    width="800"
    destroy-on-close
    @close="showDataDialog.isShow = false"
  >
    <FeedbackForm
      :id="showDataDialog.id"
      :disabled="showDataDialog.disabled"
      @cancel="showDataDialog.isShow = false"
      @submit="onConfirmSubmitItem"
    />
  </el-dialog>
</template>

<script setup lang="ts">
import { useTableConfig } from "@/hooks/useTableConfig";
import useOrgDialog from "@/hooks/useOrgDialog";
import Satisfactory_Api from "@/api/satisfactory";
import Dictionary_Api from "@/api/dictionary";
import { UserFeedback } from "@/api/satisfactory/types";
import { TableColumnCtx } from "element-plus/es/components/table/src/table/defaults";
import { sourceData } from "./constant";
import FeedbackForm from "./components/FeedbackForm.vue";

interface QueryParams {
  /** 来源 */
  Source?: number;
  /** 反馈类型 */
  Type?: string;
  /** 状态 */
  State?: number;
  /** 关键字 */
  Keyword?: string;
  /** 页码 */
  PageIndex: number;
  /** 每页条数 */
  PageSize: number;
}

/** 调试开关 */
const kEnableDebug = false;
defineOptions({
  name: "UserFeedback",
});

const {
  pageData,
  tableLoading,
  tableFluidHeight,
  total,
  tableResize,
  handleSelectionChange,
  selectedTableIds,
  tableDateFormat,
} = useTableConfig<UserFeedback>();

/** 查询条件 */
const queryParams = reactive<QueryParams>({
  PageIndex: 1,
  PageSize: 20,
});

/** 点击搜索 */
function handleQuery() {
  queryParams.PageIndex = 1;
  requestTableList();
}

/** 设为已读 */
async function onSetRead() {
  kEnableDebug && console.debug("设为已读");

  if (selectedTableIds.value.length === 0) {
    return;
  }

  const ids = selectedTableIds.value.filter(
    (id) => pageData.value.find((item) => item.Id === id)?.State === 2
  );
  if (ids.length === 0) {
    ElMessage.warning("没有未读数据");
    return;
  }

  ElMessageBox.confirm("将选中数据设置为已读, 是否继续?", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(async () => {
    const r = await Satisfactory_Api.setReaded(selectedTableIds.value);
    if (r.Type !== 200) {
      ElNotification.warning(r.Content);
      return;
    }

    ElNotification.success(r.Content);
    queryParams.PageIndex = 1;
    requestTableList();
  });
}

/** 查看/处理弹窗 */
const showDataDialog = reactive({
  isShow: false,
  title: "",
  disabled: false,
  id: "", // 用户反馈Id
});

/** 点击查看 */
async function onPreview(row: UserFeedback) {
  kEnableDebug && console.debug("查看", row);

  if (!row.Id) {
    ElMessage.error("用户反馈Id为空");
    return;
  }

  showDataDialog.title = "查看";
  showDataDialog.disabled = true;
  showDataDialog.id = row.Id!;
  showDataDialog.isShow = true;
}

/** 点击处理 */
async function onProcess(row: UserFeedback) {
  kEnableDebug && console.debug("处理", row);

  if (!row.Id) {
    ElMessage.error("用户反馈Id为空");
    return;
  }

  showDataDialog.title = "处理";
  showDataDialog.disabled = false;
  showDataDialog.id = row.Id!;
  showDataDialog.isShow = true;
}

/** 确定提交处理 */
function onConfirmSubmitItem() {
  kEnableDebug && console.debug("确定提交处理");

  // 提交成功
  showDataDialog.isShow = false;
  ElNotification.success("提交成功");

  // 刷新列表
  queryParams.PageIndex = 1;
  requestTableList();
}

/** 反馈类型数据 */
let doctorFeedbackTypeData: ReadDict[] = [];
let patientFeedbackTypeData: ReadDict[] = [];
const typeData = ref<ReadDict[]>([]);

/** 反馈类型数据加载中 */
const typeLoading = ref(false);

/**
 * 请求反馈类型数据
 */
async function requestFeedbackTypeData() {
  typeLoading.value = true;
  const rs = await Promise.all([
    Dictionary_Api.getDict({ code: "DoctorFeedbackTypeDict" }),
    Dictionary_Api.getDict({ code: "PatientFeedbackTypeDict" }),
  ]);
  typeLoading.value = false;
  const failed = rs.find((r) => r.Type !== 200);
  if (failed) {
    ElMessage.error(failed.Content);
  }

  doctorFeedbackTypeData = rs[0].Data;
  patientFeedbackTypeData = rs[1].Data;
}

/** 切换来源 */
function handleSourceChange() {
  switch (queryParams.Source) {
    case 0:
    case 10:
    case 20:
      typeData.value = [
        ...patientFeedbackTypeData,
        {
          Key: "用户未填写",
          Value: "0",
        },
      ];
      break;
    case 1:
    case 2:
    case 11:
      typeData.value = [
        ...doctorFeedbackTypeData,
        {
          Key: "用户未填写",
          Value: "0",
        },
      ];
      break;
    default:
      typeData.value = [];
  }
}

/** 来源格式化 */
function sourceFormat(row: UserFeedback, column: TableColumnCtx<UserFeedback>) {
  const val = row[column.property as keyof UserFeedback];
  var source = sourceData.find((x) => {
    return x.value === val;
  });
  return source?.label ?? "";
}

/** 请求列表数据 */
async function requestTableList() {
  tableLoading.value = true;
  const rules: Rule[] = [];
  if (queryParams.Source !== undefined && queryParams.Source !== null) {
    rules.push({ Field: "Source", Value: queryParams.Source, Operate: 3 });
  }
  if (queryParams.Type) {
    rules.push({ Field: "Type", Value: queryParams.Type, Operate: 3 });
  }
  if (queryParams.State !== undefined && queryParams.State !== null) {
    rules.push({ Field: "State", Value: queryParams.State, Operate: 3 });
  }
  const query: DictQueryParams = {
    PageCondition: {
      PageIndex: queryParams.PageIndex,
      PageSize: queryParams.PageSize,
      SortConditions: [{ SortField: "CreatedTime", ListSortDirection: 1 }],
    },
    FilterGroup: {
      Rules: rules,
      Groups: [
        {
          Rules: queryParams.Keyword
            ? [
                { Field: "Description", Value: queryParams.Keyword, Operate: 11 },
                { Field: "Phone", Value: queryParams.Keyword, Operate: 11 },
              ]
            : [],
          Operate: 2,
        },
      ],
      Operate: 1,
    },
  };
  const r = await Satisfactory_Api.getPageFeedback(query);
  tableLoading.value = false;
  if (r.Type !== 200) {
    ElMessage.error(r.Content);
    return;
  }

  // 请求成功
  pageData.value = r.Data.Rows;
  total.value = r.Data.Total;
}

onMounted(() => {
  requestFeedbackTypeData();
});

onActivated(() => {
  requestTableList();
});
</script>

<style lang="scss" scoped></style>
