<template>
  <div ref="dashboardContainerRef" class="dashboard-container">
    <!-- 顶部概览卡片 -->
    <div class="overview-section">
      <div class="overview-cards">
        <div class="overview-card">
          <div class="card-icon">
            <img src="@/assets/images/yiyuan.png" alt="医院" />
          </div>
          <div class="card-content">
            <div class="card-value">
              <el-badge
                :value="info.NewOrganizationCount"
                :hidden="!Number(info.NewOrganizationCount)"
                class="badge"
              >
                <span class="number">{{ info.OrganizationCount }}</span>
              </el-badge>
            </div>
            <div class="card-label">医院数量</div>
          </div>
        </div>
        <div class="overview-card">
          <div class="card-icon">
            <img src="@/assets/images/yisheng.png" alt="医生" />
          </div>
          <div class="card-content">
            <div class="card-value">
              <span class="number">{{ info.AuthDoctorCount }}</span>
            </div>
            <div class="card-label">累计认证医生</div>
          </div>
        </div>
        <div class="overview-card">
          <div class="card-icon">
            <img src="@/assets/images/yisheng_1.png" alt="治疗师" />
          </div>
          <div class="card-content">
            <div class="card-value">
              <span class="number">{{ info.AuthTherapistCount }}</span>
            </div>
            <div class="card-label">累计认证治疗师</div>
          </div>
        </div>
        <div class="overview-card">
          <div class="card-icon">
            <img src="@/assets/images/yisheng_1.png" alt="护士" />
          </div>
          <div class="card-content">
            <div class="card-value">
              <span class="number">{{ info.AuthNurseCount }}</span>
            </div>
            <div class="card-label">累计认证护士</div>
          </div>
        </div>
        <div class="overview-card">
          <div class="card-icon">
            <img src="@/assets/images/yonghu.png" alt="用户" />
          </div>
          <div class="card-content">
            <div class="card-value">
              <span class="number">{{ info.NormalUserCount }}</span>
            </div>
            <div class="card-label">普通用户总数</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 图表和待办事项区域 -->
    <div class="charts-todo-section">
      <!-- 图表区域 -->
      <div class="charts-section">
        <div class="chart-card chart-card-top overflow-hidden">
          <div ref="chart1Container" class="w-full h-full" />
        </div>
        <div class="chart-card chart-card-top overflow-hidden">
          <div ref="chart2Container" class="w-full h-full" />
        </div>
      </div>

      <!-- 待办事项卡片 -->
      <div class="todo-section">
        <div class="todo-card">
          <div class="todo-header">
            <h3>待办事项</h3>
          </div>
          <div class="todo-content">
            <div class="todo-item" @click="handleToUrl('DD')">
              <span class="todo-label">待发货订单</span>
              <span class="todo-count">{{ info.WaitSendGoodsOrderCount }}</span>
            </div>
            <div class="todo-item" @click="handleToUrl('YJ')">
              <span class="todo-label">待退还押金</span>
              <span class="todo-count">{{ info.WaitRefundedDeviceBond }}</span>
            </div>
            <div class="todo-item" @click="handleToUrl('YS')">
              <span class="todo-label">待审核医生</span>
              <span class="todo-count">{{ info.WaitAuthDoctorCount }}</span>
            </div>
            <div class="todo-item" @click="handleToUrl('FK')">
              <span class="todo-label">用户反馈</span>
              <span class="todo-count">{{ info.UnReadUserFeedback }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 统计数据区域 -->
    <div class="stats-section">
      <div class="stats-header">
        <h3>统计数据</h3>
        <div class="stats-filters">
          <div label="医院">
            <HospitalSelect v-model="query1.parameters.orgId" />
          </div>
          <div label="数据视图">
            <el-radio-group v-model="radio" @input="radioChange">
              <el-radio value="Day">日</el-radio>
              <el-radio value="Week">周</el-radio>
              <el-radio value="Month">月</el-radio>
            </el-radio-group>
          </div>
          <div label="时间">
            <el-date-picker
              v-model="query1.parameters.timeRange.start"
              type="date"
              placeholder="开始日期"
              :picker-options="maxDateOptions"
              class="date-picker"
              :clearable="false"
            />
            -
            <el-date-picker
              v-model="query1.parameters.timeRange.end"
              type="date"
              placeholder="结束日期"
              :picker-options="maxDateOptions"
              class="date-picker"
              :clearable="false"
            />
            <el-button type="primary" class="confirm-btn" @click="handleRefreshData">
              确认
            </el-button>
          </div>
        </div>
      </div>

      <div class="stats-cards">
        <div class="stat-card">
          <div class="stat-card-header">
            <h4>医生/治疗师/护士</h4>
          </div>
          <div class="stat-card-content">
            <div class="stat-row total-row">
              <span class="stat-value">
                {{ baseData.DoctorCount || 0 }} / {{ baseData.TherapistCount || 0 }} /
                {{ baseData.NurseCount || 0 }}
              </span>
            </div>
            <div class="stat-row">
              <span class="stat-label">昨日</span>
              <span class="stat-value">
                {{ baseData.YesterdayDoctorCount || 0 }} /
                {{ baseData.YesterdayTherapistCount || 0 }} /
                {{ baseData.YesterdayNurseCount || 0 }}
              </span>
            </div>
            <div class="stat-row">
              <span class="stat-label">今日</span>
              <span class="stat-value">
                {{ baseData.DailyDoctorCount || 0 }} / {{ baseData.DailyTherapistCount || 0 }} /
                {{ baseData.DailyNurseCount || 0 }}
              </span>
            </div>
          </div>
        </div>
        <div class="stat-card">
          <div class="stat-card-header">
            <h4>就诊数(医/治/护)</h4>
          </div>
          <div class="stat-card-content">
            <div class="stat-row total-row">
              <span class="stat-value">
                {{ baseData.ConsultCount || 0 }} / {{ baseData.TreatmentCount || 0 }}/
                {{ baseData.NurseConsultCount || 0 }}
              </span>
            </div>
            <div class="stat-row">
              <span class="stat-label">昨日</span>
              <span class="stat-value">
                {{ baseData.YesterdayConsultCount || 0 }} /
                {{ baseData.YesterdayTreatmentCount || 0 }} /
                {{ baseData.YesterdayNurseConsultCount || 0 }}
              </span>
            </div>
            <div class="stat-row">
              <span class="stat-label">今日</span>
              <span class="stat-value">
                {{ baseData.DailyConsultCount || 0 }} / {{ baseData.DailyTreatmentCount || 0 }} /
                {{ baseData.DailyNurseConsultCount || 0 }}
              </span>
            </div>
          </div>
        </div>
        <div class="stat-card">
          <div class="stat-card-header">
            <h4>就诊收入(医/治/护)</h4>
          </div>
          <div class="stat-card-content">
            <div class="stat-row total-row">
              <span class="stat-value">
                {{ baseData.DoctorConsultAmount || 0 }} /
                {{ baseData.TherapistConsultAmount || 0 }} / {{ baseData.NurseConsultAmount || 0 }}
              </span>
            </div>
            <div class="stat-row">
              <span class="stat-label">昨日</span>
              <span class="stat-value">
                {{ baseData.YesterdayDoctorConsultAmount || 0 }} /
                {{ baseData.YesterdayTherapistConsultAmount || 0 }} /
                {{ baseData.YesterdayTherapistConsultAmount || 0 }}
              </span>
            </div>
            <div class="stat-row">
              <span class="stat-label">今日</span>
              <span class="stat-value">
                {{ baseData.DailyDoctorConsultAmount || 0 }} /
                {{ baseData.DailyTherapistConsultAmount || 0 }} /
                {{ baseData.DailyNurseConsultAmount || 0 }}
              </span>
            </div>
          </div>
        </div>
        <div class="stat-card">
          <div class="stat-card-header">
            <h4>主动开方（医/治/护）</h4>
          </div>
          <div class="stat-card-content">
            <div class="stat-row total-row">
              <span class="stat-value">
                {{ baseData.DoctorDirectPrescriptionCount || 0 }} /
                {{ baseData.TherapistDirectPrescriptionCount || 0 }} /
                {{ baseData.NurseDirectPrescriptionCount || 0 }}
              </span>
            </div>
            <div class="stat-row">
              <span class="stat-label">昨日</span>
              <span class="stat-value">
                {{ baseData.YesterdayDoctorDirectPrescriptionCount || 0 }} /
                {{ baseData.YesterdayTherapistDirectPrescriptionCount || 0 }} /
                {{ baseData.YesterdayNurseDirectPrescriptionCount || 0 }}
              </span>
            </div>
            <div class="stat-row">
              <span class="stat-label">今日</span>
              <span class="stat-value">
                {{ baseData.DailyDoctorDirectPrescriptionCount || 0 }} /
                {{ baseData.DailyTherapistDirectPrescriptionCount || 0 }} /
                {{ baseData.DailyNurseDirectPrescriptionCount || 0 }}
              </span>
            </div>
          </div>
        </div>
        <div class="stat-card">
          <div class="stat-card-header">
            <h4>快速开方（医/治/护）</h4>
          </div>
          <div class="stat-card-content">
            <div class="stat-row total-row">
              <span class="stat-value">
                {{ baseData.DoctorQuickPrescriptionCount || 0 }} /
                {{ baseData.TherapistQuickPrescriptionCount || 0 }} /
                {{ baseData.NurseQuickPrescriptionCount || 0 }}
              </span>
            </div>
            <div class="stat-row">
              <span class="stat-label">昨日</span>
              <span class="stat-value">
                {{ baseData.YesterdayDoctorQuickPrescriptionCount || 0 }} /
                {{ baseData.YesterdayTherapistQuickPrescriptionCount || 0 }} /
                {{ baseData.YesterdayNurseDirectPrescriptionCount || 0 }}
              </span>
            </div>
            <div class="stat-row">
              <span class="stat-label">今日</span>
              <span class="stat-value">
                {{ baseData.DailyDoctorQuickPrescriptionCount || 0 }} /
                {{ baseData.DailyTherapistQuickPrescriptionCount || 0 }} /
                {{ baseData.DailyNurseQuickPrescriptionCount || 0 }}
              </span>
            </div>
          </div>
        </div>
        <div class="stat-card">
          <div class="stat-card-header">
            <h4>就诊开方(医/治/护)</h4>
          </div>
          <div class="stat-card-content">
            <div class="stat-row total-row">
              <span class="stat-value">
                {{ baseData.DoctorConsultPrescriptionCount || 0 }} /
                {{ baseData.TherapistConsultPrescriptionCount || 0 }} /
                {{ baseData.NurseConsultPrescriptionCount || 0 }}
              </span>
            </div>
            <div class="stat-row">
              <span class="stat-label">昨日</span>
              <span class="stat-value">
                {{ baseData.YesterdayDoctorConsultPrescriptionCount || 0 }} /
                {{ baseData.YesterdayTherapistConsultPrescriptionCount || 0 }} /
                {{ baseData.YesterdayNurseConsultPrescriptionCount || 0 }}
              </span>
            </div>
            <div class="stat-row">
              <span class="stat-label">今日</span>
              <span class="stat-value">
                {{ baseData.DailyDoctorConsultPrescriptionCount || 0 }} /
                {{ baseData.DailyTherapistConsultPrescriptionCount || 0 }} /
                {{ baseData.DailyNurseConsultPrescriptionCount || 0 }}
              </span>
            </div>
          </div>
        </div>
        <div class="stat-card">
          <div class="stat-card-header">
            <h4>治疗收入</h4>
          </div>
          <div class="stat-card-content">
            <div class="stat-row total-row">
              <span class="stat-value">￥{{ baseData.VisitAmount || 0 }}</span>
            </div>
            <div class="stat-row">
              <span class="stat-label">昨日</span>
              <span class="stat-value">￥{{ baseData.YesterdayVisitAmount || 0 }}</span>
            </div>
            <div class="stat-row">
              <span class="stat-label">今日</span>
              <span class="stat-value">￥{{ baseData.DailyVisitAmount || 0 }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 底部图表区域 -->
    <div class="bottom-charts-section">
      <el-row :gutter="24">
        <el-col :span="24" :lg="12">
          <div ref="chart3Container" class="chart-card" />
        </el-col>
        <el-col :span="24" :lg="12">
          <div ref="chart4Container" class="chart-card" />
        </el-col>
      </el-row>
      <el-row :gutter="24">
        <el-col :span="24" :lg="12">
          <div ref="chart5Container" class="chart-card" />
        </el-col>
        <el-col :span="24" :lg="12">
          <div ref="chart6Container" class="chart-card" />
        </el-col>
      </el-row>
    </div>

    <el-dialog
      v-model="isShowWeakPasswordDialog"
      title="您的密码强度过低，请修改密码"
      width="400px"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :show-close="false"
      destroy-on-close
    >
      <ChangePassword @success="handleChangePasswordSuccess" />
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import Report_Api from "@/api/report";
import {
  PlatformHomePageChart,
  PlatformHomePageStatistics,
  PlatformHomePageSummary,
} from "@/api/report/types";
import { useRouter } from "vue-router";
import * as echarts from "echarts";
import type { EChartsType } from "echarts";
import { calculateAmount } from "@/utils";
import dayjs from "dayjs";

import { useWeakPasswordAlert } from "./useWeakPasswordAlert";
const { isShowWeakPasswordDialog } = useWeakPasswordAlert();

const handleChangePasswordSuccess = () => {
  isShowWeakPasswordDialog.value = false;
};

const router = useRouter();
const info = ref<PlatformHomePageSummary>({} as PlatformHomePageSummary);
const baseData = ref<PlatformHomePageStatistics>({} as PlatformHomePageStatistics);
// 将Chart1-6改为普通变量定义
let Chart1: EChartsType | undefined;
let Chart2: EChartsType | undefined;
let Chart3: EChartsType | undefined;
let Chart4: EChartsType | undefined;
let Chart5: EChartsType | undefined;
let Chart6: EChartsType | undefined;
const chart1Container = useTemplateRef<HTMLDivElement>("chart1Container");
const chart2Container = useTemplateRef<HTMLDivElement>("chart2Container");
const chart3Container = useTemplateRef<HTMLDivElement>("chart3Container");
const chart4Container = useTemplateRef<HTMLDivElement>("chart4Container");
const chart5Container = useTemplateRef<HTMLDivElement>("chart5Container");
const chart6Container = useTemplateRef<HTMLDivElement>("chart6Container");

const maxDateOptions = {
  disabledDate(time: any) {
    return time.getTime() > new Date(); // 如果没有后面的-8.64e7就是不可以选择今天的
  },
};
const query1 = ref({
  queryName: "Report_PlatformHomePageStatistics",
  parameters: {
    orgId: null,
    timeRange: {
      start: dayjs(new Date()).format("YYYY-MM-01") + " 00:00:00",
      end: dayjs(new Date()).format("YYYY-MM-DD") + " 23:59:59",
    },
  },
  maxAge: 0,
  JobWaitingMs: 30000,
  pageIndex: 1,
  pageSize: 9999,
});
const radio = ref<string>("Day");
const disabledNextCount = ref<boolean>(false);
const handleToUrl = (type: string) => {
  switch (type) {
    case "DD":
      router.push({
        path: "/order-management/therapyOrder",
        query: {
          states: 1,
        },
      });
      break;
    case "YJ":
      // router.push({
      //   path: "/orderMar/orderInstru",
      //   query: {
      //     deviceState: 4,
      //     limitRemainMaxDays: 3,
      //   },
      // });
      break;
    case "YS":
      // router.push({ path: "/user-manage/doctor-verify" });
      break;
    case "FK":
      // router.push({ path: "/feedbackIndex/feedback" });
      break;
    default:
      break;
  }
};

const loadTopDataInfo = async () => {
  const res = await Report_Api.getRedashList<PlatformHomePageSummary>({
    queryName: "Report_PlatformHomePageSummary",
    parameters: {},
    maxAge: 0,
    JobWaitingMs: 30000,
    pageIndex: 1,
    pageSize: 999,
  });
  if (res.Type === 200) {
    info.value = res.Data.Data[0];
    updateChart1(res.Data.Data[0]);
    updateChart2(res.Data.Data[0]);
  }
};

const initCharts = () => {
  // 使用辅助函数初始化每个图表
  Chart1 = echarts.init(chart1Container.value);
  Chart1.setOption(chart1Option);
  Chart2 = echarts.init(chart2Container.value);
  Chart2.setOption(chart2Option);
  Chart3 = echarts.init(chart3Container.value);
  Chart4 = echarts.init(chart4Container.value);
  Chart5 = echarts.init(chart5Container.value);
  Chart6 = echarts.init(chart6Container.value);
};

const chart1Option = {
  title: {
    text: "就诊数据统计",
    textStyle: {
      fontSize: 18,
    },
  },
  tooltip: {
    trigger: "axis",
    axisPointer: {
      type: "shadow",
    },
    formatter: function (params: any[]) {
      // 获取当前选中的是哪一行（就诊数量或就诊收入）
      const categoryName = params[0].name;

      // 只保留当前行相关的数据
      const filteredParams = params.filter((param) => {
        // 对于"就诊数量"行，只保留xAxisIndex为0的系列
        if (categoryName === "就诊数量" && param.seriesIndex < 3) {
          return true;
        }
        // 对于"就诊收入"行，只保留xAxisIndex为1的系列
        if (categoryName === "就诊收入" && param.seriesIndex >= 3) {
          return true;
        }
        return false;
      });

      // 如果没有数据，返回空
      if (filteredParams.length === 0) {
        return "";
      }

      // 构建提示文本
      let result = `${categoryName}<br/>`;
      let unit = categoryName === "就诊数量" ? "次" : "元";
      filteredParams.forEach((param) => {
        // 使用彩色标记和名称
        result += `${param.marker} ${param.seriesName}: ${param.value}${unit}<br/>`;
      });

      return result;
    },
  },
  legend: {
    left: "right",
  },
  grid: {
    left: "3%",
    right: "4%",
    bottom: "3%",
    containLabel: true,
  },
  xAxis: [
    {
      type: "value",
      min: 0,
      minInterval: 100, // 最小单位为100次
      position: "bottom",
      axisLabel: {
        formatter: "{value}次",
      },
    },
    {
      type: "value",
      min: 0,
      minInterval: 100, // 最小单位为100元
      position: "top",
      axisLabel: {
        formatter: "{value}元",
      },
    },
  ],
  yAxis: {
    type: "category",
    data: ["就诊数量", "就诊收入"],
    axisPointer: {
      type: "shadow",
    },
  },
  series: [
    // // 就诊数量系列 - 只使用下方X轴(xAxisIndex: 0)
    // {
    //   name: "医生问诊",
    //   type: "bar",
    //   barWidth: 25,
    //   stack: "图表",
    //   label: {
    //     show: true,
    //     position: "inside",
    //   },
    //   xAxisIndex: 0,
    //   data: [doctorConsult, "-"], // 第一个位置是"就诊数量"行
    //   itemStyle: {
    //     color: "#52c41a", // 医生颜色
    //   },
    // },
    // {
    //   name: "治疗师咨询",
    //   type: "bar",
    //   stack: "图表",
    //   xAxisIndex: 0,
    //   label: {
    //     show: true,
    //     position: "inside",
    //   },
    //   data: [therapistConsult, "-"], // 第一个位置是"就诊数量"行
    //   itemStyle: {
    //     color: "#faad14", // 治疗师颜色
    //   },
    // },
    // {
    //   name: "护士咨询",
    //   type: "bar",
    //   stack: "图表",
    //   xAxisIndex: 0,
    //   label: {
    //     show: true,
    //     position: "inside",
    //   },
    //   data: [nurseConsult, "-"], // 第一个位置是"就诊数量"行
    //   itemStyle: {
    //     color: "#2ed1d1", // 护士颜色
    //   },
    // },
    // // 就诊收入系列 - 只使用上方X轴(xAxisIndex: 1)
    // {
    //   name: "医生问诊",
    //   type: "bar",
    //   barWidth: 25,
    //   stack: "图表",
    //   xAxisIndex: 1,
    //   label: {
    //     show: true,
    //     position: "inside",
    //   },
    //   data: ["-", doctorAmount], // 第二个位置是"就诊收入"行
    //   itemStyle: {
    //     color: "#52c41a", // 医生颜色
    //   },
    // },
    // {
    //   name: "治疗师咨询",
    //   type: "bar",
    //   stack: "图表",
    //   xAxisIndex: 1,
    //   label: {
    //     show: true,
    //     position: "inside",
    //   },
    //   data: ["-", therapistAmount], // 第二个位置是"就诊收入"行
    //   itemStyle: {
    //     color: "#faad14", // 治疗师颜色
    //   },
    // },
    // {
    //   name: "护士咨询",
    //   type: "bar",
    //   stack: "图表",
    //   xAxisIndex: 1,
    //   label: {
    //     show: true,
    //     position: "inside",
    //   },
    //   data: ["-", nurseAmount], // 第二个位置是"就诊收入"行
    //   itemStyle: {
    //     color: "#2ed1d1", // 护士颜色
    //   },
    // },
  ],
};

const chart2Option = {
  title: {
    text: "方案数据统计",
    textStyle: {
      fontSize: 18,
    },
  },
  tooltip: {
    trigger: "axis",
    axisPointer: {
      type: "shadow",
    },
    formatter: function (params: any[]) {
      // 获取当前选中的是哪一行（方案数量或方案收入）
      const categoryName = params[0].name;

      // 只保留当前行相关的数据
      const filteredParams = params.filter((param) => {
        // 对于"方案数量"行，只保留xAxisIndex为0的系列
        if (categoryName === "方案数量" && param.seriesIndex < 3) {
          return true;
        }
        // 对于"方案收入"行，只保留xAxisIndex为1的系列
        if (categoryName === "方案收入" && param.seriesIndex >= 3) {
          return true;
        }
        return false;
      });

      // 如果没有数据，返回空
      if (filteredParams.length === 0) {
        return "";
      }

      // 构建提示文本
      let result = `${categoryName}<br/>`;
      let unit = categoryName === "方案数量" ? "次" : "元";
      filteredParams.forEach((param) => {
        // 使用彩色标记和名称
        result += `${param.marker} ${param.seriesName}: ${param.value}${unit}<br/>`;
      });

      return result;
    },
  },
  legend: { left: "right" },
  grid: {
    left: "3%",
    right: "4%",
    bottom: "3%",
    containLabel: true,
  },
  xAxis: [
    {
      type: "value",
      min: 0,
      minInterval: 100, // 最小单位为100次
      position: "bottom",
      axisLabel: {
        formatter: "{value}次",
      },
    },
    {
      type: "value",
      min: 0,
      minInterval: 100, // 最小单位为100元
      position: "top",
      axisLabel: {
        formatter: "{value}元",
      },
    },
  ],
  yAxis: {
    type: "category",
    data: ["方案数量", "方案收入"],
    axisPointer: {
      type: "shadow",
    },
  },
};

const updateChart1 = (obj: PlatformHomePageSummary) => {
  if (!Chart1) {
    console.error("Chart1 is not initialized");
    return;
  }

  const doctorConsult = calculateAmount([obj.DoctorConsultCount], "+");
  const therapistConsult = calculateAmount([obj.TherapistConsultCount], "+");
  const nurseConsult = calculateAmount([obj.NurseConsultCount], "+");
  const doctorAmount = calculateAmount([obj.DoctorConsultAmount], "+");
  const therapistAmount = calculateAmount([obj.TherapistConsultAmount], "+");
  const nurseAmount = calculateAmount([obj.NurseConsultAmount], "+");

  Chart1!.setOption({
    series: [
      // 就诊数量系列 - 只使用下方X轴(xAxisIndex: 0)
      {
        name: "医生问诊",
        type: "bar",
        barWidth: 25,
        stack: "图表",
        label: {
          show: true,
          position: "inside",
        },
        xAxisIndex: 0,
        data: [doctorConsult, "-"], // 第一个位置是"就诊数量"行
        itemStyle: {
          color: "#52c41a", // 医生颜色
        },
      },
      {
        name: "治疗师咨询",
        type: "bar",
        stack: "图表",
        xAxisIndex: 0,
        label: {
          show: true,
          position: "inside",
        },
        data: [therapistConsult, "-"], // 第一个位置是"就诊数量"行
        itemStyle: {
          color: "#faad14", // 治疗师颜色
        },
      },
      {
        name: "护士咨询",
        type: "bar",
        stack: "图表",
        xAxisIndex: 0,
        label: {
          show: true,
          position: "inside",
        },
        data: [nurseConsult, "-"], // 第一个位置是"就诊数量"行
        itemStyle: {
          color: "#2ed1d1", // 护士颜色
        },
      },
      // 就诊收入系列 - 只使用上方X轴(xAxisIndex: 1)
      {
        name: "医生问诊",
        type: "bar",
        barWidth: 25,
        stack: "图表",
        xAxisIndex: 1,
        label: {
          show: true,
          position: "inside",
        },
        data: ["-", doctorAmount], // 第二个位置是"就诊收入"行
        itemStyle: {
          color: "#52c41a", // 医生颜色
        },
      },
      {
        name: "治疗师咨询",
        type: "bar",
        stack: "图表",
        xAxisIndex: 1,
        label: {
          show: true,
          position: "inside",
        },
        data: ["-", therapistAmount], // 第二个位置是"就诊收入"行
        itemStyle: {
          color: "#faad14", // 治疗师颜色
        },
      },
      {
        name: "护士咨询",
        type: "bar",
        stack: "图表",
        xAxisIndex: 1,
        label: {
          show: true,
          position: "inside",
        },
        data: ["-", nurseAmount], // 第二个位置是"就诊收入"行
        itemStyle: {
          color: "#2ed1d1", // 护士颜色
        },
      },
    ],
  });
};

const updateChart2 = (obj: PlatformHomePageSummary) => {
  if (!Chart2) {
    console.error("Chart2 is not initialized");
    return;
  }

  const doctorPrescription = calculateAmount([obj.DoctorPrescriptionCount], "+");
  const therapistPrescription = calculateAmount([obj.TherapistPrescriptionCount], "+");
  const nursePrescription = calculateAmount([obj.NursePrescriptionCount], "+");
  const doctorPrescriptionAmount = calculateAmount([obj.DoctorPrescriptionAmount], "+");
  const therapistPrescriptionAmount = calculateAmount([obj.TherapistPrescriptionAmount], "+");
  const nursePrescriptionAmount = calculateAmount([obj.NursePrescriptionAmount], "+");

  Chart2!.setOption({
    series: [
      // 方案数量系列 - 只使用下方X轴(xAxisIndex: 0)
      {
        name: "医生开方",
        type: "bar",
        label: {
          show: true,
          position: "inside",
        },
        barWidth: 25,
        stack: "图表",
        xAxisIndex: 0,
        data: [doctorPrescription, "-"], // 第一个位置是"方案数量"行
        itemStyle: {
          color: "#52c41a", // 医生颜色
        },
      },
      {
        name: "治疗师开方",
        type: "bar",
        stack: "图表",
        xAxisIndex: 0,
        label: {
          show: true,
          position: "inside",
        },
        data: [therapistPrescription, "-"], // 第一个位置是"方案数量"行
        itemStyle: {
          color: "#faad14", // 治疗师颜色
        },
      },
      {
        name: "护士开方",
        type: "bar",
        stack: "图表",
        xAxisIndex: 0,
        label: {
          show: true,
          position: "inside",
        },
        data: [nursePrescription, "-"], // 第一个位置是"方案数量"行
        itemStyle: {
          color: "#2ed1d1", // 护士颜色
        },
      },
      // 方案收入系列 - 只使用上方X轴(xAxisIndex: 1)
      {
        name: "医生开方",
        type: "bar",
        barWidth: 25,
        stack: "图表",
        xAxisIndex: 1,
        label: {
          show: true,
          position: "inside",
        },
        data: ["-", doctorPrescriptionAmount], // 第二个位置是"方案收入"行
        itemStyle: {
          color: "#52c41a", // 医生颜色
        },
      },
      {
        name: "治疗师开方",
        type: "bar",
        stack: "图表",
        xAxisIndex: 1,
        label: {
          show: true,
          position: "inside",
        },
        data: ["-", therapistPrescriptionAmount], // 第二个位置是"方案收入"行
        itemStyle: {
          color: "#faad14", // 治疗师颜色
        },
      },
      {
        name: "护士开方",
        type: "bar",
        stack: "图表",
        xAxisIndex: 1,
        label: {
          show: true,
          position: "inside",
        },
        data: ["-", nursePrescriptionAmount], // 第二个位置是"方案收入"行
        itemStyle: {
          color: "#2ed1d1", // 护士颜色
        },
      },
    ],
  });
};

const radioChange = (e: string) => {
  if (e !== "Day") {
    query1.value.pageSize = 999;
  } else {
    query1.value.pageSize = 10;
  }
  query1.value.pageIndex = 1;
};

const handleRefreshData = () => {
  loadDataCount();
  loadDataCountChartsData();
};

const handleGetParams = () => {
  const params = JSON.parse(JSON.stringify(query1.value));
  params.parameters.timeRange.start = dayjs(params.parameters.timeRange.start).format(
    "YYYY-MM-DD 00:00:00"
  );
  params.parameters.timeRange.end = dayjs(params.parameters.timeRange.end).format(
    "YYYY-MM-DD 23:59:59"
  );
  params.parameters.orgId = params.parameters.orgId || "*";
  return params;
};

const loadDataCount = async () => {
  const params = handleGetParams();
  const res = await Report_Api.getRedashList<PlatformHomePageStatistics>(params);
  if (res.Type === 200) {
    baseData.value = res.Data.Data[0];
  }
};

const loadDataCountChartsData = async () => {
  const params = handleGetParams();
  params.queryName = "Report_PlatformHomePageChart";
  const res = await Report_Api.getRedashList<PlatformHomePageChart>(params);
  if (res.Type === 200) {
    if (res.Data.Data.length === 0) return;
    let X: string[] = [];
    let data = [];
    data = res.Data.Data;
    if (res.Data.Data.length < query1.value.pageSize) {
      disabledNextCount.value = true;
    } else {
      disabledNextCount.value = false;
    }
    if (radio.value === "Day") {
      X = res.Data.Data.map((v) => v.Date);
    } else if (radio.value === "Week") {
      var chunks = [];
      for (var i = 0; i < res.Data.Data.length; i += 7) {
        var chunk = res.Data.Data.slice(i, i + 7);
        chunks.push(chunk);
      }
      chunks.forEach((v) => {
        X.push(v[0].Date + "至" + v[v.length - 1].Date);
      });
      data = chunks;
    } else if (radio.value === "Month") {
      const groupedData = res.Data.Data.reduce((result: Record<string, any[]>, item) => {
        const [year, month] = item.Date.split("-");
        const key = `${year}-${month}`;
        if (!result[key]) {
          result[key] = [];
        }
        result[key].push(item);
        return result;
      }, {});
      const groupedArray = Object.values(groupedData);
      groupedArray.forEach((v) => {
        X.push(v[0].Date.split("-")[0] + "-" + v[0].Date.split("-")[1]);
      });
      data = groupedArray;
    }
    updateChart3(X, data, radio.value);
    updateChart4(X, data, radio.value);
    updateChart5(X, data, radio.value);
    updateChart6(X, data, radio.value);
  }
};

const updateChart3 = (X: string[], List: any[], radio: string) => {
  if (!Chart3) {
    console.error("Chart3 is not initialized");
    return;
  }

  let titleText = "普通用户";
  let legendData = ["患者数"];
  if (query1.value.parameters.orgId) {
    titleText = "患者";
    legendData = ["新增获客", "新增患者"];
  } else {
    titleText = "普通用户";
    legendData = ["普通患者"];
  }
  let Y = [];
  let HKY = [];
  let HZY = [];
  if (radio === "Day") {
    Y = List.map((v) => v.UserCount);
    HKY = List.map((v) => v.inviteCount);
    HZY = List.map((v) => v.PatientCount);
  } else {
    List.forEach((o) => {
      const sum = o.reduce((total: number, item: any) => total + parseInt(item.UserCount), 0);
      Y.push(sum);
      const sum1 = o.reduce((total: number, item: any) => total + parseInt(item.inviteCount), 0);
      HKY.push(sum1);
      const sum2 = o.reduce((total: number, item: any) => total + parseInt(item.inviteCount), 0);
      HZY.push(sum2);
    });
  }
  const data = {
    title: {
      text: titleText,
      textStyle: {
        fontSize: 18,
      },
    },
    tooltip: {
      trigger: "axis",
    },
    legend: {
      data: legendData,
    },
    grid: {
      left: "3%",
      right: "4%",
      bottom: "15%",
      containLabel: true,
    },
    toolbox: {
      feature: {
        saveAsImage: {},
      },
    },
    dataZoom: [
      {
        type: "slider",
        show: true,
        xAxisIndex: [0],
        start: Math.max(0, ((X.length - 20) / X.length) * 100),
        end: 100,
        bottom: 10,
      },
    ],
    xAxis: {
      type: "category",
      boundaryGap: false,
      data: X,
      axisLabel: {
        rotate: 45,
        interval: 0,
      },
    },
    yAxis: {
      type: "value",
      minInterval: 1,
    },
    series: [
      {
        name: "普通患者",
        type: "line",
        data: Y,
      },
    ],
  };
  if (query1.value.parameters.orgId) {
    data.series = [
      {
        name: "新增获客",
        type: "line",
        data: HKY,
      },
      {
        name: "新增患者",
        type: "line",
        data: HZY,
      },
    ];
  } else {
    data.series = [
      {
        name: "普通患者",
        type: "line",
        data: Y,
      },
    ];
  }

  try {
    Chart3!.setOption(data, false, false);
  } catch (error) {
    console.error("Error setting options for Chart3:", error);
  }
};

const updateChart4 = (X: string[], List: any[], radio: string) => {
  if (!Chart4) {
    console.error("Chart4 is not initialized");
    return;
  }

  let Total = [];
  let WZTotal = [];
  let ZXTotal = [];
  let HLTotal = [];
  if (radio === "Day") {
    Total = List.map((v) => v.VisitCount);
    WZTotal = List.map((v) => v.ConsultCount);
    ZXTotal = List.map((v) => v.TreatmentCount);
    HLTotal = List.map((v) => v.NurseCount);
  } else {
    List.forEach((o) => {
      const sum = o.reduce((total: number, item: any) => total + parseInt(item.VisitCount), 0);
      Total.push(sum);
      const sum1 = o.reduce((total: number, item: any) => total + parseInt(item.ConsultCount), 0);
      WZTotal.push(sum1);
      const sum2 = o.reduce((total: number, item: any) => total + parseInt(item.TreatmentCount), 0);
      ZXTotal.push(sum2);
      const sum3 = o.reduce((total: number, item: any) => total + parseInt(item.NurseCount), 0);
      HLTotal.push(sum3);
    });
  }

  try {
    Chart4!.setOption({
      title: {
        text: "就诊",
        textStyle: {
          fontSize: 18,
        },
      },
      tooltip: {
        trigger: "axis",
      },
      legend: {
        data: ["总数", "问诊", "咨询", "护理"],
      },
      grid: {
        left: "3%",
        right: "4%",
        bottom: "15%",
        containLabel: true,
      },
      toolbox: {
        feature: {
          saveAsImage: {},
        },
      },
      dataZoom: [
        {
          type: "slider",
          show: true,
          xAxisIndex: [0],
          start: Math.max(0, ((X.length - 20) / X.length) * 100),
          end: 100,
          bottom: 10,
        },
      ],
      xAxis: {
        type: "category",
        boundaryGap: false,
        data: X,
        axisLabel: {
          rotate: 45,
          interval: 0,
        },
      },
      yAxis: {
        type: "value",
        minInterval: 1,
      },
      series: [
        {
          name: "总数",
          type: "line",
          data: Total,
        },
        {
          name: "问诊",
          type: "line",
          data: WZTotal,
        },
        {
          name: "咨询",
          type: "line",
          data: ZXTotal,
        },
        {
          name: "护理",
          type: "line",
          data: HLTotal,
        },
      ],
    });
  } catch (error) {
    console.error("Error setting options for Chart4:", error);
  }
};

const updateChart5 = (X: string[], List: any[], radio: string) => {
  if (!Chart5) {
    console.error("Chart5 is not initialized");
    return;
  }

  let Total = [];
  let ExecutedTotal = [];
  if (radio === "Day") {
    Total = List.map((v) => v.PrescriptionCount);
    ExecutedTotal = List.map((v) => v.ExecutePrescriptionCount);
  } else {
    List.forEach((o) => {
      const sum = o.reduce(
        (total: number, item: any) => total + parseInt(item.PrescriptionCount),
        0
      );
      Total.push(sum);
      const sum1 = o.reduce(
        (total: number, item: any) => total + parseInt(item.ExecutePrescriptionCount),
        0
      );
      ExecutedTotal.push(sum1);
    });
  }

  try {
    Chart5!.setOption({
      title: {
        text: "方案数量",
        textStyle: {
          fontSize: 18,
        },
      },
      tooltip: {
        trigger: "axis",
      },
      legend: {
        data: ["总数", "已执行"],
      },
      grid: {
        left: "3%",
        right: "4%",
        bottom: "15%",
        containLabel: true,
      },
      toolbox: {
        feature: {
          saveAsImage: {},
        },
      },
      dataZoom: [
        {
          type: "slider",
          show: true,
          xAxisIndex: [0],
          start: Math.max(0, ((X.length - 20) / X.length) * 100),
          end: 100,
          bottom: 10,
        },
      ],
      xAxis: {
        type: "category",
        boundaryGap: false,
        data: X,
        axisLabel: {
          rotate: 45,
          interval: 0,
        },
      },
      yAxis: {
        type: "value",
        minInterval: 1,
      },
      series: [
        {
          name: "总数",
          type: "line",
          data: Total,
        },
        {
          name: "已执行",
          type: "line",
          data: ExecutedTotal,
        },
      ],
    });
  } catch (error) {
    console.error("Error setting options for Chart5:", error);
  }
};

const updateChart6 = (X: string[], List: any[], radio: string) => {
  if (!Chart6) {
    console.error("Chart6 is not initialized");
    return;
  }

  let WZ = [];
  let ZX = [];
  let ZL = [];
  if (radio === "Day") {
    WZ = List.map((v) => v.RegisterDoctorConsultAmount);
    ZX = List.map((v) => v.RegisterTherapistConsultAmount);
    ZL = List.map((v) => v.TreatmentAmount);
  } else {
    List.forEach((o) => {
      const sum = o.reduce(
        (total: number, item: any) => total + parseInt(item.RegisterDoctorConsultAmount),
        0
      );
      WZ.push(sum);
      const sum1 = o.reduce(
        (total: number, item: any) => total + parseInt(item.RegisterTherapistConsultAmount),
        0
      );
      ZX.push(sum1);
      const sum2 = o.reduce(
        (total: number, item: any) => total + parseInt(item.TreatmentAmount),
        0
      );
      ZL.push(sum2);
    });
  }

  try {
    Chart6!.setOption({
      title: {
        text: "收入",
        textStyle: {
          fontSize: 18,
        },
      },
      tooltip: {
        trigger: "axis",
      },
      legend: {
        data: ["问诊", "咨询", "治疗"],
      },
      grid: {
        left: "3%",
        right: "4%",
        bottom: "15%",
        containLabel: true,
      },
      toolbox: {
        feature: {
          saveAsImage: {},
        },
      },
      dataZoom: [
        {
          type: "slider",
          show: true,
          xAxisIndex: [0],
          start: Math.max(0, ((X.length - 20) / X.length) * 100),
          end: 100,
          bottom: 10,
        },
      ],
      xAxis: {
        type: "category",
        boundaryGap: false,
        data: X,
        axisLabel: {
          rotate: 45,
          interval: 0,
        },
      },
      yAxis: {
        type: "value",
      },
      series: [
        {
          name: "问诊",
          type: "line",
          data: WZ,
        },
        {
          name: "咨询",
          type: "line",
          data: ZX,
        },
        {
          name: "治疗",
          type: "line",
          data: ZL,
        },
      ],
    });
  } catch (error) {
    console.error("Error setting options for Chart6:", error);
  }
};

const handleResizeWithDebounce = useDebounceFn(() => {
  Chart1?.resize({
    animation: { duration: 300 },
  });
  Chart2?.resize({
    animation: { duration: 300 },
  });
  Chart3?.resize();
  Chart4?.resize();
  Chart5?.resize();
  Chart6?.resize();
}, 300);

const dashboardContainerRef = useTemplateRef<HTMLElement>("dashboardContainerRef");
useResizeObserver(dashboardContainerRef, handleResizeWithDebounce);

onMounted(() => {
  initCharts();
  loadTopDataInfo();
  handleRefreshData();
});

onUnmounted(() => {
  // 组件卸载时清理所有图表实例
  Chart1?.dispose();
  Chart2?.dispose();
  Chart3?.dispose();
  Chart4?.dispose();
  Chart5?.dispose();
  Chart6?.dispose();

  // 清空引用
  Chart1 = undefined;
  Chart2 = undefined;
  Chart3 = undefined;
  Chart4 = undefined;
  Chart5 = undefined;
  Chart6 = undefined;
});
</script>

<style scoped lang="scss">
.dashboard-container {
  $space: 24px;

  min-width: 500px;
  min-height: 100vh;
  padding: $space;
  padding-bottom: 0;
  overflow: auto;
  background-color: #f5f7fa;

  .overview-section {
    margin-bottom: $space;

    .overview-cards {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: $space;
    }

    .overview-card {
      display: flex;
      gap: 12px;
      align-items: center;
      padding: 16px;
      background: white;
      border: 1px solid rgb(0 0 0 / 5%);
      border-radius: 8px;
      box-shadow: 0 1px 4px rgb(0 0 0 / 5%);
      transition: all 0.2s ease;

      &:hover {
        box-shadow: 0 4px 12px rgb(0 0 0 / 8%);
        transform: translateY(-2px);
      }

      .card-icon {
        flex-shrink: 0;
        width: 40px;
        height: 40px;
        padding: 10px;
        overflow: hidden;
        border-radius: 8px;

        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }
      }

      &:nth-child(1) .card-icon {
        background: #1890ff;
      }

      &:nth-child(2) .card-icon {
        background: #52c41a;
      }

      &:nth-child(3) .card-icon {
        background: #faad14;
      }

      &:nth-child(4) .card-icon {
        background: #2ed1d1;
      }

      &:nth-child(5) .card-icon {
        background: #ff4d4f;
      }

      .card-content {
        flex: 1;
        min-width: 0;

        .card-value {
          margin-bottom: 2px;
          font-size: 20px;
          font-weight: 600;
          line-height: 1.2;
          color: #1a1a1a;

          .number {
            color: #31ccb8;
          }
        }

        .card-label {
          font-size: 13px;
          line-height: 1.2;
          color: #666;
        }
      }
    }
  }

  .charts-todo-section {
    display: flex;
    gap: $space;
    margin-bottom: $space;

    @media (width <= 1500px) {
      flex-direction: column;
      align-items: stretch;
    }

    .charts-section {
      display: flex;
      flex: 1;
      gap: $space;

      @media (width <= 1200px) {
        flex-direction: column;
      }
    }

    .todo-section {
      flex-shrink: 0;
      width: 320px;

      @media (width <= 1500px) {
        width: 100%;
      }

      .todo-card {
        display: flex;
        flex-direction: column;
        height: 100%;
        padding: 0 16px;
        background: white;
        border: 1px solid rgb(0 0 0 / 5%);
        border-radius: 8px;
        box-shadow: 0 1px 4px rgb(0 0 0 / 5%);

        .todo-header {
          border-bottom: 1px solid #f0f0f0;

          h3 {
            margin: 10px 0 !important;
            font-size: 16px;
            font-weight: 600;
            color: #1a1a1a;
          }
        }

        .todo-content {
          display: grid;
          flex: 1;
          grid-template-columns: repeat(2, 1fr);
          gap: 8px;
          padding: 8px 0;

          .todo-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            padding: 8px;
            font-size: 13px;
            text-align: center;
            cursor: pointer;
            background: #f8f9fa;
            border-radius: 6px;
            transition: all 0.2s ease;

            &:hover {
              color: #409eff;
              background: #f0f7ff;
            }

            .todo-label {
              margin-bottom: 4px;
              overflow: hidden;
              text-overflow: ellipsis;
              font-size: 16px;
              white-space: nowrap;
            }

            .todo-count {
              min-width: 24px;
              padding: 2px 8px;
              font-size: 16px;
              font-weight: 500;
              color: #ff4d4f;
              text-align: center;
              background: #fff1f0;
              border-radius: 10px;
            }
          }
        }
      }
    }
  }

  .stats-section {
    padding: $space;
    margin-bottom: $space;
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 12px rgb(0 0 0 / 4%);

    .stats-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 24px;

      h3 {
        font-size: 18px;
        font-weight: 600;
        color: #1a1a1a;
      }

      .stats-filters {
        display: flex;
        gap: 16px;
        align-items: center;

        .date-picker {
          width: 160px;
        }

        .confirm-btn {
          margin-left: 16px;
        }
      }
    }

    .stats-cards {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
      gap: 24px;

      .stat-card {
        position: relative;
        padding: 16px;
        overflow: hidden;
        border-radius: 8px;
        transition: all 0.3s ease;

        &:nth-child(1) {
          background: linear-gradient(135deg, #e6f7ff 0%, #f0f7ff 100%);
          border: 1px solid rgb(24 144 255 / 10%);
        }

        &:nth-child(2) {
          background: linear-gradient(135deg, #f6ffed 0%, #f0fff0 100%);
          border: 1px solid rgb(82 196 26 / 10%);
        }

        &:nth-child(3) {
          background: linear-gradient(135deg, #fff7e6 0%, #fff9f0 100%);
          border: 1px solid rgb(250 173 20 / 10%);
        }

        &:nth-child(4) {
          background: linear-gradient(135deg, #f9f0ff 0%, #f5f0ff 100%);
          border: 1px solid rgb(114 46 209 / 10%);
        }

        &:nth-child(5) {
          background: linear-gradient(135deg, #fff1f0 0%, #fff5f5 100%);
          border: 1px solid rgb(255 77 79 / 10%);
        }

        &:nth-child(6) {
          background: linear-gradient(135deg, #f0f5ff 0%, #f5f7ff 100%);
          border: 1px solid rgb(47 84 235 / 10%);
        }

        &:nth-child(7) {
          background: linear-gradient(135deg, #e6fffb 0%, #f0ffff 100%);
          border: 1px solid rgb(24 144 255 / 10%);
        }

        &:hover {
          box-shadow: 0 4px 12px rgb(0 0 0 / 8%);
          transform: translateY(-2px);
        }

        .stat-card-header {
          position: relative;
          z-index: 1;
          text-align: center;

          h4 {
            margin: 0;
            font-size: 16px;
            font-weight: 600;
            color: #1a1a1a;
          }
        }

        .stat-card-content {
          position: relative;
          z-index: 1;
          text-align: center;

          .stat-row {
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 4px 0;

            &:last-child {
              margin-bottom: 0;
            }

            .stat-label {
              font-size: 14px;
              color: #666;
            }

            .stat-value {
              margin-left: 12px;
              font-size: 14px;
              font-weight: 500;
              color: #1a1a1a;
            }

            // 总数行样式
            &.total-row {
              flex-direction: column;
              justify-content: center;
              border-radius: 8px;

              .stat-label {
                font-size: 20px;
                font-weight: 600;
                color: #333;
              }

              .stat-value {
                font-size: 28px;
                font-weight: 800;
                line-height: 1.2;
                color: #1a1a1a;
                letter-spacing: 0.5px;
                text-shadow: 0 1px 2px rgb(0 0 0 / 10%);
              }
            }
          }
        }

        &::before {
          position: absolute;
          top: 0;
          right: 0;
          width: 100px;
          height: 100px;
          content: "";
          background: radial-gradient(circle at top right, rgb(255 255 255 / 80%), transparent 70%);
          opacity: 0.5;
        }
      }
    }
  }

  .chart-card {
    height: 500px;
    padding: $space;
    margin-bottom: $space;
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 12px rgb(0 0 0 / 4%);
  }

  .chart-card-top {
    flex: 1;
    height: 350px;
    margin-bottom: 0;

    @media (width <= 1200px) {
      // height: 280px;
      flex: unset;
    }
  }
}
</style>
