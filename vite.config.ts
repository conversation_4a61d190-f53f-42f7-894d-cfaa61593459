import vue from "@vitejs/plugin-vue";
import { loadEnv, defineConfig } from "vite";
import AutoImport from "unplugin-auto-import/vite";
import Components from "unplugin-vue-components/vite";
import { ElementPlusResolver } from "unplugin-vue-components/resolvers";
import vueJsx from "@vitejs/plugin-vue-jsx";

import UnoCSS from "unocss/vite";
import { resolve } from "path";
import { name, version, engines, dependencies, devDependencies } from "./package.json";
import fs from "fs";
import { visualizer } from "rollup-plugin-visualizer";
import Inspect from "vite-plugin-inspect";

// console.log("process.env:", process.env);
// const isDebug = process.env.NODE_ENV == "development";

// 平台的名称、版本、运行所需的 node 版本、依赖、构建时间的类型提示
const __APP_INFO__ = {
  pkg: { name, version, engines, dependencies, devDependencies },
  buildTimestamp: Date.now(),
};

const pathSrc = resolve(__dirname, "src");

const cacheChunkNames: string[] = [];

// Vite配置  https://cn.vitejs.dev/config
export default defineConfig(({ mode }) => {
  const env = loadEnv(mode, process.cwd());
  // console.log("env:", mode);

  const apiProxyRewriteReg = new RegExp(`^${env.VITE_PROXY_API}`);
  const ossProxyRewriteReg = new RegExp(`^${env.VITE_PROXY_OSS}`);
  const wsProxyRewriteReg = new RegExp(`^${env.VITE_PROXY_WS}`);

  return {
    resolve: {
      alias: {
        "@": pathSrc,
      },
    },
    css: {
      preprocessorOptions: {
        // 定义全局 SCSS 变量
        scss: {
          api: "modern-compiler",
          additionalData: `
            @use "@/styles/variables.scss" as *;
          `,
        },
      },
    },
    server: {
      host: env.VITE_DEV_HOST,
      port: +env.VITE_DEV_PORT,
      https:
        env.VITE_DEV_HTTPS === "true"
          ? {
              key: fs.readFileSync("./certs/server.key"),
              cert: fs.readFileSync("./certs/server.crt"),
            }
          : undefined,
      cors: true,
      open: true,
      proxy: {
        [env.VITE_PROXY_API]: {
          target: env.VITE_APP_API_URL,
          changeOrigin: true,
          rewrite: (path) => path.replace(apiProxyRewriteReg, ""),
        },
        [env.VITE_PROXY_WS]: {
          target: env.VITE_APP_WS_URL,
          changeOrigin: true,
          ws: true,
          rewrite: (path) => path.replace(wsProxyRewriteReg, ""),
        },
        [env.VITE_PROXY_OSS]: {
          target: env.VITE_APP_UPLOAD_URL,
          changeOrigin: true,
          rewrite: (path) => path.replace(ossProxyRewriteReg, ""),
        },
      },
    },
    plugins: [
      vue(),
      vueJsx(), // 启用 jsx 支持
      UnoCSS(),
      // 自动导入配置 https://github.com/sxzz/element-plus-best-practices/blob/main/vite.config.ts
      AutoImport({
        // 导入 Vue 函数，如：ref, reactive, toRef 等
        imports: ["vue", "@vueuse/core", "pinia", "vue-router", "vue-i18n"],
        resolvers: [
          // 导入 Element Plus函数，如：ElMessage, ElMessageBox 等
          ElementPlusResolver(),
        ],
        eslintrc: {
          enabled: false,
          filepath: "./.eslintrc-auto-import.json",
          globalsPropValue: true,
        },
        vueTemplate: true,
        // 导入函数类型声明文件路径 (false:关闭自动生成)
        dts: "src/types/auto-imports.d.ts",
      }),
      Components({
        resolvers: [
          // 导入 Element Plus 组件
          ElementPlusResolver(),
        ],
        // 指定自定义组件位置(默认:src/components)
        dirs: ["src/components"],
        // 导入组件类型声明文件路径 (false:关闭自动生成)
        dts: "src/types/components.d.ts",
      }),
      // 打包体积分析插件 - 仅在分析模式下启用
      ...(process.env.ANALYZE === "true"
        ? [
            visualizer({
              filename: "dist/stats.html", // 分析报告输出路径
              open: true, // 构建完成后自动打开报告
              gzipSize: true, // 显示gzip压缩后的大小
              brotliSize: true, // 显示brotli压缩后的大小
              template: "treemap", // 使用树状图模板
            }),
          ]
        : []),
      // Vite构建过程分析插件 - 仅在开发模式下启用
      ...(process.env.INSPECT === "true" && mode === "development" ? [Inspect()] : []),
    ],
    // 预加载项目必需的组件
    optimizeDeps: {
      include: [
        "vue",
        "vue-router",
        "element-plus",
        "pinia",
        "axios",
        "@vueuse/core",
        "sortablejs",
        "exceljs",
        "path-to-regexp",
        "echarts",
        "vue-i18n",
        "nprogress",
        "qs",
        "path-browserify",
        "@element-plus/icons-vue",
        "element-plus/es/locale/lang/zh-cn",
        "element-plus/es/locale/lang/en",
        "element-plus/es/components/form/style/css",
        "element-plus/es/components/form-item/style/css",
        "element-plus/es/components/button/style/css",
        "element-plus/es/components/input/style/css",
        "element-plus/es/components/input-number/style/css",
        "element-plus/es/components/switch/style/css",
        "element-plus/es/components/upload/style/css",
        "element-plus/es/components/menu/style/css",
        "element-plus/es/components/col/style/css",
        "element-plus/es/components/icon/style/css",
        "element-plus/es/components/row/style/css",
        "element-plus/es/components/tag/style/css",
        "element-plus/es/components/dialog/style/css",
        "element-plus/es/components/loading/style/css",
        "element-plus/es/components/radio/style/css",
        "element-plus/es/components/radio-group/style/css",
        "element-plus/es/components/popover/style/css",
        "element-plus/es/components/scrollbar/style/css",
        "element-plus/es/components/tooltip/style/css",
        "element-plus/es/components/dropdown/style/css",
        "element-plus/es/components/dropdown-menu/style/css",
        "element-plus/es/components/dropdown-item/style/css",
        "element-plus/es/components/sub-menu/style/css",
        "element-plus/es/components/menu-item/style/css",
        "element-plus/es/components/divider/style/css",
        "element-plus/es/components/card/style/css",
        "element-plus/es/components/link/style/css",
        "element-plus/es/components/breadcrumb/style/css",
        "element-plus/es/components/breadcrumb-item/style/css",
        "element-plus/es/components/table/style/css",
        "element-plus/es/components/tree-select/style/css",
        "element-plus/es/components/table-column/style/css",
        "element-plus/es/components/select/style/css",
        "element-plus/es/components/option/style/css",
        "element-plus/es/components/pagination/style/css",
        "element-plus/es/components/tree/style/css",
        "element-plus/es/components/alert/style/css",
        "element-plus/es/components/radio-button/style/css",
        "element-plus/es/components/checkbox-group/style/css",
        "element-plus/es/components/checkbox/style/css",
        "element-plus/es/components/tabs/style/css",
        "element-plus/es/components/tab-pane/style/css",
        "element-plus/es/components/rate/style/css",
        "element-plus/es/components/date-picker/style/css",
        "element-plus/es/components/notification/style/css",
        "element-plus/es/components/image/style/css",
        "element-plus/es/components/statistic/style/css",
        "element-plus/es/components/watermark/style/css",
        "element-plus/es/components/config-provider/style/css",
        "element-plus/es/components/text/style/css",
        "element-plus/es/components/drawer/style/css",
        "element-plus/es/components/color-picker/style/css",
        "element-plus/es/components/backtop/style/css",
        "element-plus/es/components/message-box/style/css",
        "element-plus/es/components/skeleton/style/css",
        "element-plus/es/components/skeleton/style/css",
        "element-plus/es/components/skeleton-item/style/css",
        "element-plus/es/components/badge/style/css",
        "element-plus/es/components/steps/style/css",
        "element-plus/es/components/step/style/css",
        "element-plus/es/components/avatar/style/css",
        "element-plus/es/components/descriptions/style/css",
        "element-plus/es/components/descriptions-item/style/css",
        "element-plus/es/components/checkbox-group/style/css",
        "element-plus/es/components/progress/style/css",
        "element-plus/es/components/image-viewer/style/css",
        "element-plus/es/components/empty/style/css",
        "element-plus/es/components/message/style/css",
        "element-plus/es/components/cascader/style/css",
        "element-plus/es/components/cascader-panel/style/css",
      ],
    },
    // 构建配置
    build: {
      chunkSizeWarningLimit: 2000, // 消除打包大小超过500kb警告
      minify: "terser", // Vite 2.6.x 以上需要配置 minify: "terser", terserOptions 才能生效
      terserOptions: {
        compress: {
          keep_infinity: true, // 防止 Infinity 被压缩成 1/0，这可能会导致 Chrome 上的性能问题
          drop_console: true, // 生产环境去除 console
          drop_debugger: true, // 生产环境去除 debugger
        },
        format: {
          comments: true, // 删除注释
        },
      },
      rollupOptions: {
        output: {
          dir: "dist",
          compact: true,
          manualChunks: (id: string) => {
            // console.log("id:", id);
            const match = /^.*node_modules.*node_modules\/(.*?)\/.*/.exec(id);
            if (match) {
              const chunkName = match[1];
              if (!cacheChunkNames.includes(chunkName)) {
                cacheChunkNames.push(chunkName);
                // console.log("chunkName:", chunkName);
              }
              if (["pinyin", "echarts"].includes(chunkName)) {
                return chunkName;
              }

              const chunkNames = [
                "vue",
                // "default-passive-events",
                "element-plus",
                "@vue",
                "@element-plus",
                "vue-router",
                "pinia",
                "vue-i18n",
                "dayjs",
                "nprogress",
                // "pinyin",
                "decimal.js",
                "@vueuse",
                // "@sxzz",
                "lodash-unified",
                // "normalize-wheel-es",
                // "@floating-ui",
                "lodash-es",
                // "async-validator",
                "vue-demi",
                // "@ctrl",
                // "@intlify",
                // "perfect-debounce",
                // "hookable",
                // "birpc",
                // "memoize-one",
                "axios",
                "qs",
                "crypto-js",
                // "side-channel",
                // "es-errors",
                // "object-inspect",
                // "side-channel-list",
                // "side-channel-map",
                // "side-channel-weakmap",
                // "get-intrinsic",
                // "call-bound",
                // "call-bind-apply-helpers",
                // "es-object-atoms",
                // "math-intrinsics",
                // "gopd",
                // "es-define-property",
                // "has-symbols",
                // "get-proto",
                // "function-bind",
                // "hasown",
                // "dunder-proto",
                // "echarts",
                // "zrender",
                // "tslib",
                // "vue-json-pretty",
                // "sortablejs",
                // "path-browserify",
                // "@wangeditor-next",
                // "qrcode.vue",
                // "path-to-regexp",
              ];
              if (chunkNames.includes(chunkName)) {
                return "chunk-vendors";
              }
              // return "other-vendors";
              return null;
            }
            return null;
          },
          // manualChunks: {
          //   "vue-i18n": ["vue-i18n"],
          // },
          // 用于从入口点创建的块的打包输出格式[name]表示文件名,[hash]表示该文件内容hash值
          entryFileNames: "js/app.[hash].js",
          // 用于命名代码拆分时创建的共享块的输出命名
          chunkFileNames: "js/[hash].js",
          // chunkFileNames: "js/[name].[hash].js",
          // 用于输出静态资源的命名，[ext]表示文件扩展名
          assetFileNames: (assetInfo: any) => {
            const info = assetInfo.name.split(".");
            let extType = info[info.length - 1];
            // console.log('文件信息', assetInfo.name)
            if (/\.(mp4|webm|ogg|mp3|wav|flac|aac)(\?.*)?$/i.test(assetInfo.name)) {
              extType = "media";
            } else if (/\.(png|jpe?g|gif|svg)(\?.*)?$/.test(assetInfo.name)) {
              extType = "img";
            } else if (/\.(woff2?|eot|ttf|otf)(\?.*)?$/i.test(assetInfo.name)) {
              extType = "fonts";
            }
            return `${extType}/[hash].[ext]`;
          },
        },
      },
    },
    define: {
      __APP_INFO__: JSON.stringify(__APP_INFO__),
    },
  };
});
