{"name": "platform-operation", "version": "0.0.1", "private": true, "type": "module", "scripts": {"dev1": "NODE_ENV=production vite --mode debug", "dev": "pnpm i && vite", "stg": "pnpm i && vite --mode stg", "pro": "pnpm i && vite --mode pro", "dev:inspect": "cross-env INSPECT=true vite", "build:dev": "vite build", "build:stg": "vite build --mode stg", "build:pro": "vite build --mode pro", "build:analyze": "cross-env ANALYZE=true vite build", "build:analyze:dev": "cross-env ANALYZE=true vite build", "build:analyze:stg": "cross-env ANALYZE=true vite build --mode stg", "build:analyze:pro": "cross-env ANALYZE=true vite build --mode pro", "preview": "vite preview", "build-only": "vite build", "type-check": "vue-tsc --noEmit", "lint:eslint": "eslint --fix ./src", "lint:prettier": "prettier --write \"**/*.{js,cjs,ts,json,tsx,css,less,scss,vue,html,md}\"", "lint:stylelint": "stylelint  \"**/*.{css,scss,vue}\" --fix", "lint:lint-staged": "lint-staged", "preinstall": "npx only-allow pnpm", "prepare": "husky"}, "config": {"commitizen": {"path": "node_modules/cz-git"}}, "lint-staged": {"*.{js,ts}": ["eslint --fix", "prettier --write"], "*.{cjs,json}": ["prettier --write"], "*.{vue,html}": ["eslint --fix", "prettier --write", "stylelint --fix"], "*.{scss,css}": ["stylelint --fix", "prettier --write"], "*.md": ["prettier --write"]}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "@stomp/stompjs": "^7.1.1", "@types/crypto-js": "^4.2.2", "@vitejs/plugin-vue-jsx": "^4.2.0", "@vueuse/core": "^13.5.0", "@wangeditor-next/editor": "^5.6.35", "@wangeditor-next/editor-for-vue": "^5.1.14", "axios": "^1.10.0", "codemirror": "^5.65.19", "codemirror-editor-vue3": "^2.8.0", "crypto-js": "^4.2.0", "dayjs": "^1.11.13", "decimal.js": "^10.5.0", "default-passive-events": "^2.0.0", "echarts": "^5.6.0", "element-plus": "^2.10.2", "exceljs": "^4.4.0", "html2canvas": "^1.4.1", "jspdf": "^3.0.1", "lodash-es": "^4.17.21", "nprogress": "^0.2.0", "papaparse": "^5.5.3", "path-browserify": "^1.0.1", "path-to-regexp": "^8.2.0", "pinia": "^3.0.3", "pinyin": "^4.0.0", "qrcode.vue": "^3.6.0", "qs": "^6.14.0", "segmentit": "^2.0.3", "sortablejs": "^1.15.6", "vue": "^3.5.17", "vue-i18n": "^11.1.8", "vue-json-pretty": "^2.5.0", "vue-router": "^4.5.1"}, "devDependencies": {"@commitlint/cli": "^19.8.1", "@commitlint/config-conventional": "^19.8.1", "@eslint/js": "^9.30.1", "@iconify/utils": "^2.3.0", "@types/codemirror": "^5.60.16", "@types/lodash-es": "^4.17.12", "@types/node": "^22.16.0", "@types/nprogress": "^0.2.3", "@types/papaparse": "^5.3.16", "@types/path-browserify": "^1.0.3", "@types/qs": "^6.14.0", "@types/sortablejs": "^1.15.8", "@typescript-eslint/eslint-plugin": "^8.35.1", "@typescript-eslint/parser": "^8.35.1", "@vitejs/plugin-vue": "^5.2.4", "autoprefixer": "^10.4.21", "commitizen": "^4.3.1", "cross-env": "^7.0.3", "cz-git": "^1.11.2", "eslint": "^9.30.1", "eslint-config-prettier": "^10.1.5", "eslint-plugin-prettier": "^5.5.1", "eslint-plugin-vue": "^9.33.0", "globals": "^16.3.0", "husky": "^9.1.7", "lint-staged": "^15.5.2", "postcss": "^8.5.6", "postcss-html": "^1.8.0", "postcss-scss": "^4.0.9", "prettier": "^3.6.2", "rollup-plugin-visualizer": "^6.0.3", "sass": "^1.89.2", "stylelint": "^16.21.0", "stylelint-config-html": "^1.1.0", "stylelint-config-recess-order": "^6.1.0", "stylelint-config-recommended-scss": "^14.1.0", "stylelint-config-recommended-vue": "^1.6.1", "stylelint-config-standard": "^37.0.0", "terser": "^5.43.1", "typescript": "^5.8.3", "typescript-eslint": "^8.35.1", "unocss": "65.4.3", "unplugin-auto-import": "^19.3.0", "unplugin-vue-components": "^28.8.0", "vite": "^6.3.5", "vite-bundle-analyzer": "^1.1.0", "vite-plugin-inspect": "^11.3.0", "vite-plugin-mock-dev-server": "^1.9.1", "vue-eslint-parser": "^9.4.3", "vue-tsc": "^2.2.12"}, "engines": {"node": ">=18.0.0"}, "author": "成都康复行网络科技有限公司", "packageManager": "pnpm@9.15.3+sha512.1f79bc245a66eb0b07c5d4d83131240774642caaa86ef7d0434ab47c0d16f66b04e21e0c086eb61e62c77efc4d7f7ec071afad3796af64892fae66509173893a"}